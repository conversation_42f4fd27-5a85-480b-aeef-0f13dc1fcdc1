package proxy

import (
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage"
	"sync"
)

var (
	once          sync.Once
	instance      *proxyStorage
	storageByName = map[string]func() storage.Storage{
	}
)

func RegisterStorage(name string, storage storage.Storage) error {
	return getProxyStorage().Register(name, storage)
}

func GetStorage() storage.Storage {
	return getProxyStorage()
}
