package proxy

import (
	"context"
	entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"os"
	"strings"
	"sync"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
)

const (
	SEPARATOR        = ":"
	SEPARATOR_SCHEMA = "||"
	DefaultSchema    = "morpheus"
)

/* proxyStorage通过uri前的schema来选择已注册的Storage实例， 发送给Storage实例的uri中， 将不再提供schema, 如果需要schema，
   那么需要Storage实例自行拼接<schema>:
*/
type proxyStorage struct {
	storageByName map[string]storage.Storage
	mutex         sync.Mutex
}

func getProxyStorage() *proxyStorage {
	once.Do(func() {
		instance = NewProxyStorage()
		for name, generator := range storageByName {
			if err := instance.Register(name, generator()); err != nil {
				log.Errorf("failed to register storage, name: %v, err: %v", name, err)
			}
		}
	})
	return instance
}

func NewProxyStorage() *proxyStorage {
	object := &proxyStorage{storageByName: make(map[string]storage.Storage)}
	return object
}

// 这里没有给SplitSchema设置接收器(proxyStorage)， 是因为该函数还会被具体的storage继续使用， 但proxyStorage对外是不可见的，
// 所以storage的pakcage下， 开放了这个函数
func SplitSchema(uri string) (string, string, error) {
	fields := strings.SplitN(uri, SEPARATOR, 2)
	if len(fields) == 2 {
		if _, ok := getProxyStorage().storageByName[fields[0]]; ok {
			return fields[0], fields[1], nil
		} else {
			return "", "", status.Errorf(codes.InvalidArgument, "unknown schema: %v", fields[0])
		}
	}

	if len(fields) == 1 {
		// uri中不存在分隔符， 这些如果uri与已经注册的schema一致， 那么就认为是schema
		// 如果uri与已经注册的schema不同， 那么认为是默认schema, 如果默认schema不存在， 返回错误
		if _, ok := getProxyStorage().storageByName[fields[0]]; ok {
			return fields[0], "", nil
		}

		if _, ok := getProxyStorage().storageByName[DefaultSchema]; ok {
			return DefaultSchema, fields[0], nil
		}
		return "", "", status.Errorf(codes.InvalidArgument, "unknown uri: %v", uri)
	}
	return "", "", status.Errorf(codes.InvalidArgument, "bad uri: %v", uri)
}

func (s *proxyStorage) Download(ctx context.Context, input util.DownloadInput) (*sdk.DownloadInfo, error) {
	//t.FetchUri = TaskSubst(t, t.GetFetchUri(), "") TODO 可以再想想，暂时先放到scan.go中调Download的地方
	storage, _, err := s.getStorage(input.Uri)
	if err != nil {
		return nil, err
	}
	return storage.Download(ctx, input)
}

func chooseStoreUriSchema(t *entities.Task, filepath string) {
	fields := strings.Split(t.GetStoreUri(), SEPARATOR_SCHEMA)
	for i, _ := range fields {
		if schemaUsable(fields[i], filepath) {
			t.StoreUri = fields[i]
			return
		}
	}
}

func schemaUsable(uri string, filepath string) bool {
	var err error
	if strings.Contains(uri, "message") {
		fi, err := os.Stat(filepath)
		if err != nil || fi.Size() > 200 {
			return false
		}
	} else {
		_, _, err = SplitSchema(uri)
	}
	if err != nil {
		return false
	}
	return true
}

// 例如：message||files/3e3c3c2fd417ed5dd39b06f3a145b0712164f31b/logs/qowl/storage
// 按照||分割，判断扫描结果文件的size是否大于200，如果大于则：不能走message
// 而是走：files/3e3c3c2fd417ed5dd39b06f3a145b0712164f31b/logs/qowl/storage
// 即：将store_uri赋值为：files/3e3c3c2fd417ed5dd39b06f3a145b0712164f31b/logs/qowl/storage存入s3
// 按照||切分判断之后若还存在message，那么确定走message，返回为true
func ChooseStorage(t *entities.Task, filepath string) bool {
	chooseStoreUriSchema(t, filepath)
	if strings.Contains(t.GetStoreUri(), "message") {
		return true
	}
	return false
}

func (s *proxyStorage) Upload(ctx context.Context, t *entities.Task, filepath string) (*sdk.UploadInfo, error) {
	//t.StoreUri = TaskSubst(t, t.GetStoreUri(), filepath) TODO 可以再想想，暂时先放到scan.go中调Upload的地方
	chooseStoreUriSchema(t, filepath)
	log.Debugf("store_uri after choose is : %v", t.GetStoreUri())
	storage, _, err := s.getStorage(t.GetStoreUri())
	if err != nil {
		return nil, err
	}
	return storage.Upload(ctx, t, filepath)
}

func (s *proxyStorage) Register(name string, storage storage.Storage) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	if _, ok := s.storageByName[name]; ok {
		return status.Errorf(codes.AlreadyExists, "storage %v already exists", name)
	}
	s.storageByName[name] = storage
	return nil
}

func (s *proxyStorage) getStorage(uri string) (storage.Storage, string, error) {
	schema, value, err := SplitSchema(uri)
	if err != nil {
		return nil, "", err
	}
	storage, ok := s.storageByName[schema]
	if !ok {
		return nil, "", status.Errorf(codes.NotFound, "storage %v not registered", schema)
	}

	return storage, value, nil
}
