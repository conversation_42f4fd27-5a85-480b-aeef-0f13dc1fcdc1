package adapter

import (
	"context"
	"strings"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func NewStorageAdapter(fs file.FileStorage) storage.Storage {
	return &StorageAdapter{fileStorage: fs}
}

const (
	MB              = 1024 * 1024
	GB              = 1024 * MB
	CONNECT_REFUSED = "connection refused"
)

type StorageAdapter struct {
	fileStorage file.FileStorage
}

// TODO API 修改为：Download(ctx context.Context, in util.DownloadInput) (*sdk.DownloadInfo, error)
func (s *StorageAdapter) Download(ctx context.Context, in util.DownloadInput) (*sdk.DownloadInfo, error) {
	var err error
	var downloadInfo sdk.DownloadInfo
	var retryDownloadInfo *sdk.DownloadInfo
	// uri如果是空的话, 使用sha1进行下载
	if in.Uri == "" {
		in.Uri = in.Sha1
	}
	for i := 0; i < 3; i++ {
		retryDownloadInfo, err = s.fileStorage.Download(ctx, in)

		if retryDownloadInfo != nil {
			downloadInfo.Source = retryDownloadInfo.Source
			downloadInfo.GrpcDuration += retryDownloadInfo.GrpcDuration
			downloadInfo.DownloadFileDuration += retryDownloadInfo.DownloadFileDuration
			downloadInfo.GrpcErr = retryDownloadInfo.GrpcErr
			downloadInfo.DownloadFileErr = retryDownloadInfo.DownloadFileErr
			downloadInfo.RetryCount = retryDownloadInfo.RetryCount
			downloadInfo.Length = retryDownloadInfo.Length
			downloadInfo.Cancel = retryDownloadInfo.Cancel
			downloadInfo.Body = retryDownloadInfo.Body
			downloadInfo.Sha1Same = retryDownloadInfo.Sha1Same
			downloadInfo.ActualSha1 = retryDownloadInfo.ActualSha1
		}
		if err != nil {
			// 说明sha1不一致了
			if status.Code(err) == codes.InvalidArgument {
				continue
			}
			// 连接有问题重试
			if strings.Contains(err.Error(), CONNECT_REFUSED) {
				log.Warnf("download file failed, retry %v times, err: %v", i, err)
				continue
			}
			return &downloadInfo, err
		} else {
			return &downloadInfo, nil
		}
	}
	return &downloadInfo, status.Errorf(codes.InvalidArgument,
		"failed to download file, (unzip )content sha1: %v not equal workload.sha1: %v",
		downloadInfo.ActualSha1, in.Sha1)
}

func (s *StorageAdapter) Upload(ctx context.Context, t *entities.Task, filepath string) (*sdk.UploadInfo, error) {
	content, err := util.LoadFile(filepath)
	if err != nil {
		return nil, err
	}
	util.SetStoreData(t, content)
	return s.fileStorage.Upload(ctx, t.GetStoreUri(), content)
}
