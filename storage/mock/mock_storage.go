// Code generated by MockGen. DO NOT EDIT.
// Source: git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage (interfaces: Storage)
//
// Generated by this command:
//
//	mockgen -destination mock/mock_storage.go git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage Storage
//

// Package mock_storage is a generated GoMock package.
package mock_storage

import (
	context "context"
	reflect "reflect"

	kamala_entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	sdk "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk"
	util "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"
	gomock "go.uber.org/mock/gomock"
)

// MockStorage is a mock of Storage interface.
type MockStorage struct {
	ctrl     *gomock.Controller
	recorder *MockStorageMockRecorder
	isgomock struct{}
}

// MockStorageMockRecorder is the mock recorder for MockStorage.
type MockStorageMockRecorder struct {
	mock *MockStorage
}

// NewMockStorage creates a new mock instance.
func NewMockStorage(ctrl *gomock.Controller) *MockStorage {
	mock := &MockStorage{ctrl: ctrl}
	mock.recorder = &MockStorageMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStorage) EXPECT() *MockStorageMockRecorder {
	return m.recorder
}

// Download mocks base method.
func (m *MockStorage) Download(ctx context.Context, input util.DownloadInput) (*sdk.DownloadInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Download", ctx, input)
	ret0, _ := ret[0].(*sdk.DownloadInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Download indicates an expected call of Download.
func (mr *MockStorageMockRecorder) Download(ctx, input any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Download", reflect.TypeOf((*MockStorage)(nil).Download), ctx, input)
}

// Upload mocks base method.
func (m *MockStorage) Upload(ctx context.Context, t *kamala_entities.Task, filepath string) (*sdk.UploadInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upload", ctx, t, filepath)
	ret0, _ := ret[0].(*sdk.UploadInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Upload indicates an expected call of Upload.
func (mr *MockStorageMockRecorder) Upload(ctx, t, filepath any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upload", reflect.TypeOf((*MockStorage)(nil).Upload), ctx, t, filepath)
}
