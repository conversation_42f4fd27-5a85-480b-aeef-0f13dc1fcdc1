package local

import (
	"context"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/adapter"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/proxy"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"
)

func init() {
	_ = proxy.RegisterStorage("local", adapter.NewStorageAdapter(&localStorage{}))
}

type localStorage struct {
}

func NewStorage() storage.Storage {
	return adapter.NewStorageAdapter(&localStorage{})
}
func (s *localStorage) Download(ctx context.Context, in util.DownloadInput) (*sdk.DownloadInfo, error) {
	var downloadInfo sdk.DownloadInfo
	reader, err := util.StreamLoadFile(in.FilePath)
	if err != nil {
		return nil, err
	}
	downloadInfo.Body = reader

	err = util.CheckSha1AndSave(&downloadInfo, &in)
	if err != nil {
		return &downloadInfo, err
	}
	return &downloadInfo, nil
}

func (s *localStorage) Upload(ctx context.Context, uri string, content []byte) (*sdk.UploadInfo, error) {
	return nil, util.SaveFile(content, uri)
}

func (s *localStorage) ConcurrentDownload(ctx context.Context, in util.DownloadInput) (*sdk.DownloadInfo, error) {
	return nil, nil
}
