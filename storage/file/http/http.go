package http

import (
	"context"
	"strings"
	"time"

	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/adapter"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/proxy"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"
)

func init() {
	_ = proxy.RegisterStorage("http", adapter.NewStorageAdapter(&httpStorage{}))
}

type httpStorage struct {
}

func NewStorage() storage.Storage {
	return adapter.NewStorageAdapter(&httpStorage{})
}

// 走http此时in.Url不能为空
func (h *httpStorage) Download(ctx context.Context, in util.DownloadInput) (*sdk.DownloadInfo, error) {
	var downloadInfo sdk.DownloadInfo
	sdkStorage, err := util.GetDialer().FetchStorageSDK(ctx)
	if err != nil {
		return nil, err
	}
	begin := time.Now()
	resp, err := sdkStorage.DownloadFile(ctx, in.Url)
	downloadInfo.DownloadFileDuration = time.Since(begin)
	if err != nil {
		downloadInfo.DownloadFileErr = err
		return &downloadInfo, err
	}
	downloadInfo.Source = getUrlSource(in.Url)
	downloadInfo.Body = resp.Body
	err = util.CheckSha1AndSave(&downloadInfo, &in)
	if err != nil {
		return &downloadInfo, nil
	}
	return &downloadInfo, nil
}

func (h *httpStorage) Upload(ctx context.Context, url string, content []byte) (*sdk.UploadInfo, error) {
	var uploadInfo sdk.UploadInfo
	sdkStorage, err := util.GetDialer().FetchStorageSDK(ctx)
	if err != nil {
		return nil, err
	}
	begin := time.Now()
	err = sdkStorage.UploadUrl(ctx, url, content)
	if err != nil {
		return nil, err
	}
	uploadInfo.UploadFileDuration = time.Since(begin)
	return &uploadInfo, nil
}

func getUrlSource(url string) string {
	if strings.Contains(url, "speccenter.wrapper.qianxin-inc.cn") {
		return "center"
	}
	if strings.Contains(url, "s3.b.qianxin-inc.cn") {
		return "s3"
	}
	return ""
}

func (h *httpStorage) ConcurrentDownload(ctx context.Context, in util.DownloadInput) (*sdk.DownloadInfo, error) {
	return nil, nil
}
