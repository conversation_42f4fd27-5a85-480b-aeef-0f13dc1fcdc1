package file

import (
	"context"

	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"
)

type FileStorage interface {
	Download(ctx context.Context, in util.DownloadInput) (*sdk.DownloadInfo, error)
	Upload(ctx context.Context, uri string, content []byte) (*sdk.UploadInfo, error)
	ConcurrentDownload(ctx context.Context, in util.DownloadInput) (*sdk.DownloadInfo, error)
}
