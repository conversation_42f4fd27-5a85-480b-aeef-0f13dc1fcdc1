package relay

import (
	"context"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/adapter"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/proxy"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"
	"os"
	"path"
	"strings"
)

func init() {
	_ = proxy.RegisterStorage("relay", adapter.NewStorageAdapter(&relayStorage{}))
}

type relayStorage struct {
}

func NewStorage() storage.Storage {
	return adapter.NewStorageAdapter(&relayStorage{})
}

func (r *relayStorage) Download(ctx context.Context, in util.DownloadInput) (*sdk.DownloadInfo, error) {
	var downloadInfo sdk.DownloadInfo

	url := r.getRelayUrl(in.Uri)
	reader, err := util.StreamLoadFile(url)
	if err != nil {
		return nil, err
	}
	downloadInfo.Body = reader
	downloadInfo.Source = "relay"

	err = util.CheckSha1AndSave(&downloadInfo, &in)
	if err != nil {
		return &downloadInfo, err
	}
	return &downloadInfo, nil
}

func (r *relayStorage) getRelayUrl(uri string) string {
	prefix := os.Getenv("KAMALA_RELAY_DIR")
	fields := strings.SplitN(uri, ":", 2)
	if len(fields) == 2 {
		return path.Join(prefix, fields[1])
	} else {
		return ""
	}
}

func (r *relayStorage) Upload(ctx context.Context, uri string, content []byte) (*sdk.UploadInfo, error) {
	var uploadInfo sdk.UploadInfo
	return &uploadInfo, nil
}

func (r *relayStorage) ConcurrentDownload(ctx context.Context, in util.DownloadInput) (*sdk.DownloadInfo, error) {
	return nil, nil
}