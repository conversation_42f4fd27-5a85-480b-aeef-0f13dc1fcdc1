package morpheus

import (
	"crypto/sha1"
	"errors"
	"fmt"
	"io"
	"math"
	"os"
	"strings"
	"time"

	"git-biz.qianxin-inc.cn/zion-infra/data-foundation/apis/morpheus-api.git/generated-go/morpheus/base"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/adapter"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/concurrentSdk"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/proxy"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"

	"context"

	"github.com/Rican7/retry"
	"github.com/Rican7/retry/strategy"
)

func init() {
	_ = proxy.RegisterStorage("morpheus", adapter.NewStorageAdapter(&morpheusStorage{}))
}

const (
	MB = 1024 * 1024
	GB = 1024 * MB
)

type morpheusStorage struct {
	storageSDK sdk.StorageFileInterface
}

func NewStorage() *morpheusStorage {
	return &morpheusStorage{}
}

func NewStorageWithSDK(storageSDK sdk.StorageFileInterface) *morpheusStorage {
	return &morpheusStorage{
		storageSDK: storageSDK,
	}
}

// 重试的要求(如：Uri地址)和限制(如：重试次数、重试间隔时间等)
type retryRequest struct {
	uri      string
	count    uint
	timeout  time.Duration
	waitTime time.Duration
	content  []byte
}

func retryUpload(in *retryRequest, uploadInfo *sdk.UploadInfo, storageSDK sdk.StorageFileInterface) error {
	ctx, cancel := context.WithTimeout(context.Background(), in.timeout)
	defer cancel()
	err := retry.Retry(func(attempt uint) error {
		if storageSDK == nil {
			dialer := util.GetDialer()
			log.Info("get dialer: [%v] ", dialer)
			storageSrv, err := dialer.FetchStorageSDK(ctx)
			log.Info("fetch storage sdk: [%v] ", storageSrv)
			if err != nil {
				return err
			}
			storageSDK = storageSrv
		}

		begin := time.Now()
		retryUploadInfo, err := storageSDK.CreateStorageWithContent(ctx, in.content, in.uri)

		if retryUploadInfo != nil {
			uploadInfo.GrpcDuration = retryUploadInfo.GrpcDuration
			uploadInfo.UploadFileDuration = retryUploadInfo.UploadFileDuration
			uploadInfo.GrpcErr = retryUploadInfo.GrpcErr
			uploadInfo.UploadFileErr = retryUploadInfo.UploadFileErr
			uploadInfo.RetryCount = int(attempt)
		}

		if err != nil {
			log.Warnf("[%v] failed to create storage, err: %v, attempt: %v, length: %v, store_uri: %v",
				time.Since(begin), err, attempt, len(in.content), in.uri)
		}
		return err
	}, strategy.Limit(in.count), strategy.Wait(in.waitTime))
	return err
}

func (c *morpheusStorage) Upload(ctx context.Context, uri string, content []byte) (*sdk.UploadInfo, error) {
	var uploadInfo sdk.UploadInfo
	deal, _ := ctx.Deadline()
	lastTime := deal.Sub(time.Now())
	log.Debugf("upload lastTime : %v, upload timeout：%v\n", lastTime, lastTime)
	uploadIn := &retryRequest{
		uri:      uri,
		count:    1,
		timeout:  lastTime,
		waitTime: time.Second,
		content:  content,
	}
	begin := time.Now()
	err := retryUpload(uploadIn, &uploadInfo, c.storageSDK)
	if err != nil {
		log.Errorf("[%v] failed to create storage, err: %v, store_uri: %v",
			time.Since(begin), err, uri)
		return &uploadInfo, err
	}
	return &uploadInfo, err
}

func retryDownload(in *retryRequest, downloadInfo *sdk.DownloadInfo, storageSdk sdk.StorageFileInterface) error {
	ctx, cancel := context.WithTimeout(context.Background(), in.timeout)
	//defer cancel() TODO 可以打开了，落盘操作全部集中在下载层不在适配器层
	downloadInfo.Cancel = cancel
	err := retry.Retry(func(attempt uint) error {
		retryDownloadInfo, err := storageSdk.DownloadUri(ctx, in.uri)
		if retryDownloadInfo != nil {
			downloadInfo.Code = retryDownloadInfo.Code
			downloadInfo.Source = retryDownloadInfo.Source
			downloadInfo.Content = retryDownloadInfo.Content
			downloadInfo.Body = retryDownloadInfo.Body
			downloadInfo.GrpcDuration += retryDownloadInfo.GrpcDuration
			downloadInfo.DownloadFileDuration += retryDownloadInfo.DownloadFileDuration
			downloadInfo.GrpcErr = retryDownloadInfo.GrpcErr
			downloadInfo.DownloadFileErr = retryDownloadInfo.DownloadFileErr
			downloadInfo.RetryCount = int(attempt)
		}
		return err
	}, strategy.Limit(in.count), strategy.Wait(in.waitTime))
	return err
}

func (c *morpheusStorage) Download(ctx context.Context, in util.DownloadInput) (*sdk.DownloadInfo, error) {
	if in.Size != 0 && in.Size != 1 {
		return c.ConcurrentDownload(ctx, in)
	}
	deal, _ := ctx.Deadline()
	totalTime := deal.Sub(time.Now())

	if totalTime < 0 {
		totalTime = 240 * time.Second
	}

	var downloadInfo sdk.DownloadInfo
	var timeout time.Duration
	begin := time.Now()
	var storageSdk sdk.StorageFileInterface
	var err error
	if c.storageSDK != nil {
		storageSdk = c.storageSDK
	} else {
		storageSdk, err = util.GetDialer().FetchStorageSDK(ctx)
		if err != nil {
			return nil, err
		}
	}
	// 如果通过uri获取样本的size成功，那么取（0.8 * 总时间 - 一个样本阶梯时间）与（一个样本阶梯时间）二者中的最小值作为超时时间
	// 如果需要重试，则将剩余的所有时间：（0.8 * 总时间） - timeout ==>这个值肯定是比上面的超时时间长的，就保证了超时情况下的时间一定是大的
	// 如果通过uri获取样本大小失败，则第一次将超时时间设置为：总时间 * 30%，这样超时后将剩余的70%全部给它

	// TODO 此时的样本真实size不通过RPC调用去将获取了而是采用上游传下来的size
	if in.Size != 0 {
		log.Infof("get storage size:%v\n", in.Size)
		diff := totalTime - util.GetDownloadLadderTime(in.Size)*time.Second
		timeout = time.Duration(math.Min(float64(diff), float64(util.GetDownloadLadderTime(in.Size)*time.Second)))
	} else {
		timeout = time.Duration(float64(totalTime) * 0.3)
		log.Warnf("the true size of the sample is unknown, use default timeout:%v", timeout)
	}

	log.Debugf("totalTime = %v, timeout = %v", totalTime, timeout)
	downloadIn := &retryRequest{
		uri:      in.Uri,
		count:    1,
		timeout:  timeout,
		waitTime: time.Second,
	}
	err = retryDownload(downloadIn, &downloadInfo, storageSdk)
	defer downloadInfo.Cancel()
	if err != nil {
		log.Errorf("[%v] failed to download uri, err: %v, uri: %v",
			time.Since(begin), err, in.Uri)
		return &downloadInfo, errors.New(fmt.Sprintf("failed to download, http err: %v", err))
	}
	defer downloadInfo.Body.Close()
	httpCode := downloadInfo.Code
	// 如果返回的code码在100和300之间说明正常下载，400这类的错误码直接判定为下载失败
	if httpCode >= 100 && httpCode < 400 {
		log.Infof("download success http code: %v", httpCode)
	} else if httpCode >= 400 && httpCode < 500 {
		log.Errorf("failed to download, http code:%v", httpCode)
		return &downloadInfo, errors.New(fmt.Sprintf("http code: %v", httpCode))
	} else if httpCode >= 500 && httpCode < 600 {
		downloadIn.count = 3
		downloadIn.timeout = totalTime - timeout
		downloadIn.waitTime = 2 * time.Second
		log.Warnf("failed to download, server is busy, code:%v, error:%v", httpCode, err)
		err = retryDownload(downloadIn, &downloadInfo, storageSdk)
		httpCode = downloadInfo.Code
	} else {
		downloadIn.count = 1
		downloadIn.timeout = totalTime - timeout
		downloadIn.waitTime = time.Second
		log.Warnf("failed to download, just download timout: %v, code:%v, error:%v", timeout, httpCode, err)
		err = retryDownload(downloadIn, &downloadInfo, storageSdk)
		httpCode = downloadInfo.Code
	}
	if httpCode < 100 || httpCode >= 400 {
		return &downloadInfo, errors.New(fmt.Sprintf("http code: %v", httpCode))
	}
	// 检查sha1的一致性并落盘
	err = util.CheckSha1AndSave(&downloadInfo, &in)
	if err != nil {
		return &downloadInfo, err
	}
	downloadInfo.Cancel()
	return &downloadInfo, nil
}

func (c *morpheusStorage) ConcurrentDownload(ctx context.Context, in util.DownloadInput) (*sdk.DownloadInfo, error) {
	var downloadInfo sdk.DownloadInfo
	file, err := os.OpenFile(in.FilePath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0666)
	if err != nil {
		log.Errorf("open file failed: %v", err)
		return &downloadInfo, err
	}

	var storageSdk sdk.StorageFileInterface
	if c.storageSDK != nil {
		log.Debugf("use storage sdk from context")
		storageSdk = c.storageSDK
	} else {
		var err error
		log.Debugf("use storage sdk from dialer")
		storageSdk, err = util.GetDialer().FetchStorageSDK(ctx)
		if err != nil {
			log.Errorf("get morpheus storage sdk failed: %v", err)
			return &downloadInfo, err
		}
	}
	var s *base.Storage
	deadline, _ := ctx.Deadline()
	log.Debugf("ctx deadline left time: %v", deadline.Sub(time.Now()))
	begin := time.Now()
	if strings.HasPrefix(in.Uri, "files") {
		s, err = storageSdk.GetStorage(ctx, in.Uri)
	} else {
		s, err = storageSdk.GetScanLogBySha1(ctx, in.Uri)
	}
	downloadInfo.GrpcDuration = time.Since(begin)
	if err != nil {
		log.Errorf("get morpheus storage failed: %v", err)
		downloadInfo.GrpcErr = err
		return &downloadInfo, err
	}
	url := s.GetFile().GetUrl()
	downloadInfo.Source = sdk.GetUrlSource(url)
	NumOfConcurrency, part := setConcurrentNum(in.Size)
	d := concurrentSdk.NewDownloader(in.Size, part, NumOfConcurrency)
	res, err := d.Download(ctx, file, url)
	if res != nil {
		downloadInfo.Length = res.Length
		downloadInfo.DownloadFileDuration = res.DownloadFileDuration / time.Duration(NumOfConcurrency)
		downloadInfo.DownloadFileErr = res.DownloadFileErr
	}
	file.Close()
	if err != nil || downloadInfo.Length != in.Size {
		log.Errorf("download failed: %v, n = %v, in.size = %v", err, downloadInfo.Length, in.Size)
		return &downloadInfo, err
	}
	var actualSah1 string
	var size int64
	size, actualSah1, err = checkSha1(in.FilePath, in.Sha1)
	downloadInfo.ActualSha1 = actualSah1
	if size != 0 {
		downloadInfo.Length = uint64(size)
	}
	if err != nil {
		cleanFile(in.FilePath)
		return &downloadInfo, err
	}
	downloadInfo.Sha1Same = true
	return &downloadInfo, nil
}

func setConcurrentNum(size uint64) (int, uint64) {
	concurrency := 1
	piece := size
	switch {
	case size >= 50*MB && size < 200*MB:
		concurrency = int(size/50/MB + 1)
		piece = 50 * MB
		break
	case size >= 200*MB && size < 1.5*GB:
		concurrency = int(size/100/MB + 1)
		piece = 100 * MB
		break
	case size >= 1.5*GB && size < 2.5*GB:
		concurrency = int(size/200/MB + 1)
		piece = 200 * MB
		break
	case size >= 2.5*GB && size < 3.5*GB:
		concurrency = int(size/300/MB + 1)
		piece = 300 * MB
		break
	case size >= 3.5*GB && size < 4.5*GB:
		concurrency = int(size/400/MB + 1)
		piece = 400 * MB
		break
	case size >= 4.5*GB && size < 5.5*GB:
		concurrency = int(size/500/MB + 1)
		piece = 500 * MB
		break
	case size >= 5.5*GB && size < 6.5*GB:
		concurrency = int(size/600/MB + 1)
		piece = 600 * MB
		break
	case size >= 6.5*GB && size < 7.5*GB:
		concurrency = int(size/700/MB + 1)
		piece = 700 * MB
		break
	case size >= 7.5*GB && size < 8.5*GB:
		concurrency = int(size/800/MB + 1)
		piece = 800 * MB
		break
	case size >= 8.5*GB && size < 10*GB:
		concurrency = int(size/900/MB + 1)
		piece = 900 * MB
		break
	}
	return concurrency, piece
}

// 校验sha1一致性，一致返回实际sha1值，校验失败或不一致返回具体错误
func checkSha1(path, expectSha1 string) (int64, string, error) {
	// TODO 不管他是不是压缩包，先校验一下sha1是否一致，不一致的则再去判断是不是压缩包，
	// TODO 是的话解压检验，检验结果还是不一致的话，则判定为sha1不一致
	var size int64
	var actualSha1 string
	var err error
	size, actualSha1, err = streamCalcSha1(path)
	if err != nil {
		return size, actualSha1, err
	}
	// sha1一致返回nil，不一致解压校验sha1一致性
	if actualSha1 == expectSha1 {
		return size, actualSha1, nil
	}
	isZip, err := checkZip(path)
	if err != nil {
		return size, "", err
	}
	if isZip {
		zipFile := fmt.Sprintf("%s.zip", path)
		err = os.Rename(path, zipFile)
		if err != nil {
			return size, "", errors.New(fmt.Sprintf("rename file from: %v to: %v", path, zipFile))
		}
		err = util.UnzipFile(zipFile, path)
		if err != nil {
			_ = util.RemoveFile(zipFile)
			return size, "", errors.New(fmt.Sprintf("unzip src: %v to dest: %v failed: %v", zipFile, path, err))
		}
		err = util.RemoveFile(zipFile)
		if err != nil {
			return size, "", errors.New(fmt.Sprintf("remove file: %v failed: %v", zipFile, err))
		}
	}

	size, actualSha1, err = streamCalcSha1(path)
	if err != nil {
		return size, actualSha1, err
	}
	// sha1一致返回nil，不一致返回codes.InvalidArgument
	if actualSha1 == expectSha1 {
		return size, actualSha1, nil
	}
	return size, actualSha1, status.Errorf(codes.InvalidArgument, "actual sha1: %v not equal expect sha1: %v", actualSha1, expectSha1)
}

func checkZip(path string) (bool, error) {
	if !util.CheckFileIsExist(path) {
		return false, errors.New(fmt.Sprintf("the file: %v not exist", path))
	}
	f, err := os.Open(path)
	if err != nil {
		return false, errors.New(fmt.Sprintf("open file: %v failed: %v", path, err))
	}
	defer f.Close()
	isZip, _ := util.IsZip(f)
	if isZip {
		return true, nil
	}
	return false, nil
}

// 用于下载校验出现任何失败时，做文件的清理，不关心返回值它只是为了保险而存在
func cleanFile(path string) {
	if util.CheckFileIsExist(path) {
		_ = util.RemoveFile(path)
	}
	if util.CheckFileIsExist(fmt.Sprintf("%v.zip", path)) {
		_ = util.RemoveFile(fmt.Sprintf("%v.zip", path))
	}
}

func streamCalcSha1(filepath string) (int64, string, error) {
	f, err := os.Open(filepath)
	var size int64
	hash := sha1.New()
	if err != nil {
		return size, "", errors.New(fmt.Sprintf("open file: %v failed: %v", filepath, err))
	}
	defer f.Close()
	size, err = io.Copy(hash, f)
	actualSha1 := fmt.Sprintf("%x", hash.Sum(nil))
	return size, actualSha1, nil
}
