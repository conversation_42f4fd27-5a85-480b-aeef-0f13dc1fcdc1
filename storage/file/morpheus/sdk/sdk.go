package sdk

import (
	"context"
	"crypto/sha1"
	"crypto/tls"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"math/rand"
	"net"
	"net/http"
	"strings"
	"sync"
	"time"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/credentials"
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/dialer"
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	"git-biz.qianxin-inc.cn/zion-infra/data-foundation/apis/morpheus-api.git/generated-go/base_v1"

	// apiPb "git-biz.qianxin-inc.cn/zion-infra/data-foundation/apis/morpheus-api.git/generated-go/base_v1"
	"git-biz.qianxin-inc.cn/zion-infra/data-foundation/apis/morpheus-api.git/generated-go/morpheus/base"
	file2 "git-biz.qianxin-inc.cn/zion-infra/file_access-api/file_access/file"
	taskPb "git-biz.qianxin-inc.cn/zion-infra/file_access-api/file_access/task"

	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/dnscache"

	// storageApiPb "git-biz.qianxin-inc.cn/zion-infra/morpheus-api/morpheus/api/storage"
	// "git-biz.qianxin-inc.cn/zion-infra/morpheus-api/morpheus/storage"
	"github.com/Rican7/retry"
	"github.com/Rican7/retry/strategy"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// 加一个dns缓存
var resolver = dnscache.New(1 * time.Minute)

// StorageFileInterface 定义存储文件操作的接口
type StorageFileInterface interface {
	CreateStorage(ctx context.Context, filePath string, storageUri string) (*UploadInfo, error)
	CreateStorageWithContent(ctx context.Context, content []byte, storageUri string) (*UploadInfo, error)
	GetStorage(ctx context.Context, storageUri string) (*base.Storage, error)
	GetScanLogBySha1(ctx context.Context, sha1 string) (*base.Storage, error)
	DownloadFile(ctx context.Context, url string) (resp *http.Response, err error)
	DownloadUri(ctx context.Context, resUri string) (*DownloadInfo, error)
	Sha1Sum(content []byte) string
	SetMaxErrorRetryTime(retryTimes int)
}

type StorageFile struct {
	storageCli        base_v1.BaseV1Client
	maxErrorRetryTime int
	dialTimeout       time.Duration
	httpClient        *http.Client
	clientOnce        sync.Once

	UriFunc func(content []byte) string
}

// 确保 StorageFile 实现了 StorageFileInterface 接口
var _ StorageFileInterface = (*StorageFile)(nil)

type DownloadInfo struct {
	Content              []byte
	Body                 io.ReadCloser
	Length               uint64
	GrpcDuration         time.Duration
	DownloadFileDuration time.Duration
	GrpcErr              error
	DownloadFileErr      error
	Source               string // from s3 or center
	RetryCount           int
	Cancel               context.CancelFunc
	Sha1Same             bool
	ActualSha1           string
	Code                 int
}

func (d *DownloadInfo) GetGrpcDuration() time.Duration {
	return d.GrpcDuration
}

func (d *DownloadInfo) GetHttpDuration() time.Duration {
	return d.DownloadFileDuration
}

func (d *DownloadInfo) GetContentSize() int {
	return int(d.Length)
}

type UploadInfo struct {
	GrpcDuration       time.Duration
	UploadFileDuration time.Duration
	GrpcErr            error
	UploadFileErr      error
	RetryCount         int
}

func (u *UploadInfo) GetGrpcDuration() time.Duration {
	return u.GrpcDuration
}

func (u *UploadInfo) GetHttpDuration() time.Duration {
	return u.UploadFileDuration
}

func NewStorageFile(ctx context.Context, storageSrvName string, dialTimeout time.Duration, opt ...dialer.DialOption) (*StorageFile, error) {
	if opt == nil {
		opt = make([]dialer.DialOption, 0)
	}
	opt = append(opt, dialer.DialWithRegistry(nil), dialer.DialWithTimeout(dialTimeout),
		dialer.DialWithCredentialsOption(credentials.WithInsecureDial(false),
			credentials.WithInsecureSkipVerify(true)))

	_ = dialer.RegisterDialOption("DialTimeout", opt...)
	conn := dialer.CreateClientConnection(dialer.WithTarget(storageSrvName), dialer.WithDialOptTag("DialTimeout"))
	sf := &StorageFile{
		storageCli:        base_v1.NewBaseV1Client(conn),
		dialTimeout:       dialTimeout,
		maxErrorRetryTime: 3,
	}
	return sf, nil
}

/*
CreateStorage 根据filePath读取文件，上传指定uri

	filePath : 待上传文件

	storageUri : 资源Uri，例如：目前鉴定平台采用形如 files/{sha1}/storage 作为资源唯一标识符
			 如果uri不同，则认为不同资源，如果一致有重复的，那么进行秒传完成，对外无感知
*/
func (s *StorageFile) CreateStorage(ctx context.Context, filePath string, storageUri string) (*UploadInfo, error) {
	content, err := ioutil.ReadFile(filePath)
	if err != nil {
		s := fmt.Sprintf("[ERROR] CreateStorage read file [%s] failed, err = %v", filePath, err)
		return nil, errors.New(s)
	}
	return s.CreateStorageWithContent(ctx, content, storageUri)
}

/*
CreateStorageWithContent 上传content至指定uri，存储到storage中

	content : 待上传文件内容

	storageUri : same as CreateStorage uri
*/
func (s *StorageFile) CreateStorageWithContent(ctx context.Context, content []byte, storageUri string) (*UploadInfo, error) {
	return s.uploadFileSlice(ctx, content, storageUri)
}

/*
GetStorage 根据resUri获取storage中文件信息

	storageUri : same as CreateStorage storageUri
*/
func (s *StorageFile) GetStorage(ctx context.Context, storageUri string) (*base.Storage, error) {
	reqFile := &base_v1.GetStorageMeta_Request{
		Key: &base.StorageId{
			Uri: storageUri,
		},
	}
	storageSrv, err := s.storageCli.GetStorageMeta(ctx, reqFile)
	if storageSrv != nil {
		return storageSrv.Meta, err
	}
	return nil, err
}

func (s *StorageFile) GetScanLogBySha1(ctx context.Context, sha1 string) (*base.Storage, error) {
	storageSrv, err := s.storageCli.GetScanlogByKey(ctx, &base.GetScanlogBySha1_Request{Sha1: sha1})
	if err != nil {
		return nil, errors.New(fmt.Sprintf("call morpheus GetScanlogBySha1 interface failed: %v", err))
	}
	if storageSrv != nil {
		return storageSrv, err
	}
	return nil, err
}

/*
DownloadFile 根据GetStorage中获取的File.Url，下载文件
*/
func (s *StorageFile) DownloadFile(ctx context.Context, url string) (resp *http.Response, err error) {
	resp, err = s.exeDownload(ctx, url)
	return
}

/*
DownloadUri 下载指定resUri，返回body

	resUri : same as CreateStorage resUri
*/
func (s *StorageFile) DownloadUri(ctx context.Context, resUri string) (*DownloadInfo, error) {
	downloadInfo := DownloadInfo{}
	// 判断uri是sha1还是一个完整的uri,若是以files开头表示为一个完整的uri否则就是一个sha1
	var fp *base.Storage
	var err error
	begin := time.Now()
	if strings.HasPrefix(resUri, "files") {
		fp, err = s.GetStorage(ctx, resUri)
	} else {
		fp, err = s.GetScanLogBySha1(ctx, resUri)
	}
	downloadInfo.GrpcDuration = time.Since(begin)
	if err != nil {
		downloadInfo.GrpcErr = err
		return &downloadInfo, err
	}
	if fp.GetFile().GetUrl() == "" {
		downloadInfo.GrpcErr = errors.New("download url can't be empty")
		return &downloadInfo, err
	}

	begin = time.Now()
	log.Infof("download file url: %s", fp.GetFile().GetUrl())
	resp, err := s.DownloadFile(ctx, fp.GetFile().GetUrl())
	downloadInfo.DownloadFileDuration = time.Since(begin)
	downloadInfo.Source = GetUrlSource(fp.GetFile().GetUrl())
	if err != nil {
		downloadInfo.DownloadFileErr = err
		return &downloadInfo, err
	}
	downloadInfo.Body = resp.Body
	downloadInfo.Code = resp.StatusCode
	return &downloadInfo, nil
}

func (s *StorageFile) Sha1Sum(content []byte) string {
	sha11 := sha1.New()
	sha11.Write(content)
	return hex.EncodeToString(sha11.Sum([]byte(nil)))
}

func (s *StorageFile) SetMaxErrorRetryTime(retryTimes int) {
	s.maxErrorRetryTime = retryTimes
}

func (s *StorageFile) makeStorageRequest(ctx context.Context, content []byte, sha1, uri string) (*base.Storage, error) {
	baseStorage := &base.Storage{
		Uri: uri,
		File: &file2.File{
			Name: uri,
			Size: int64(len(content)),
			Sha1: sha1,
		},
	}

	reqStorage := &base_v1.CreateStorageMeta_Request{
		Key: &base.StorageId{
			Uri: uri,
		},
		Meta: baseStorage,
	}
	_, err := s.storageCli.CreateStorageMeta(ctx, reqStorage)
	if err != nil {
		return nil, err
	}
	log.Debugf("create storage uri %s success", uri)
	return baseStorage, nil
}

func (s *StorageFile) uploadFileSlice(ctx context.Context, content []byte, uri string) (*UploadInfo, error) {
	fileSha1 := s.Sha1Sum(content)
	uploadInfo := UploadInfo{}
	var resultErr error
	begin := time.Now()
	storageSrv, err := s.makeStorageRequest(ctx, content, fileSha1, uri)
	uploadInfo.GrpcDuration = time.Since(begin)
	uploadInfo.GrpcErr = err
	if err != nil {
		st, _ := status.FromError(err)
		if st.Code() == codes.AlreadyExists {
			// 文件已存在，文件上传成功，秒传成功!
			uploadInfo.GrpcErr = err
			return &uploadInfo, nil
		}
		return &uploadInfo, err
	}

	retryTimes := 0
	for ; retryTimes < s.maxErrorRetryTime; retryTimes++ {
		begin = time.Now()
		tasks, err := s.getPrepareUploadTask(ctx, storageSrv, []*taskPb.FileUploadTask{}, fileSha1)
		uploadInfo.GrpcDuration += time.Since((begin))
		uploadInfo.GrpcErr = err
		if err != nil {
			log.Errorf("getPrepareUploadTask failed, err: %v, sha1: %s", err, fileSha1)
			resultErr = err
		} else {
			resultErr = nil
			if len(tasks) == 0 {
				break
			}
			begin = time.Now()
			resultErr = s.runTask(ctx, storageSrv, tasks, content, fileSha1)
			uploadInfo.UploadFileDuration += time.Since(begin)
			uploadInfo.UploadFileErr = resultErr
			if resultErr == nil {
				// save file in storage ok!
				break
			}
		}
	}
	if resultErr == nil {
		// 当没有待上传队列时，等待GetStorage接口状态变为上传完成即可
		retryTimes = 0
		var stor *base.Storage
		for ; retryTimes < s.maxErrorRetryTime; retryTimes++ {
			begin = time.Now()
			stor, err = s.GetStorage(ctx, uri)
			uploadInfo.GrpcDuration += time.Since(begin)
			uploadInfo.GrpcErr = err
			if err != nil {
				log.Errorf("GetStorage failed, err: %v, sha1: %s", err, fileSha1)
				return &uploadInfo, err
			}
			if stor.GetFile().GetState() == file2.State_UPLOADED {
				break
			}
			time.Sleep(time.Second * 1)
		}
		if retryTimes >= s.maxErrorRetryTime {
			return &uploadInfo, status.Errorf(codes.Unavailable,
				"storageUri %s GetStorage file state %s not equal %s, please retry!", uri,
				stor.GetFile().GetState(), file2.State_UPLOADED)
		}
	}
	return &uploadInfo, resultErr
}

func (s *StorageFile) getPrepareUploadTask(ctx context.Context, storageSrv *base.Storage, tasks []*taskPb.FileUploadTask, sha1 string) ([]*taskPb.FileUploadTask, error) {
	taskReq := &base.ListUploadTasks_Request{
		Uri:           storageSrv.File.GetName(),
		Sha1:          sha1,
		FinishedTasks: tasks, // 直接传入所有已完成的任务
	}
	taskResp, err := s.storageCli.ListUploadTasks(ctx, taskReq)
	if err != nil {
		return nil, err
	}
	uploadTasks := taskResp.GetWaitingTasks()
	return uploadTasks, nil
}

func (s *StorageFile) runTask(ctx context.Context, storage *base.Storage, tasks []*taskPb.FileUploadTask, content []byte, fileSha1 string) error {
	return s.runTaskWithRetry(ctx, storage, tasks, content, fileSha1, 0)
}

func (s *StorageFile) runTaskWithRetry(ctx context.Context, storage *base.Storage, tasks []*taskPb.FileUploadTask, content []byte, fileSha1 string, retryCount int) error {
	const maxRetries = 3              // 最大重试次数
	const baseDelay = 2 * time.Second // 基础延迟时间

	var wg sync.WaitGroup
	var errData error = nil
	var mu sync.Mutex
	logger := log.WithContext(ctx)
	logPrefix := fmt.Sprintf("[storage-sdk] uri %s", storage.GetFile().GetName())
	log.Debugf("need to upload %d tasks, retry count: %d", len(tasks), retryCount)

	// 第一阶段：并发执行所有任务
	for i, task := range tasks {
		wg.Add(1)
		go func(task *taskPb.FileUploadTask, i int) {
			defer wg.Done()
			logger.Debugf("%s begin upload content total len %d task offset %d task len %d",
				logPrefix, len(content), task.GetOffset(), task.GetLength())
			err := s.exeTask(ctx, task.Url, content[task.Offset:task.Length+task.Offset])
			if err != nil {
				log.Errorf("failed to upload task %d, url: %s, offset: %d, length: %d, sha1: %s, err: %v",
					i, task.Url, task.Offset, task.Length, fileSha1, err)
				task.State = taskPb.State_FAILED
			} else {
				log.Debugf("successfully uploaded task %d, url: %s, offset: %d, length: %d",
					i, task.Url, task.Offset, task.Length)
				task.State = taskPb.State_FINISHED
			}

			// 保护 errData 的并发写入
			mu.Lock()
			if errData == nil && err != nil {
				errData = err
			}
			mu.Unlock()
		}(task, i)
	}
	wg.Wait()

	// 统计任务执行结果
	var successCount, failCount int
	for _, task := range tasks {
		if task.State == taskPb.State_FINISHED {
			successCount++
		} else if task.State == taskPb.State_FAILED {
			failCount++
		}
	}
	log.Debugf("%s task execution completed: success=%d, failed=%d, total=%d",
		logPrefix, successCount, failCount, len(tasks))

	// 第二阶段：所有任务完成后，统一上报状态并领取下一批
	// 统一上报状态并领取下一批任务
	// 所有任务都已执行完毕，直接使用 tasks 作为已完成的任务
	newTasks, err := s.getPrepareUploadTask(ctx, storage, tasks, fileSha1)
	if err != nil {
		return err
	}

	// 检查是否有新任务需要处理
	if len(newTasks) > 0 {
		// 检查重试次数限制
		if retryCount >= maxRetries {
			log.Errorf("max retries (%d) exceeded, giving up. failed tasks: %d", maxRetries, len(newTasks))
			return fmt.Errorf("max retries exceeded after %d attempts", maxRetries)
		}

		// 应用退避策略：指数退避 + 随机抖动
		if retryCount > 0 {
			delay := time.Duration(retryCount) * baseDelay
			// 添加随机抖动，避免同时重试造成压力
			jitter := time.Duration(rand.Intn(1000)) * time.Millisecond
			totalDelay := delay + jitter

			log.Debugf("applying backoff delay: %v (base: %v, jitter: %v)", totalDelay, delay, jitter)

			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(totalDelay):
				// 继续执行
			}
		}

		// 递归重试
		return s.runTaskWithRetry(ctx, storage, newTasks, content, fileSha1, retryCount+1)
	}

	// 如果有错误但已经没有新任务了，返回错误
	if errData != nil {
		return errData
	}

	return nil
}

func (s *StorageFile) exeTask(ctx context.Context, url string, datas []byte) error {
	request, err := http.NewRequest("PUT", url, strings.NewReader(string(datas)))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	request = request.WithContext(ctx)
	res, err := s.getHttpClient().Do(request)
	if err != nil {
		return fmt.Errorf("failed to execute request: %w", err)
	}
	defer res.Body.Close()
	if res.StatusCode >= 400 {
		body, _ := io.ReadAll(res.Body)
		return status.Errorf(codes.Code(res.StatusCode), "upload http response code %d, body: %s", res.StatusCode, string(body))
	}
	return nil
}

func (s *StorageFile) UploadUrl(ctx context.Context, url string, content []byte) error {
	err := s.exeTask(ctx, url, content)
	if err != nil {
		log.Errorf("failed to upload by http：%v", err)
		return err
	}
	return nil
}

func (s *StorageFile) exeDownload(ctx context.Context, url string) (*http.Response, error) {
	request, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}
	request = request.WithContext(ctx)
	res, err := s.getHttpClient().Do(request)
	if err != nil {
		return nil, err
	}
	// defer res.Body.Close()
	return res, nil
}

func (s *StorageFile) getHttpClient() *http.Client {
	s.clientOnce.Do(func() {
		dialer := &net.Dialer{
			Timeout: s.dialTimeout,
		}
		// 加一个dns缓存
		// resolver = dnscache.New(1 * time.Minute)
		s.httpClient = &http.Client{
			Transport: &http.Transport{
				DialContext: func(ctx context.Context, network, address string) (net.Conn, error) {
					separator := strings.LastIndex(address, ":")
					ip := ""
					err := retry.Retry(func(attempt uint) error {
						ipaddress, err := resolver.FetchOneString(address[:separator])
						log.Debugf("dns parsing domain: %v successfully, ip: %v", address[:separator], ipaddress)
						ip = ipaddress
						return err
					}, strategy.Limit(3), strategy.Wait(10*time.Millisecond))
					if err != nil {
						log.Errorf("dns parsing ip: %v error: %v", address[:separator], err)
						return nil, err
					}

					ipn := net.ParseIP(ip)
					if ipn == nil {
						log.Errorf("parse ip err, ip:%v", ip)
						return nil, fmt.Errorf("parse ip err, ip:%v", ip)
					}

					addr := ip + address[separator:]

					if ipn.To16() != nil {
						addr = fmt.Sprintf("[%v]%v", ip, address[separator:])
					}

					conn, err := dialer.DialContext(ctx, network, addr)
					if err != nil {
						log.Errorf("dailer network: [%v] address: [%v], err: %v", network, ip+address[separator:], err)
					}
					return conn, err
				},
				DisableKeepAlives: true,
				TLSClientConfig:   &tls.Config{InsecureSkipVerify: true},
			},
		}
	})
	return s.httpClient
}

func GetUrlSource(url string) string {
	if strings.Contains(url, "speccenter.wrapper.qianxin-inc.cn") {
		return "center"
	}

	if strings.Contains(url, "s3.b.qianxin-inc.cn") {
		return "s3"
	}

	return ""
}
