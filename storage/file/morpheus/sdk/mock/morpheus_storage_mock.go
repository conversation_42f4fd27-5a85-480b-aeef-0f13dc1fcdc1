// Code generated by MockGen. DO NOT EDIT.
// Source: git-biz.qianxin-inc.cn/zion-infra/data-foundation/apis/morpheus-api.git/generated-go/base_v1 (interfaces: BaseV1Client)
//
// Generated by this command:
//
//	mockgen git-biz.qianxin-inc.cn/zion-infra/data-foundation/apis/morpheus-api.git/generated-go/base_v1 BaseV1Client
//

// Package mock_base_v1 is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	base_v1 "git-biz.qianxin-inc.cn/zion-infra/data-foundation/apis/morpheus-api.git/generated-go/base_v1"
	base "git-biz.qianxin-inc.cn/zion-infra/data-foundation/apis/morpheus-api.git/generated-go/morpheus/base"
	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockBaseV1Client is a mock of BaseV1Client interface.
type MockBaseV1Client struct {
	ctrl     *gomock.Controller
	recorder *MockBaseV1ClientMockRecorder
}

// MockBaseV1ClientMockRecorder is the mock recorder for MockBaseV1Client.
type MockBaseV1ClientMockRecorder struct {
	mock *MockBaseV1Client
}

// NewMockBaseV1Client creates a new mock instance.
func NewMockBaseV1Client(ctrl *gomock.Controller) *MockBaseV1Client {
	mock := &MockBaseV1Client{ctrl: ctrl}
	mock.recorder = &MockBaseV1ClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBaseV1Client) EXPECT() *MockBaseV1ClientMockRecorder {
	return m.recorder
}

// AppendKeyValueMetaEvents mocks base method.
func (m *MockBaseV1Client) AppendKeyValueMetaEvents(arg0 context.Context, arg1 *base_v1.AppendKeyValueMetaEvents_Request, arg2 ...grpc.CallOption) (*base_v1.AppendKeyValueMetaEvents_Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AppendKeyValueMetaEvents", varargs...)
	ret0, _ := ret[0].(*base_v1.AppendKeyValueMetaEvents_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AppendKeyValueMetaEvents indicates an expected call of AppendKeyValueMetaEvents.
func (mr *MockBaseV1ClientMockRecorder) AppendKeyValueMetaEvents(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AppendKeyValueMetaEvents", reflect.TypeOf((*MockBaseV1Client)(nil).AppendKeyValueMetaEvents), varargs...)
}

// AppendStorageMetaEvents mocks base method.
func (m *MockBaseV1Client) AppendStorageMetaEvents(arg0 context.Context, arg1 *base_v1.AppendStorageMetaEvents_Request, arg2 ...grpc.CallOption) (*base_v1.AppendStorageMetaEvents_Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AppendStorageMetaEvents", varargs...)
	ret0, _ := ret[0].(*base_v1.AppendStorageMetaEvents_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AppendStorageMetaEvents indicates an expected call of AppendStorageMetaEvents.
func (mr *MockBaseV1ClientMockRecorder) AppendStorageMetaEvents(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AppendStorageMetaEvents", reflect.TypeOf((*MockBaseV1Client)(nil).AppendStorageMetaEvents), varargs...)
}

// BatchGetKeyValueMeta mocks base method.
func (m *MockBaseV1Client) BatchGetKeyValueMeta(arg0 context.Context, arg1 *base_v1.BatchGetKeyValueMeta_Request, arg2 ...grpc.CallOption) (*base_v1.BatchGetKeyValueMeta_Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetKeyValueMeta", varargs...)
	ret0, _ := ret[0].(*base_v1.BatchGetKeyValueMeta_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetKeyValueMeta indicates an expected call of BatchGetKeyValueMeta.
func (mr *MockBaseV1ClientMockRecorder) BatchGetKeyValueMeta(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetKeyValueMeta", reflect.TypeOf((*MockBaseV1Client)(nil).BatchGetKeyValueMeta), varargs...)
}

// BatchGetStorageMeta mocks base method.
func (m *MockBaseV1Client) BatchGetStorageMeta(arg0 context.Context, arg1 *base_v1.BatchGetStorageMeta_Request, arg2 ...grpc.CallOption) (*base_v1.BatchGetStorageMeta_Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetStorageMeta", varargs...)
	ret0, _ := ret[0].(*base_v1.BatchGetStorageMeta_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetStorageMeta indicates an expected call of BatchGetStorageMeta.
func (mr *MockBaseV1ClientMockRecorder) BatchGetStorageMeta(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetStorageMeta", reflect.TypeOf((*MockBaseV1Client)(nil).BatchGetStorageMeta), varargs...)
}

// BatchUpsertKeyValues mocks base method.
func (m *MockBaseV1Client) BatchUpsertKeyValues(arg0 context.Context, arg1 *base.BatchUpsertKeyValues_Request, arg2 ...grpc.CallOption) (*base.BatchUpsertKeyValues_Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchUpsertKeyValues", varargs...)
	ret0, _ := ret[0].(*base.BatchUpsertKeyValues_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchUpsertKeyValues indicates an expected call of BatchUpsertKeyValues.
func (mr *MockBaseV1ClientMockRecorder) BatchUpsertKeyValues(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpsertKeyValues", reflect.TypeOf((*MockBaseV1Client)(nil).BatchUpsertKeyValues), varargs...)
}

// CreateKeyValueMeta mocks base method.
func (m *MockBaseV1Client) CreateKeyValueMeta(arg0 context.Context, arg1 *base_v1.CreateKeyValueMeta_Request, arg2 ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateKeyValueMeta", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateKeyValueMeta indicates an expected call of CreateKeyValueMeta.
func (mr *MockBaseV1ClientMockRecorder) CreateKeyValueMeta(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateKeyValueMeta", reflect.TypeOf((*MockBaseV1Client)(nil).CreateKeyValueMeta), varargs...)
}

// CreateKeyValueWithKey mocks base method.
func (m *MockBaseV1Client) CreateKeyValueWithKey(arg0 context.Context, arg1 *base_v1.CreateKeyValueWithKey_Request, arg2 ...grpc.CallOption) (*base_v1.CreateKeyValueWithKey_Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateKeyValueWithKey", varargs...)
	ret0, _ := ret[0].(*base_v1.CreateKeyValueWithKey_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateKeyValueWithKey indicates an expected call of CreateKeyValueWithKey.
func (mr *MockBaseV1ClientMockRecorder) CreateKeyValueWithKey(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateKeyValueWithKey", reflect.TypeOf((*MockBaseV1Client)(nil).CreateKeyValueWithKey), varargs...)
}

// CreateStorageMeta mocks base method.
func (m *MockBaseV1Client) CreateStorageMeta(arg0 context.Context, arg1 *base_v1.CreateStorageMeta_Request, arg2 ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateStorageMeta", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateStorageMeta indicates an expected call of CreateStorageMeta.
func (mr *MockBaseV1ClientMockRecorder) CreateStorageMeta(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateStorageMeta", reflect.TypeOf((*MockBaseV1Client)(nil).CreateStorageMeta), varargs...)
}

// CreateStorageWithKey mocks base method.
func (m *MockBaseV1Client) CreateStorageWithKey(arg0 context.Context, arg1 *base_v1.CreateStorageWithKey_Request, arg2 ...grpc.CallOption) (*base_v1.CreateStorageWithKey_Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateStorageWithKey", varargs...)
	ret0, _ := ret[0].(*base_v1.CreateStorageWithKey_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateStorageWithKey indicates an expected call of CreateStorageWithKey.
func (mr *MockBaseV1ClientMockRecorder) CreateStorageWithKey(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateStorageWithKey", reflect.TypeOf((*MockBaseV1Client)(nil).CreateStorageWithKey), varargs...)
}

// DeleteKeyValue mocks base method.
func (m *MockBaseV1Client) DeleteKeyValue(arg0 context.Context, arg1 *base_v1.DeleteKeyValue_Request, arg2 ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteKeyValue", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteKeyValue indicates an expected call of DeleteKeyValue.
func (mr *MockBaseV1ClientMockRecorder) DeleteKeyValue(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteKeyValue", reflect.TypeOf((*MockBaseV1Client)(nil).DeleteKeyValue), varargs...)
}

// DeleteKeyValueMeta mocks base method.
func (m *MockBaseV1Client) DeleteKeyValueMeta(arg0 context.Context, arg1 *base_v1.DeleteKeyValueMeta_Request, arg2 ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteKeyValueMeta", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteKeyValueMeta indicates an expected call of DeleteKeyValueMeta.
func (mr *MockBaseV1ClientMockRecorder) DeleteKeyValueMeta(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteKeyValueMeta", reflect.TypeOf((*MockBaseV1Client)(nil).DeleteKeyValueMeta), varargs...)
}

// DeleteKeyValueMetaEvents mocks base method.
func (m *MockBaseV1Client) DeleteKeyValueMetaEvents(arg0 context.Context, arg1 *base_v1.DeleteKeyValueMetaEvents_Request, arg2 ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteKeyValueMetaEvents", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteKeyValueMetaEvents indicates an expected call of DeleteKeyValueMetaEvents.
func (mr *MockBaseV1ClientMockRecorder) DeleteKeyValueMetaEvents(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteKeyValueMetaEvents", reflect.TypeOf((*MockBaseV1Client)(nil).DeleteKeyValueMetaEvents), varargs...)
}

// DeleteStorage mocks base method.
func (m *MockBaseV1Client) DeleteStorage(arg0 context.Context, arg1 *base_v1.DeleteStorage_Request, arg2 ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteStorage", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteStorage indicates an expected call of DeleteStorage.
func (mr *MockBaseV1ClientMockRecorder) DeleteStorage(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteStorage", reflect.TypeOf((*MockBaseV1Client)(nil).DeleteStorage), varargs...)
}

// DeleteStorageMeta mocks base method.
func (m *MockBaseV1Client) DeleteStorageMeta(arg0 context.Context, arg1 *base_v1.DeleteStorageMeta_Request, arg2 ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteStorageMeta", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteStorageMeta indicates an expected call of DeleteStorageMeta.
func (mr *MockBaseV1ClientMockRecorder) DeleteStorageMeta(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteStorageMeta", reflect.TypeOf((*MockBaseV1Client)(nil).DeleteStorageMeta), varargs...)
}

// DeleteStorageMetaEvents mocks base method.
func (m *MockBaseV1Client) DeleteStorageMetaEvents(arg0 context.Context, arg1 *base_v1.DeleteStorageMetaEvents_Request, arg2 ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteStorageMetaEvents", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteStorageMetaEvents indicates an expected call of DeleteStorageMetaEvents.
func (mr *MockBaseV1ClientMockRecorder) DeleteStorageMetaEvents(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteStorageMetaEvents", reflect.TypeOf((*MockBaseV1Client)(nil).DeleteStorageMetaEvents), varargs...)
}

// GetKeyValueMeta mocks base method.
func (m *MockBaseV1Client) GetKeyValueMeta(arg0 context.Context, arg1 *base_v1.GetKeyValueMeta_Request, arg2 ...grpc.CallOption) (*base_v1.GetKeyValueMeta_Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetKeyValueMeta", varargs...)
	ret0, _ := ret[0].(*base_v1.GetKeyValueMeta_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetKeyValueMeta indicates an expected call of GetKeyValueMeta.
func (mr *MockBaseV1ClientMockRecorder) GetKeyValueMeta(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKeyValueMeta", reflect.TypeOf((*MockBaseV1Client)(nil).GetKeyValueMeta), varargs...)
}

// GetScanlogByKey mocks base method.
func (m *MockBaseV1Client) GetScanlogByKey(arg0 context.Context, arg1 *base.GetScanlogBySha1_Request, arg2 ...grpc.CallOption) (*base.Storage, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetScanlogByKey", varargs...)
	ret0, _ := ret[0].(*base.Storage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScanlogByKey indicates an expected call of GetScanlogByKey.
func (mr *MockBaseV1ClientMockRecorder) GetScanlogByKey(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScanlogByKey", reflect.TypeOf((*MockBaseV1Client)(nil).GetScanlogByKey), varargs...)
}

// GetStorageMeta mocks base method.
func (m *MockBaseV1Client) GetStorageMeta(arg0 context.Context, arg1 *base_v1.GetStorageMeta_Request, arg2 ...grpc.CallOption) (*base_v1.GetStorageMeta_Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStorageMeta", varargs...)
	ret0, _ := ret[0].(*base_v1.GetStorageMeta_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStorageMeta indicates an expected call of GetStorageMeta.
func (mr *MockBaseV1ClientMockRecorder) GetStorageMeta(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStorageMeta", reflect.TypeOf((*MockBaseV1Client)(nil).GetStorageMeta), varargs...)
}

// ListKeyValues mocks base method.
func (m *MockBaseV1Client) ListKeyValues(arg0 context.Context, arg1 *base.ListKeyValues_Request, arg2 ...grpc.CallOption) (*base.ListKeyValues_Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListKeyValues", varargs...)
	ret0, _ := ret[0].(*base.ListKeyValues_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListKeyValues indicates an expected call of ListKeyValues.
func (mr *MockBaseV1ClientMockRecorder) ListKeyValues(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListKeyValues", reflect.TypeOf((*MockBaseV1Client)(nil).ListKeyValues), varargs...)
}

// ListUploadTasks mocks base method.
func (m *MockBaseV1Client) ListUploadTasks(arg0 context.Context, arg1 *base.ListUploadTasks_Request, arg2 ...grpc.CallOption) (*base.ListUploadTasks_Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListUploadTasks", varargs...)
	ret0, _ := ret[0].(*base.ListUploadTasks_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListUploadTasks indicates an expected call of ListUploadTasks.
func (mr *MockBaseV1ClientMockRecorder) ListUploadTasks(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListUploadTasks", reflect.TypeOf((*MockBaseV1Client)(nil).ListUploadTasks), varargs...)
}

// PullKeyValueMetaEvents mocks base method.
func (m *MockBaseV1Client) PullKeyValueMetaEvents(arg0 context.Context, arg1 *base_v1.PullKeyValueMetaEvents_Request, arg2 ...grpc.CallOption) (base_v1.BaseV1_PullKeyValueMetaEventsClient, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PullKeyValueMetaEvents", varargs...)
	ret0, _ := ret[0].(base_v1.BaseV1_PullKeyValueMetaEventsClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PullKeyValueMetaEvents indicates an expected call of PullKeyValueMetaEvents.
func (mr *MockBaseV1ClientMockRecorder) PullKeyValueMetaEvents(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PullKeyValueMetaEvents", reflect.TypeOf((*MockBaseV1Client)(nil).PullKeyValueMetaEvents), varargs...)
}

// PullStorageMetaEvents mocks base method.
func (m *MockBaseV1Client) PullStorageMetaEvents(arg0 context.Context, arg1 *base_v1.PullStorageMetaEvents_Request, arg2 ...grpc.CallOption) (base_v1.BaseV1_PullStorageMetaEventsClient, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PullStorageMetaEvents", varargs...)
	ret0, _ := ret[0].(base_v1.BaseV1_PullStorageMetaEventsClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PullStorageMetaEvents indicates an expected call of PullStorageMetaEvents.
func (mr *MockBaseV1ClientMockRecorder) PullStorageMetaEvents(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PullStorageMetaEvents", reflect.TypeOf((*MockBaseV1Client)(nil).PullStorageMetaEvents), varargs...)
}

// SubscribeKeyValueMetaEvents mocks base method.
func (m *MockBaseV1Client) SubscribeKeyValueMetaEvents(arg0 context.Context, arg1 *base_v1.SubscribeKeyValueMetaEvents_Request, arg2 ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubscribeKeyValueMetaEvents", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubscribeKeyValueMetaEvents indicates an expected call of SubscribeKeyValueMetaEvents.
func (mr *MockBaseV1ClientMockRecorder) SubscribeKeyValueMetaEvents(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubscribeKeyValueMetaEvents", reflect.TypeOf((*MockBaseV1Client)(nil).SubscribeKeyValueMetaEvents), varargs...)
}

// SubscribeStorageMetaEvents mocks base method.
func (m *MockBaseV1Client) SubscribeStorageMetaEvents(arg0 context.Context, arg1 *base_v1.SubscribeStorageMetaEvents_Request, arg2 ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubscribeStorageMetaEvents", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubscribeStorageMetaEvents indicates an expected call of SubscribeStorageMetaEvents.
func (mr *MockBaseV1ClientMockRecorder) SubscribeStorageMetaEvents(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubscribeStorageMetaEvents", reflect.TypeOf((*MockBaseV1Client)(nil).SubscribeStorageMetaEvents), varargs...)
}
