package sdk

import (
	"bytes"
	"context"
	"crypto/sha1"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"os"
	"testing"
	"time"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git"
	"git-biz.qianxin-inc.cn/zion-infra/data-foundation/apis/morpheus-api.git/generated-go/base_v1"
	"git-biz.qianxin-inc.cn/zion-infra/data-foundation/apis/morpheus-api.git/generated-go/morpheus/base"
	file2 "git-biz.qianxin-inc.cn/zion-infra/file_access-api/file_access/file"
	taskPb "git-biz.qianxin-inc.cn/zion-infra/file_access-api/file_access/task"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk/mock"
	"google.golang.org/protobuf/types/known/emptypb"

	// "github.com/golang/mock/gomock"

	"github.com/cch123/supermonkey"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

const (
	MB = 1024 * 1024
	GB = 1024 * MB
)

func cleanTestFiles(files []string) {
	for _, path := range files {
		err := os.Remove(path)
		if err != nil {
			log.Printf("Remove test file: %v failed: %v", path, err)
		} else {
			log.Printf("Remove test file: %v", path)
		}
	}
}

func TestExeDownload(t *testing.T) {
	// s := &StorageFile{}
	// url := "https://golangnote.com/topic/251.html"
	// ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	// defer cancel()
	// _, err := s.exeDownload(ctx, url)
	// if err != nil {
	// 	t.Fatalf("exe download failed: %v", err)
	// }
	// t.Log("length: ", len(ret.Body))
}

func Test_Sha1Sum(t *testing.T) {
	h := sha1.New()
	io.Copy(h, bytes.NewReader([]byte("test")))
	sha1Sum := fmt.Sprintf("%x", h.Sum(nil))

	s := &StorageFile{}
	res := s.Sha1Sum([]byte("test"))

	assert.Equal(t, sha1Sum, res)
}

func Test_GetStorage(t *testing.T) {
	crtl := gomock.NewController(t)
	mock_cli := mock.NewMockBaseV1Client(crtl)
	mock_cli.EXPECT().GetStorageMeta(gomock.Any(), gomock.Any()).Return(
		&base_v1.GetStorageMeta_Response{}, nil,
	).AnyTimes()
	s := StorageFile{
		storageCli: mock_cli,
	}

	s.GetStorage(context.Background(), "")

	s.getHttpClient()
}

func Test_getHttpClient(t *testing.T) {
	crtl := gomock.NewController(t)
	mock_cli := mock.NewMockBaseV1Client(crtl)
	// mock_cli.EXPECT().GetStorage(gomock.Any(), gomock.Any()).Return(
	// 	&storage.Storage{}, nil,
	// ).AnyTimes()
	s := StorageFile{
		storageCli: mock_cli,
	}

	s.getHttpClient()
}

func Test_GetUrlSource(t *testing.T) {
	res := GetUrlSource("speccenter.wrapper.qianxin-inc.cn/")
	assert.Equal(t, "center", res)

	res = GetUrlSource("s3.b.qianxin-inc.cn/")
	assert.Equal(t, "s3", res)

	res = GetUrlSource("test/")
	assert.Equal(t, "", res)
}

func Test_makeStorageRequest(t *testing.T) {
	crtl := gomock.NewController(t)

	t.Run("success", func(t *testing.T) {
		mock_cli := mock.NewMockBaseV1Client(crtl)
		mock_cli.EXPECT()
		mock_cli.EXPECT().CreateStorageMeta(gomock.Any(), gomock.Any()).Return(
			&emptypb.Empty{}, nil,
		).AnyTimes()

		s := StorageFile{
			storageCli: mock_cli,
		}

		_, err := s.makeStorageRequest(context.Background(), []byte{}, "", "")

		assert.Nil(t, err)
	})

	t.Run("err", func(t *testing.T) {
		mock_cli := mock.NewMockBaseV1Client(crtl)
		mock_cli.EXPECT()
		mock_cli.EXPECT().CreateStorageMeta(gomock.Any(), gomock.Any()).Return(
			&emptypb.Empty{}, fmt.Errorf("test"),
		).AnyTimes()

		s := StorageFile{
			storageCli: mock_cli,
		}

		_, err := s.makeStorageRequest(context.Background(), []byte{}, "", "")

		assert.NotNil(t, err)
	})
}

func TestStorageFile_GetUrlSource(t *testing.T) {
	a := assert.New(t)
	tests := []struct {
		Uri    string
		Source string
	}{
		{
			Uri:    "https://speccenter.wrapper.qianxin-inc.cn",
			Source: "center",
		},
		{
			Uri:    "https://s3.b.qianxin-inc.cn",
			Source: "s3",
		},
		{
			Uri:    "http://other",
			Source: "",
		},
	}

	for _, item := range tests {
		source := GetUrlSource(item.Uri)
		a.Equal(item.Source, source)
	}
}

func TestStorageFile_UploadUrl(t *testing.T) {
	framework.Init()
	a := assert.New(t)
	host := "127.0.0.1:31992"
	ctx, cancel1 := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel1()

	storage, err := NewStorageFile(ctx, host, 10*time.Second)
	a.Equal(nil, err, "NewStorageFile failed")
	content := []byte("abc")
	url := "http://127.0.0.1:64000/not/exist"
	err = storage.UploadUrl(ctx, url, content)
	a.ErrorContains(err, "connection refused")
}

func TestStorageFile_CreateStorage(t *testing.T) {
	t.Skip("skip integration test: requires real morpheus service")
	framework.Init()
	a := assert.New(t)
	host := "*************:31992"
	ctx, cancel1 := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel1()
	// sha := SHA1("UploadTest")
	sha := "10a311d33341e54cb11debafbbab64d2d6c353c7/logs/qde2m"
	uri := fmt.Sprintf("files/%s/storage", sha)

	// 确保内容唯一性，以触发上传 runTask 逻辑
	now := time.Now()
	content := bytes.Repeat([]byte(now.String()), 14*MB/len(now.String()))
	path := "/tmp/my_upload_sample"

	ioutil.WriteFile(path, content, 0o644)

	storage, err := NewStorageFile(ctx, host, 10*time.Second)
	a.Equal(nil, err, "NewStorageFile failed")
	info, err := storage.CreateStorage(ctx, path, uri)
	a.Equal(nil, err, "CreateStorage failed")

	log.Printf("grpc duration: %v", info.GetGrpcDuration())
	log.Printf("http duration: %v", info.GetHttpDuration())

	fg, err := storage.GetStorage(ctx, uri)
	a.Equal(nil, err)
	t.Logf("URL : %v\n", fg.File.GetUrl())

	os.Remove(path)
}

func TestStorageFile_DownloadUri(t *testing.T) {
	t.Skip("skip integration test: requires real morpheus service")
	framework.Init()
	a := assert.New(t)
	host := "*************:31992"

	tests := []struct {
		Uri        string
		FilePath   string
		Sha1       string
		expectSize int
	}{
		{
			Uri:        "files/1724bf26caf8cfb1f11b9ef3fc028012f32deeb2/storage",
			FilePath:   "./1724bf26caf8cfb1f11b9ef3fc028012f32deeb2",
			Sha1:       "1724bf26caf8cfb1f11b9ef3fc028012f32deeb2",
			expectSize: 1124712,
		},
		{
			// get uri by calling GetScanLogBySha1
			Uri:        "1724bf26caf8cfb1f11b9ef3fc028012f32deeb2",
			FilePath:   "./1724bf26caf8cfb1f11b9ef3fc028012f32deeb2",
			Sha1:       "1724bf26caf8cfb1f11b9ef3fc028012f32deeb2",
			expectSize: 1124712,
		},
	}
	ctx, cancel := context.WithTimeout(context.Background(), 600*time.Second)
	defer cancel()
	testFiles := make([]string, len(tests))
	for _, tt := range tests {
		storage, err := NewStorageFile(ctx, host, 10*time.Second)
		a.Equal(nil, err, "NewStorageFile failed")
		info, err := storage.DownloadUri(ctx, tt.Uri)
		if err != nil {
			t.Fatalf("download failed: %v", err)
		}
		a.Equal(nil, err)
		a.Equal(200, info.Code)
		a.Equal(0, int(info.Length))
		a.Equal(0, int(info.GetContentSize()))

		log.Printf("grpc duration: %v", info.GetGrpcDuration())
		log.Printf("http duration: %v", info.GetHttpDuration())

		content, err := ioutil.ReadAll(info.Body)
		a.Equal(nil, err)
		a.Equal(tt.expectSize, len(content))
		testFiles = append(testFiles, tt.FilePath)
	}
	cleanTestFiles(testFiles)
}

func TestStorageFile_runTaskWithRetry(t *testing.T) {
	// 创建测试数据
	content := []byte("test content for upload")
	fileSha1 := "test-sha1"
	storageObj := &base.Storage{
		File: &file2.File{
			Name: "test-file",
		},
	}

	t.Run("successful upload without retry", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockStorageCli := mock.NewMockBaseV1Client(ctrl)
		storage := &StorageFile{
			storageCli: mockStorageCli,
		}

		// 使用 supermonkey mock exeTask 方法
		patchGuard := supermonkey.Patch((*StorageFile).exeTask, func(s *StorageFile, ctx context.Context, url string, datas []byte) error {
			return nil
		})
		defer patchGuard.Unpatch()

		ctx := context.Background()
		tasks := []*taskPb.FileUploadTask{
			{
				Url:    "http://example.com/upload1",
				Offset: 0,
				Length: 10,
				State:  taskPb.State_WAITING,
			},
		}

		// Mock getPrepareUploadTask 返回空任务列表，表示上传完成
		mockStorageCli.EXPECT().
			ListUploadTasks(gomock.Any(), gomock.Any()).
			Return(&base.ListUploadTasks_Response{
				WaitingTasks: []*taskPb.FileUploadTask{},
			}, nil).
			Times(1)

		err := storage.runTask(ctx, storageObj, tasks, content, fileSha1)
		assert.NoError(t, err)
	})

	t.Run("successful upload with retry", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockStorageCli := mock.NewMockBaseV1Client(ctrl)
		storage := &StorageFile{
			storageCli: mockStorageCli,
		}

		// 使用 supermonkey mock exeTask 方法
		patchGuard := supermonkey.Patch((*StorageFile).exeTask, func(s *StorageFile, ctx context.Context, url string, datas []byte) error {
			return nil
		})
		defer patchGuard.Unpatch()

		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		tasks := []*taskPb.FileUploadTask{
			{
				Url:    "http://example.com/upload1",
				Offset: 0,
				Length: 10,
				State:  taskPb.State_WAITING,
			},
		}

		// 第一次调用返回新任务，第二次返回空列表
		gomock.InOrder(
			mockStorageCli.EXPECT().
				ListUploadTasks(gomock.Any(), gomock.Any()).
				Return(&base.ListUploadTasks_Response{
					WaitingTasks: []*taskPb.FileUploadTask{
						{
							Url:    "http://example.com/upload2",
							Offset: 10,
							Length: 5,
							State:  taskPb.State_WAITING,
						},
					},
				}, nil),
			mockStorageCli.EXPECT().
				ListUploadTasks(gomock.Any(), gomock.Any()).
				Return(&base.ListUploadTasks_Response{
					WaitingTasks: []*taskPb.FileUploadTask{},
				}, nil),
		)

		err := storage.runTask(ctx, storageObj, tasks, content, fileSha1)
		assert.NoError(t, err)
	})

	t.Run("max retries exceeded", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockStorageCli := mock.NewMockBaseV1Client(ctrl)
		storage := &StorageFile{
			storageCli: mockStorageCli,
		}

		// 使用 supermonkey mock exeTask 方法
		patchGuard := supermonkey.Patch((*StorageFile).exeTask, func(s *StorageFile, ctx context.Context, url string, datas []byte) error {
			return nil
		})
		defer patchGuard.Unpatch()

		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		tasks := []*taskPb.FileUploadTask{
			{
				Url:    "http://example.com/upload1",
				Offset: 0,
				Length: 10,
				State:  taskPb.State_WAITING,
			},
		}

		// 每次都返回新任务，触发重试
		mockStorageCli.EXPECT().
			ListUploadTasks(gomock.Any(), gomock.Any()).
			Return(&base.ListUploadTasks_Response{
				WaitingTasks: []*taskPb.FileUploadTask{
					{
						Url:    "http://example.com/upload2",
						Offset: 10,
						Length: 5,
						State:  taskPb.State_WAITING,
					},
				},
			}, nil).
			Times(4) // 初始调用 + 3次重试

		err := storage.runTask(ctx, storageObj, tasks, content, fileSha1)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "max retries exceeded")
	})

	t.Run("context cancelled during backoff", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockStorageCli := mock.NewMockBaseV1Client(ctrl)
		storage := &StorageFile{
			storageCli: mockStorageCli,
		}

		// 使用 supermonkey mock exeTask 方法
		patchGuard := supermonkey.Patch((*StorageFile).exeTask, func(s *StorageFile, ctx context.Context, url string, datas []byte) error {
			return nil
		})
		defer patchGuard.Unpatch()

		ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
		defer cancel()

		tasks := []*taskPb.FileUploadTask{
			{
				Url:    "http://example.com/upload1",
				Offset: 0,
				Length: 10,
				State:  taskPb.State_WAITING,
			},
		}

		// 第一次调用返回新任务，触发重试
		mockStorageCli.EXPECT().
			ListUploadTasks(gomock.Any(), gomock.Any()).
			Return(&base.ListUploadTasks_Response{
				WaitingTasks: []*taskPb.FileUploadTask{
					{
						Url:    "http://example.com/upload2",
						Offset: 10,
						Length: 5,
						State:  taskPb.State_WAITING,
					},
				},
			}, nil).
			AnyTimes()

		// 快速取消上下文
		go func() {
			time.Sleep(50 * time.Millisecond)
			cancel()
		}()

		err := storage.runTask(ctx, storageObj, tasks, content, fileSha1)
		assert.Error(t, err)
		// 可能是 context.DeadlineExceeded 或 context.Canceled，都接受
		assert.True(t, err == context.DeadlineExceeded || err == context.Canceled)
	})

	t.Run("getPrepareUploadTask error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockStorageCli := mock.NewMockBaseV1Client(ctrl)
		storage := &StorageFile{
			storageCli: mockStorageCli,
		}

		// 使用 supermonkey mock exeTask 方法
		patchGuard := supermonkey.Patch((*StorageFile).exeTask, func(s *StorageFile, ctx context.Context, url string, datas []byte) error {
			return nil
		})
		defer patchGuard.Unpatch()

		ctx := context.Background()
		tasks := []*taskPb.FileUploadTask{
			{
				Url:    "http://example.com/upload1",
				Offset: 0,
				Length: 10,
				State:  taskPb.State_WAITING,
			},
		}

		// Mock getPrepareUploadTask 返回错误
		mockStorageCli.EXPECT().
			ListUploadTasks(gomock.Any(), gomock.Any()).
			Return(nil, fmt.Errorf("service unavailable")).
			Times(1)

		err := storage.runTask(ctx, storageObj, tasks, content, fileSha1)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "service unavailable")
	})
}

func TestStorageFile_runTask(t *testing.T) {
	content := []byte("test content")
	fileSha1 := "test-sha1"
	storageObj := &base.Storage{
		File: &file2.File{
			Name: "test-file",
		},
	}

	t.Run("basic functionality", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockStorageCli := mock.NewMockBaseV1Client(ctrl)
		storage := &StorageFile{
			storageCli: mockStorageCli,
		}

		// 使用 supermonkey mock exeTask 方法
		patchGuard := supermonkey.Patch((*StorageFile).exeTask, func(s *StorageFile, ctx context.Context, url string, datas []byte) error {
			return nil
		})
		defer patchGuard.Unpatch()

		ctx := context.Background()
		tasks := []*taskPb.FileUploadTask{
			{
				Url:    "http://example.com/upload1",
				Offset: 0,
				Length: 10,
				State:  taskPb.State_WAITING,
			},
		}

		// Mock successful completion
		mockStorageCli.EXPECT().
			ListUploadTasks(gomock.Any(), gomock.Any()).
			Return(&base.ListUploadTasks_Response{
				WaitingTasks: []*taskPb.FileUploadTask{},
			}, nil).
			Times(1)

		err := storage.runTask(ctx, storageObj, tasks, content, fileSha1)
		assert.NoError(t, err)
	})
}
