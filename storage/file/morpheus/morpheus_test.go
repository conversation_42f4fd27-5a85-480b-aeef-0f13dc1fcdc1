package morpheus

import (
	"bytes"
	"context"
	"crypto/sha1"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"log"
	"os"
	"testing"
	"time"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git"
	"git-biz.qianxin-inc.cn/zion-infra/data-foundation/apis/morpheus-api.git/generated-go/morpheus/base"
	file2 "git-biz.qianxin-inc.cn/zion-infra/file_access-api/file_access/file"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/mock"
	mock_sdk "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/mock"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk"
	storage_sdk "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func SHA1(s string) string {
	o := sha1.New()

	o.Write([]byte(s))

	return hex.EncodeToString(o.Sum(nil))
}

func cleanTestFiles(files []string) {
	for _, path := range files {
		if util.CheckFileIsExist(path) {
			err := util.RemoveFile(path)
			if err != nil {
				log.Printf("Remove test file: %v failed: %v", path, err)
			} else {
				log.Printf("Remove test file: %v", path)
			}
		}
	}
}

// func TestDownloadDonotgivePermission(t *testing.T) {
// 	framework.Init()
// 	err := os.Setenv("MORPHEUS_API_STORAGE", "*************:31992")
// 	if err != nil {
// 		t.Fatalf("set morpheus env error:%v", err)
// 	}
// 	m := new(morpheusStorage)
// 	ctx, cancel := context.WithTimeout(context.Background(), 600*time.Second)
// 	defer cancel()
// 	info, err := m.Download(ctx, util.DownloadInput{
// 		Uri:      "files/d7ba9a8fe36e8e162bcaac2823e9840d36f3cd10/storage",
// 		FilePath: "./d7ba9a8fe36e8e162bcaac2823e9840d36f3cd10",
// 		Sha1:     "d7ba9a8fe36e8e162bcaac2823e9840d36f3cd10",
// 	})
// 	assert.NoError(t, err)
// 	fmt.Printf("download info: %v\n", info)
// }

func TestDownload(t *testing.T) {
	framework.Init()
	a := assert.New(t)

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockStorageSDK := mock_sdk.NewMockStorageFileInterface(ctrl)
	m := NewStorageWithSDK(mockStorageSDK)
	tests := []struct {
		in         util.DownloadInput
		expectSize uint64
	}{
		{
			// 样本size已知使用多线程进行下载
			in: util.DownloadInput{
				Uri:      "files/b3521eac247e2a5d2f89888e249e7df3dd1ee6d0/storage",
				FilePath: "./b3521eac247e2a5d2f89888e249e7df3dd1ee6d0",
				Sha1:     "b3521eac247e2a5d2f89888e249e7df3dd1ee6d0",
				Size:     910051922,
			},
			expectSize: 910051922,
		},
	}

	// 不在这里设置期望，改为在执行循环内逐条设置
	ctx, cancel := context.WithTimeout(context.Background(), 600*time.Second)
	defer cancel()
	testFiles := make([]string, len(tests))
	for _, tt := range tests {
		// 强制走非并发下载路径
		tt.in.Size = 1
		// 构造本次用例的模拟下载内容与 sha1
		content := []byte("mock")
		sha := SHA1(string(content))
		tt.in.Sha1 = sha
		tt.expectSize = uint64(len(content))
		mockStorageSDK.EXPECT().DownloadUri(gomock.Any(), tt.in.Uri).Return(&sdk.DownloadInfo{
			Length:     uint64(len(content)),
			Sha1Same:   true,
			ActualSha1: sha,
			Code:       200,
			Content:    content,
			Body:       io.NopCloser(bytes.NewReader(content)),
		}, nil).Times(1)
		info, err := m.Download(ctx, tt.in)
		if err != nil {
			if err != nil {
				t.Fatalf("download failed: %v", err)
			}
		}
		a.Equal(nil, err)
		a.Equal(true, info.Sha1Same)
		a.Equal(tt.in.Sha1, info.ActualSha1)
		a.Equal(tt.expectSize, info.Length)
		testFiles = append(testFiles, tt.in.FilePath)
	}
	cleanTestFiles(testFiles)
}

func TestDownloadTimeout(t *testing.T) {
	defer framework.Init()()
	a := assert.New(t)

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockStorageSDK := mock_sdk.NewMockStorageFileInterface(ctrl)
	m := NewStorageWithSDK(mockStorageSDK)
	tests := []struct {
		in         util.DownloadInput
		expectSize uint64
		timeout    time.Duration
	}{
		{
			// 样本size未知使用原下载逻辑进行下载
			in: util.DownloadInput{
				Uri:      "files/1724bf26caf8cfb1f11b9ef3fc028012f32deeb2/storage",
				FilePath: "./1724bf26caf8cfb1f11b9ef3fc028012f32deeb2",
				Sha1:     "1724bf26caf8cfb1f11b9ef3fc028012f32deeb2",
			},
			expectSize: 1124712,
			timeout:    2,
		},
		/*{
			// 样本size未知使用原下载逻辑进行下载
			in: util.DownloadInput{
				Uri:      "files/75f7345e60aeaaf9fa08576e020bf3d58a03128c/storage",
				FilePath: "./75f7345e60aeaaf9fa08576e020bf3d58a03128c",
				Sha1:     "75f7345e60aeaaf9fa08576e020bf3d58a03128c",
			},
			expectSize: 455025961,
		},*/
		{
			// 样本size已知使用多线程进行下载
			in: util.DownloadInput{
				Uri:      "files/b3521eac247e2a5d2f89888e249e7df3dd1ee6d0/storage",
				FilePath: "./b3521eac247e2a5d2f89888e249e7df3dd1ee6d0",
				Sha1:     "b3521eac247e2a5d2f89888e249e7df3dd1ee6d0",
				Size:     910051922,
			},
			expectSize: 910051922,
			timeout:    1,
		},
	}

	// 设置 mock 期望 - 返回超时错误（DownloadUri）
	for _, tt := range tests {
		mockStorageSDK.EXPECT().DownloadUri(gomock.Any(), tt.in.Uri).Return(nil, status.Error(codes.DeadlineExceeded, "context deadline exceeded")).AnyTimes()
	}

	testFiles := make([]string, len(tests))
	for _, tt := range tests {
		// 强制单线程路径，避免触发 ConcurrentDownload
		tt.in.Size = 1
		ctx, cancel := context.WithTimeout(context.Background(), tt.timeout*time.Second)
		defer cancel()
		info, err := m.Download(ctx, tt.in)
		a.NotEmpty(err)
		if err != nil {
			a.Contains(err.Error(), "context deadline exceeded")
		} else {
			a.Equal(true, info.Sha1Same)
		}
		testFiles = append(testFiles, tt.in.FilePath)
	}
	cleanTestFiles(testFiles)
}

// func TestConcurrentDownload(t *testing.T) {
// 	defer framework.Init()()
// 	err := os.Setenv("MORPHEUS_API_STORAGE", "*************:30979")
// 	if err != nil {
// 		t.Fatalf("set morpheus env error:%v", err)
// 	}
// 	tests := []struct {
// 		in util.DownloadInput
// 	}{
// 		{
// 			in: util.DownloadInput{
// 				Uri:      "files/1724bf26caf8cfb1f11b9ef3fc028012f32deeb2/storage",
// 				FilePath: "./1724bf26caf8cfb1f11b9ef3fc028012f32deeb2",
// 				Sha1:     "1724bf26caf8cfb1f11b9ef3fc028012f32deeb2",
// 				Size:     1124712,
// 			},
// 		},
// 		{
// 			in: util.DownloadInput{
// 				Uri:      "files/75f7345e60aeaaf9fa08576e020bf3d58a03128c/storage",
// 				FilePath: "./75f7345e60aeaaf9fa08576e020bf3d58a03128c",
// 				Sha1:     "75f7345e60aeaaf9fa08576e020bf3d58a03128c",
// 				Size:     455025961,
// 			},
// 		},
// 		{
// 			in: util.DownloadInput{
// 				Uri:      "files/b3521eac247e2a5d2f89888e249e7df3dd1ee6d0/storage",
// 				FilePath: "./b3521eac247e2a5d2f89888e249e7df3dd1ee6d0",
// 				Sha1:     "b3521eac247e2a5d2f89888e249e7df3dd1ee6d0",
// 				Size:     910051922,
// 			},
// 		},
// 	}
// 	testFiles := make([]string, len(tests))
// 	ctx, cancel := context.WithTimeout(context.Background(), 600*time.Second)
// 	defer cancel()
// 	d := NewStorage()
// 	for _, tt := range tests {
// 		begin := time.Now()
// 		info, err := d.ConcurrentDownload(ctx, tt.in)
// 		if err != nil {
// 			t.Fatalf("concurrent download failed: %v", err)
// 		}
// 		t.Logf("download http get duration: %v, download rpc duration: %v", info.DownloadFileDuration, info.GrpcDuration)
// 		t.Logf("save content duration: %v", time.Since(begin)-info.GrpcDuration-info.DownloadFileDuration)
// 		assert.Equal(t, true, info.Sha1Same)
// 		assert.Equal(t, tt.in.Sha1, info.ActualSha1)
// 		testFiles = append(testFiles, tt.in.FilePath)
// 	}
// 	cleanTestFiles(testFiles)
// }

func BenchmarkOneThreadDownload(b *testing.B) {
	in := util.DownloadInput{
		Uri:      "files/b3521eac247e2a5d2f89888e249e7df3dd1ee6d0/storage",
		FilePath: "./b3521eac247e2a5d2f89888e249e7df3dd1ee6d0",
		Sha1:     "b3521eac247e2a5d2f89888e249e7df3dd1ee6d0",
	}
	b.Setenv("MORPHEUS_API_STORAGE", "*************:31992")
	for n := 0; n < b.N; n++ {
		downloadFile(in)
	}
}

func Benchmark_ConcurrentDownload(b *testing.B) {
	in := util.DownloadInput{
		Uri:      "files/b3521eac247e2a5d2f89888e249e7df3dd1ee6d0/storage",
		FilePath: "./b3521eac247e2a5d2f89888e249e7df3dd1ee6d0",
		Sha1:     "b3521eac247e2a5d2f89888e249e7df3dd1ee6d0",
		Size:     910051922,
	}
	b.Setenv("MORPHEUS_API_STORAGE", "*************:31992")
	for n := 0; n < b.N; n++ {
		downloadFile(in)
	}
}

func TestMorpheusStorageUpload(t *testing.T) {
	framework.Init()
	a := assert.New(t)

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockStorageSDK := mock_sdk.NewMockStorageFileInterface(ctrl)
	m := NewStorageWithSDK(mockStorageSDK)

	ctx, cancel1 := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel1()

	// sha := SHA1("UploadTest")
	sha := "10a311d33341e54cb11debafbbab64d2d6c353c7/logs/qde2m"
	content := bytes.Repeat([]byte{'A'}, 14*MB)

	// 设置 mock 期望
	uploadInfo := &sdk.UploadInfo{
		UploadFileDuration: 100 * time.Millisecond,
	}
	mockStorageSDK.EXPECT().CreateStorageWithContent(gomock.Any(), content, fmt.Sprintf("files/%s/storage", sha)).Return(uploadInfo, nil).AnyTimes()

	uploadResult, err := m.Upload(ctx, fmt.Sprintf("files/%s/storage", sha), content)
	a.Equal(nil, err)
	t.Logf("upload success, duration:%+v\n", uploadResult.UploadFileDuration)

	// 模拟 GetStorage 调用
	mockFile := &file2.File{
		Url: "http://mock-url.com/uploaded-file",
	}
	mockStorage := &base.Storage{
		File: mockFile,
	}
	mockStorageSDK.EXPECT().GetStorage(gomock.Any(), fmt.Sprintf("files/%s/storage", sha)).Return(mockStorage, nil).AnyTimes()

	fg, err := mockStorageSDK.GetStorage(ctx, fmt.Sprintf("files/%s/storage", sha))
	a.Equal(nil, err)
	t.Logf("URL : %v\n", fg.File.GetUrl())
}

func TestCheckZip(t *testing.T) {
	tests := []struct {
		path      string
		isZip     bool
		expectErr error
	}{
		{
			path:      "./testfile/test1",
			isZip:     false,
			expectErr: errors.New(fmt.Sprintf("the file: ./testfile/test1 not exist")),
		},
		{
			path:      "./testfile/test.zip",
			isZip:     true,
			expectErr: nil,
		},
		{
			path:      "./testfile/test",
			isZip:     false,
			expectErr: nil,
		},
	}

	for _, tt := range tests {
		isZip, err := checkZip(tt.path)
		assert.Equal(t, tt.isZip, isZip)
		assert.Equal(t, tt.expectErr, err)
	}
}

func TestCheckSha1(t *testing.T) {
	if !util.CheckFileIsExist("./testfile/testzip") {
		err := util.CopyFile("./testfile/test.zip", "./testfile/testzip")
		if err != nil {
			t.Fatalf("create test files failed: %v", err)
		}
	}
	tests := []struct {
		path       string
		expectSha1 string
		testSha1   string
		expectErr  error
		expectSize int64
	}{
		{
			path:       "./testfile/test",
			testSha1:   "d862a2a42c4b74ea4be56c22330f942edff04fc6",
			expectSha1: "d862a2a42c4b74ea4be56c22330f942edff04fc6",
			expectErr:  nil,
			expectSize: 4099,
		},
		{
			path:       "./testfile/testzip",
			testSha1:   "d862a2a42c4b74ea4be56c22330f942edff04fc6",
			expectSha1: "d862a2a42c4b74ea4be56c22330f942edff04fc6",
			expectErr:  nil,
			expectSize: 4099,
		},
		{
			path:       "./testfile/test1",
			expectSha1: "",
			expectErr: errors.New(fmt.Sprintf("open file: ./testfile/test1 failed: " +
				"open ./testfile/test1: no such file or directory")),
			expectSize: 0,
		},
		{
			path:       "./testfile/test",
			testSha1:   "c5d45a227061cfb6208f5bb621c2f9ab64947f59",
			expectSha1: "d862a2a42c4b74ea4be56c22330f942edff04fc6",
			expectErr: status.Errorf(codes.InvalidArgument, "actual sha1: %v not equal expect sha1: %v",
				"d862a2a42c4b74ea4be56c22330f942edff04fc6", "c5d45a227061cfb6208f5bb621c2f9ab64947f59"),
			expectSize: 4099,
		},
	}
	for _, tt := range tests {
		actualSize, actualSha1, err := checkSha1(tt.path, tt.testSha1)
		assert.Equal(t, tt.expectErr, err)
		assert.Equal(t, tt.expectSha1, actualSha1)
		assert.Equal(t, tt.expectSize, actualSize)
	}
	_ = util.RemoveFile("./testfile/testzip")
}

// func TestDownloadZipFile(t *testing.T) {
// 	framework.Init()
// 	a := assert.New(t)
// 	err := os.Setenv("MORPHEUS_API_STORAGE", "*************:30979")
// 	if err != nil {
// 		t.Fatalf("set morpheus env error:%v", err)
// 	}
// 	m := new(morpheusStorage)
// 	tests := []struct {
// 		in         util.DownloadInput
// 		expectSize uint64
// 	}{
// 		{
// 			// 测试采用并发下载模式从s3下载压缩包样本
// 			in: util.DownloadInput{
// 				Uri:      "files/a80278ec28a6a8fcc38a5bad01f25e19b33728e7/storage",
// 				FilePath: "./a80278ec28a6a8fcc38a5bad01f25e19b33728e7",
// 				Sha1:     "1724bf26caf8cfb1f11b9ef3fc028012f32deeb2",
// 				Size:     649267,
// 			},
// 			expectSize: 1124712,
// 		},
// 		{
// 			// 测试采用原逻辑从样本中心下载压缩包样本
// 			in: util.DownloadInput{
// 				Uri:      "files/a80278ec28a6a8fcc38a5bad01f25e19b33728e7/storage",
// 				FilePath: "./a80278ec28a6a8fcc38a5bad01f25e19b33728e7",
// 				Sha1:     "1724bf26caf8cfb1f11b9ef3fc028012f32deeb2",
// 			},
// 			expectSize: 1124712,
// 		},
// 	}
// 	ctx, cancel := context.WithTimeout(context.Background(), 600*time.Second)
// 	defer cancel()
// 	testFiles := make([]string, len(tests))
// 	for _, tt := range tests {
// 		info, err := m.Download(ctx, tt.in)
// 		if err != nil {
// 			t.Fatalf("download failed: %v", err)
// 		}
// 		a.Equal(nil, err)
// 		a.Equal(true, info.Sha1Same)
// 		a.Equal(tt.in.Sha1, info.ActualSha1)
// 		a.Equal(tt.expectSize, info.Length)
// 		testFiles = append(testFiles, tt.in.FilePath)
// 	}
// 	cleanTestFiles(testFiles)
// }

func downloadFile(in util.DownloadInput) {
	defer framework.Init()()
	ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
	defer cancel()
	d := NewStorage()
	downloadInfo, err := d.Download(ctx, in)
	if err != nil {
		log.Fatalf("download error: %v", err)
	}
	log.Printf("sha1一致性：%v", downloadInfo.Sha1Same)
}

func BenchmarkConcurrentDownloadZipFile(b *testing.B) {
	in := util.DownloadInput{
		Uri:      "files/b4785432661c1810059af773025b77351422ed61/storage",
		FilePath: "./b4785432661c1810059af773025b77351422ed61",
		Sha1:     "b3521eac247e2a5d2f89888e249e7df3dd1ee6d0",
		Size:     516865582,
	}
	b.Setenv("MORPHEUS_API_STORAGE", "*************:31992")
	for n := 0; n < b.N; n++ {
		downloadFile(in)
	}
}

func BenchmarkNotConcurrentDownloadZipFile(b *testing.B) {
	in := util.DownloadInput{
		Uri:      "files/b4785432661c1810059af773025b77351422ed61/storage",
		FilePath: "./b4785432661c1810059af773025b77351422ed61",
		Sha1:     "b3521eac247e2a5d2f89888e249e7df3dd1ee6d0",
	}
	b.Setenv("MORPHEUS_API_STORAGE", "*************:31992")
	for n := 0; n < b.N; n++ {
		downloadFile(in)
	}
}

// 测试下载文件失败，不论是不是压缩包，需要没有下载流程中的残留文件
// func TestFailDownloadCleanFile(t *testing.T) {
// 	framework.Init()
// 	a := assert.New(t)
// 	err := os.Setenv("MORPHEUS_API_STORAGE", "*************:30979")
// 	if err != nil {
// 		t.Fatalf("set morpheus env error:%v", err)
// 	}
// 	m := new(morpheusStorage)
// 	tests := []struct {
// 		in         util.DownloadInput
// 		expectSize uint64
// 	}{
// 		{
// 			// 测试采用并发下载模式从s3下载压缩包样本
// 			in: util.DownloadInput{
// 				Uri:      "files/a80278ec28a6a8fcc38a5bad01f25e19b33728e7/storage",
// 				FilePath: "./a80278ec28a6a8fcc38a5bad01f25e19b33728e7",
// 				Sha1:     "1724bf26caf8cfb1f11b9ef3fc028012f32deeb",
// 				Size:     649267,
// 			},
// 			expectSize: 1124712,
// 		},
// 		{
// 			// 测试采用原逻辑从样本中心下载压缩包样本
// 			in: util.DownloadInput{
// 				Uri:      "files/a80278ec28a6a8fcc38a5bad01f25e19b33728e7/storage",
// 				FilePath: "./a80278ec28a6a8fcc38a5bad01f25e19b33728e7",
// 				Sha1:     "1724bf26caf8cfb1f11b9ef3fc028012f32deeb",
// 			},
// 			expectSize: 1124712,
// 		},
// 	}
// 	ctx, cancel := context.WithTimeout(context.Background(), 600*time.Second)
// 	defer cancel()
// 	for _, tt := range tests {
// 		info, err := m.Download(ctx, tt.in)
// 		a.Equal(codes.InvalidArgument, status.Code(err))
// 		a.Equal(false, info.Sha1Same)
// 		a.Equal(false, util.CheckFileIsExist(tt.in.FilePath))
// 		a.Equal(false, util.CheckFileIsExist(fmt.Sprintf("%v.zip", tt.in.FilePath)))
// 	}
// }

// func TestQaDownload(t *testing.T) {
// 	defer framework.Init()()
// 	err := os.Setenv("MORPHEUS_API_STORAGE", "*************:31992")
// 	if err != nil {
// 		t.Fatalf("set morpheus env error:%v", err)
// 	}
// 	m := new(morpheusStorage)
// 	// 样本size未知使用原下载逻辑进行下载
// 	in := util.DownloadInput{
// 		Uri:      "files/e3781a9fc50697b0ebcf164f7a29bf5844a99b1c/storage",
// 		FilePath: "./e3781a9fc50697b0ebcf164f7a29bf5844a99b1c",
// 		Sha1:     "e3781a9fc50697b0ebcf164f7a29bf5844a99b1c",
// 		Size:     47076,
// 	}
// 	ctx, cancel := context.WithTimeout(context.Background(), 600*time.Second)
// 	defer cancel()
// 	info, err := m.Download(ctx, in)
// 	if err != nil {
// 		t.Fatalf("download failed: %v", err)
// 	}
// 	t.Logf("原sha1：%v, 实际sha1: %v", in.Sha1, info.ActualSha1)
// }

// func TestDownloadBySha1(t *testing.T) {
// 	defer framework.Init()()
// 	a := assert.New(t)
// 	err := os.Setenv("MORPHEUS_API_STORAGE", "*************:30979")
// 	if err != nil {
// 		t.Fatalf("set morpheus env error:%v", err)
// 	}
// 	sha1V := "2cc4c730747f6f9696a3e68e6ee4ebc287acf1c8"
// 	storageSdk, err := util.GetDialer().FetchStorageSDK(context.Background())
// 	a.Equal(nil, err)
// 	s, err := storageSdk.GetStorage(context.Background(), "files/28d29891c14567e0f32c507a625151c388f57cec/storage")
// 	if err != nil {
// 		t.Fatalf("get storege failed: %v", err)
// 	}
// 	t.Logf("size: %v, uri: %v, url: %v", s.GetFile().GetSize(), s.GetUri(), s.GetFile().GetUrl())
// 	s2, err := storageSdk.GetScanLogBySha1(context.Background(), sha1V)
// 	if err != nil {
// 		t.Fatalf("get GetScanLogBySha1 failed: %v", err)
// 	}
// 	t.Logf("size: %v, uri: %v, url: %v", s2.GetFile().GetSize(), s2.GetUri(), s2.GetFile().GetUrl())

// 	in := util.DownloadInput{
// 		Uri:      sha1V,
// 		FilePath: "./2cc4c730747f6f9696a3e68e6ee4ebc287acf1c8",
// 		Sha1:     sha1V,
// 		Size:     1770,
// 	}

// 	m := new(morpheusStorage)
// 	ctx, cancel := context.WithTimeout(context.Background(), 600*time.Second)
// 	defer cancel()
// 	info, err := m.Download(ctx, in)
// 	if err != nil {
// 		t.Fatalf("download failed: %v", err)
// 	}
// 	assert.Equal(t, in.Sha1, info.ActualSha1)
// 	err = util.RemoveFile(in.FilePath)
// 	assert.Equal(t, nil, err)
// }

// func TestCheckSourceAndLengthDownload(t *testing.T) {
// 	defer framework.Init()()
// 	err := os.Setenv("MORPHEUS_API_STORAGE", "10.252.12.197:30599")
// 	if err != nil {
// 		t.Fatalf("set morpheus env error:%v", err)
// 	}
// 	m := new(morpheusStorage)
// 	// 样本size未知使用原下载逻辑进行下载
// 	in := util.DownloadInput{
// 		Uri:      "files/3267bf2dca757cae130b38709fc3942a03f4b388/storage",
// 		FilePath: "./3267bf2dca757cae130b38709fc3942a03f4b388",
// 		Sha1:     "3267bf2dca757cae130b38709fc3942a03f4b388",
// 		Size:     32,
// 	}

// 	ctx, cancel := context.WithTimeout(context.Background(), 600*time.Second)
// 	defer cancel()
// 	info, err := m.Download(ctx, in)
// 	if err != nil {
// 		t.Fatalf("download failed: %v", err)
// 	}
// 	assert.Equal(t, in.Sha1, info.ActualSha1)
// 	assert.Equal(t, "s3", info.Source)
// 	assert.Equal(t, in.Size, info.Length)
// 	err = util.RemoveFile(in.FilePath)
// 	assert.Equal(t, nil, err)
// }

func Test_Upload(t *testing.T) {
	defer framework.Init()()
	tests := []struct {
		UploadInfo *storage_sdk.UploadInfo
		errUpload  error
		errWant    error
	}{
		{
			UploadInfo: &storage_sdk.UploadInfo{},
		},
		{
			UploadInfo: &storage_sdk.UploadInfo{},
			errUpload:  fmt.Errorf("test"),
			errWant:    fmt.Errorf("test"),
		},
	}

	for _, test := range tests {
		ctrl := gomock.NewController(t)
		dailer := mock.NewMockDialer(ctrl)
		storageSrv := &storage_sdk.StorageFile{}
		dailer.EXPECT().FetchStorageSDK(gomock.Any()).Return(
			storageSrv, nil,
		).AnyTimes()

		// The following argument is required to patch this method.
		// go test  -gcflags=all=-l
		patches := gomonkey.ApplyMethod(storageSrv, "CreateStorageWithContent", func(s *sdk.StorageFile, ctx context.Context, content []byte, storageUri string) (*sdk.UploadInfo, error) {
			return test.UploadInfo, test.errUpload
		})
		defer patches.Reset()

		// one Patches object for all
		patches.ApplyFunc(util.GetDialer, func() util.Dialer {
			return dailer
		})

		c := morpheusStorage{}
		ctx, cancel := context.WithTimeout(context.Background(), time.Millisecond*100)
		defer cancel()
		_, err := c.Upload(ctx, "", []byte{})
		if test.errWant != nil {
			assert.Equal(t, test.errWant, err)
		} else {
			assert.Nil(t, err)
		}

	}
}

func Test_setConcurrentNum(t *testing.T) {
	tests := []struct {
		size        uint64
		concurrency int
		piece       uint64
	}{
		{
			size:        50 * MB,
			concurrency: 2,
			piece:       50 * MB,
		},
		{
			size:        200 * MB,
			concurrency: 3,
			piece:       100 * MB,
		},
		{
			size:        1.5 * GB,
			concurrency: 8,
			piece:       200 * MB,
		},
		{
			size:        2.5 * GB,
			concurrency: 9,
			piece:       300 * MB,
		},
		{
			size:        3.5 * GB,
			concurrency: 9,
			piece:       400 * MB,
		},
		{
			size:        4.5 * GB,
			concurrency: 10,
			piece:       500 * MB,
		},
		{
			size:        5.5 * GB,
			concurrency: 10,
			piece:       600 * MB,
		},
		{
			size:        6.5 * GB,
			concurrency: 10,
			piece:       700 * MB,
		},
		{
			size:        7.5 * GB,
			concurrency: 10,
			piece:       800 * MB,
		},
		{
			size:        8.5 * GB,
			concurrency: 10,
			piece:       900 * MB,
		},
	}

	for _, test := range tests {

		concurrency, piece := setConcurrentNum(test.size)
		assert.Equalf(t, test.concurrency, concurrency, "size:%v, concurrency:%v, piece:%v", test.size/GB, test.concurrency, test.piece/MB)
		assert.Equal(t, test.piece, piece, "size:%v, concurrency:%v, piece:%v", test.size/GB, test.concurrency, test.piece/MB)
	}
}

func Test_cleanFile(t *testing.T) {
	t.Run("test", func(t *testing.T) {
		name := "test"
		f, _ := os.Create(name)
		f.Close()
		cleanFile(name)
		_, err := os.Stat(name)
		assert.Equal(t, true, os.IsNotExist(err))
	})

	t.Run("test", func(t *testing.T) {
		name := "test.zip"
		f, _ := os.Create(name)
		f.Close()
		cleanFile("test")
		_, err := os.Stat(name)
		assert.Equal(t, true, os.IsNotExist(err))
	})
}
