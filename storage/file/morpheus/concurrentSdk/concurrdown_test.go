package concurrentSdk

import (
	"context"
	"log"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestDownloader_Download(t *testing.T) {
	a := assert.New(t)

	tests := []struct {
		url         interface{}
		path        string
		err         string
		size        uint64
		blockSize   uint64
		concurrency int
	}{
		{
			url:         9999,
			path:        "/tmp/my_downloaded_package",
			err:         "input type isn't right",
			size:        9999,
			blockSize:   4096 * 1024,
			concurrency: 3,
		},
		{
			url:         "https://paoding.b.qianxin-inc.cn/not/found",
			path:        "/tmp/my_downloaded_package",
			err:         "total bytes is zero",
			size:        0,
			blockSize:   1024,
			concurrency: 3,
		},
		// 416 Range Not Satisfiable
		{
			url:         "https://paoding.b.qianxin-inc.cn/not/found",
			path:        "/tmp/my_downloaded_package",
			err:         "http code: 416",
			size:        9999,
			blockSize:   1024,
			concurrency: 3,
		},
		{
			url:         "https://mirrors.cloud.tencent.com/pypi/packages/4b/0d/53aea75710af4528a25ed6837d71d117602b01946b307a3912cb3cfcbcba/retry-0.9.2-py2.py3-none-any.whl",
			path:        "/tmp/my_downloaded_package",
			err:         "",
			size:        7986,
			blockSize:   4096 * 1024,
			concurrency: 3,
		},
		{
			url:         "https://mirrors.cloud.tencent.com/pypi/packages/68/dc/31397ed1d4594a2b1495bc4e4cdc4fdb80c02a1e2593b45ac56a58e9b431/grpcio-1.48.2-cp36-cp36m-manylinux_2_17_x86_64.manylinux2014_x86_64.whl",
			path:        "/tmp/my_downloaded_package",
			err:         "",
			size:        4572190,
			blockSize:   4096 * 1024,
			concurrency: 3,
		},
	}

	for _, tt := range tests {
		log.Printf("case: %+v", tt)
		ctx, cancel1 := context.WithTimeout(context.Background(), 130*time.Second)
		defer cancel1()

		downloader := NewDownloader(tt.size, tt.blockSize, tt.concurrency)
		file, err := os.Create(tt.path)
		a.Equal(nil, err)
		defer os.Remove(tt.path)
		defer file.Close()
		info, err := downloader.Download(ctx, file, tt.url)
		log.Printf("info: %+v, error: %v", info, err)
		if err != nil {
			a.NotEmpty(tt.err)
			a.ErrorContains(err, tt.err)
		} else {
			fi, err := file.Stat()
			a.Equal(nil, err)
			a.Equal(tt.size, uint64(fi.Size()), "file size equal")
			a.Equal(tt.size, info.Length)
		}
	}
}
