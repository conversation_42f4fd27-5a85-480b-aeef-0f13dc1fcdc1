package concurrentSdk

import (
	"context"
	"crypto/tls"
	"errors"
	"fmt"
	"io"
	"net"
	"net/http"
	"os"
	"sync"
	"time"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"
)

const MaxRetries = 3

type Downloader interface {
	Download(ctx context.Context, file *os.File, input interface{}) (*DownloadInfo, error)
}

type DownloadInfo struct {
	Length               uint64
	DownloadFileDuration time.Duration
	DownloadFileErr      error
}

type downloader struct {
	ctx context.Context

	file *os.File
	wg   sync.WaitGroup
	m    sync.Mutex

	totalBytes   uint64 // 样本实际的总大小
	piece        uint64
	concurrency  int
	pos          uint64
	written      int64
	err          error
	downloadInfo *DownloadInfo

	partMaxRetries int
}

func NewDownloader(totalBytes, piece uint64, concurrency int) Downloader {
	d1 := &downloader{
		totalBytes:     totalBytes,
		piece:          piece,
		concurrency:    concurrency,
		partMaxRetries: MaxRetries,
		downloadInfo:   &DownloadInfo{},
	}
	d := &httpDownloader{
		downloader:  d1,
		dialTimeout: time.Second * 30,
	}
	return d
}

func (d *downloader) getErr() error {
	d.m.Lock()
	defer d.m.Unlock()

	return d.err
}

func (d *downloader) setErr(e error) {
	d.m.Lock()
	defer d.m.Unlock()

	d.err = e
}

func (d *downloader) incrWritten(n int64) {
	d.m.Lock()
	defer d.m.Unlock()

	d.downloadInfo.Length += uint64(n)
}

func (d *downloader) incrDuration(t time.Duration) {
	d.m.Lock()
	defer d.m.Unlock()

	d.downloadInfo.DownloadFileDuration += t
}

type block struct {
	file  *os.File
	start uint64 // 每一片的起始位置
	size  uint64 // 每一片的大小
}

type httpDownloader struct {
	*downloader

	url         string
	clientOnce  sync.Once
	httpClient  *http.Client
	dialTimeout time.Duration
}

func (d *httpDownloader) Download(ctx context.Context, file *os.File, input interface{}) (*DownloadInfo, error) {
	in, ok := input.(string)
	if !ok {
		d.err = errors.New("input type isn't right")
		return d.downloadInfo, d.err
	}

	if d.totalBytes <= 0 {
		d.err = errors.New("total bytes is zero")
		return d.downloadInfo, d.err
	}

	d.ctx = ctx
	d.file = file
	d.url = in

	ch := make(chan block)
	for i := 0; i < d.concurrency; i++ {
		d.wg.Add(1)
		go d.downloadPart(ctx, ch)
	}

	for d.getErr() == nil {
		if d.pos >= d.totalBytes {
			break
		}

		ch <- block{file: d.file, start: d.pos, size: d.piece}
		d.pos += d.piece
	}

	close(ch)
	d.wg.Wait()
	return d.downloadInfo, d.err
}

func (d *httpDownloader) downloadPart(ctx context.Context, ch chan block) {
	defer d.wg.Done()
	for {
		chunk, ok := <-ch
		if !ok {
			break
		}
		if d.getErr() != nil {
			continue
		}

		if err := d.downloadChunk(ctx, chunk); err != nil {
			d.setErr(err)
		}
	}
}

func (d *httpDownloader) getHttpClient() *http.Client {
	d.clientOnce.Do(func() {
		d.httpClient = &http.Client{
			Transport: &http.Transport{
				DialContext: (&net.Dialer{
					Timeout: d.dialTimeout,
				}).DialContext,
				DisableKeepAlives: true,
				TLSClientConfig:   &tls.Config{InsecureSkipVerify: true},
			},
		}
	})
	return d.httpClient
}

func (d *httpDownloader) downloadChunk(ctx context.Context, chunk block) error {
	var n int
	var retryErr error
	var timeout time.Duration
	deal, _ := ctx.Deadline()
	totalTime := deal.Sub(time.Now())
	// 如果通过uri获取样本的size成功，那么取（0.8 * 总时间 - 一个样本阶梯时间）与（一个样本阶梯时间）二者中的最小值作为超时时间
	// 如果需要重试，则将剩余的所有时间：（0.8 * 总时间） - timeout ==>这个值肯定是比上面的超时时间长的，就保证了超时情况下的时间一定是大的
	// 如果通过uri获取样本大小失败，则第一次将超时时间设置为：总时间 * 30%，这样超时后将剩余的70%全部给它
	// TODO 此时的样本真实size不通过RPC调用去将获取了而是采用上游传下来的size
	log.Debugf("totalTime: %v, d.totalBytes: %v", totalTime, d.totalBytes)
	if d.totalBytes != 0 {
		ladder := util.GetDownloadLadderTime(d.totalBytes) * time.Second
		diff := totalTime - ladder
		// ensure a positive timeout; prefer the smaller between remaining time and ladder time
		if diff <= 0 {
			timeout = ladder
		} else if diff < ladder {
			timeout = diff
		} else {
			timeout = ladder
		}
		log.Debugf("with totalBytes timeout: %v", timeout)
	} else {
		timeout = time.Duration(float64(totalTime) * 0.3)
		log.Debugf("without totalBytes timeout: %v", timeout)
	}

	firstCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()
	resp, httpErr := d.httpGet(firstCtx, chunk)
	if httpErr != nil {
		d.downloadInfo.DownloadFileErr = httpErr
		return httpErr
	}

	code := resp.StatusCode
	// 服务返回状态码若是100类、200类、300类表示正常下载；
	// 若是400类状态码表示服务找不到了或者是服务禁止访问等，那么直接返回了；
	// 若是500类状态码表示服务器正忙等，说明此时请求应该来慢点了，加大重试的间隔时间和整体的重试时间；
	// 其他未知错误码执行正常重试逻辑；
	if code >= 100 && code < 400 {
		retryErr = nil
	} else if code >= 400 && code < 500 {
		d.downloadInfo.DownloadFileErr = errors.New(fmt.Sprintf("http code: %v", code))
		return errors.New(fmt.Sprintf("http code: %v", code))
	} else if code >= 500 && code < 600 {
		resp, retryErr = d.retryHttpGet(ctx, chunk, 3, 2*time.Second)
	} else {
		resp, retryErr = d.retryHttpGet(ctx, chunk, 2, time.Second)
	}
	if retryErr != nil {
		d.downloadInfo.DownloadFileErr = retryErr
		return retryErr
	}

	n, writeErr := saveAt(d.file, resp.Body, chunk.start, resp.ContentLength)
	if writeErr != nil {
		return errors.New(fmt.Sprintf("[n:%v, offset: %v]saveAt error: %v", n, chunk.start, writeErr))
	}
	resp.Body.Close()
	d.incrWritten(int64(n))
	return nil
}

func saveAt(file *os.File, in io.ReadCloser, off uint64, contentLength int64) (int, error) {
	buf := make([]byte, 16*1024)
	var wn int
	var we error
	for {
		// 单次写入文件的内容大小，该值与从http body中读取到的内容大小理论上每次应该都是一样的
		var n int
		rn, err := in.Read(buf)
		if rn > 0 {
			n, we = file.WriteAt(buf[:rn], int64(off))
			if we != nil {
				return wn, errors.New(fmt.Sprintf("write file failed: %v", we))
			}
			off += uint64(n)
			wn += n
		}
		if err != nil {
			if err == io.EOF {
				break
			}
			// 针对UnexpectedEOF的错误处理逻辑，主要是在进行并发下载时，在HTTP Header中添加Range关键字，每次只Get：start-end bytes
			// 的内容回来，但是在采用该方式时，在一些复杂的或者特定的环境场景中，会出现实际上从：HTTP IO 流中已经读取到完整内容了，但是依然
			// 返回UnexpectedEOF错误，而不是EOF，具体http包底层实现处理行为，暂时还不清除，针对这一问题，采用下面这种方式处理，err为：
			// UnexpectedEOF，同时最后一次读取到的内容和写入的内容相等且落盘写入的总大小和该段的content length 相等，则认定
			// UnexpectedEOF不是错误为有效读取
			log.Debugf("[%v] err: %v, rn: %v, n: %v, contentLength: %v", err == io.ErrUnexpectedEOF, err, rn, n, contentLength)
			if err == io.ErrUnexpectedEOF && rn == n && int64(wn) == contentLength {
				break
			}
			return rn, errors.New(fmt.Sprintf("read http body failed: %v", err))
		}
	}
	return wn, nil
}

func (d *httpDownloader) httpGet(ctx context.Context, chunk block) (*http.Response, error) {
	req, httpErr := http.NewRequestWithContext(ctx, "GET", d.url, nil)
	if httpErr != nil {
		return nil, httpErr
	}
	req.Header.Set("Range", fmt.Sprintf("bytes=%d-%d", chunk.start, chunk.start+chunk.size-1))
	begin := time.Now()
	resp, httpErr := d.getHttpClient().Do(req)
	d.incrDuration(time.Since(begin))
	if httpErr != nil {
		return nil, httpErr
	}
	return resp, nil
}

func (d *httpDownloader) retryHttpGet(ctx context.Context, chunk block, count int, waitTime time.Duration) (*http.Response, error) {
	var resp *http.Response
	var httpErr error
	for i := 0; i < count; i++ {
		time.Sleep(waitTime)
		resp, httpErr = d.httpGet(ctx, chunk)
		if httpErr != nil {
			continue
		}
		if resp.StatusCode >= 100 && resp.StatusCode < 400 {
			break
		}
	}
	if httpErr != nil {
		return nil, httpErr
	}
	if resp.StatusCode >= 100 && resp.StatusCode <= 400 {
		return resp, nil
	}
	return resp, errors.New(fmt.Sprintf("http code: %v", resp.StatusCode))
}
