//go:generate mockgen -destination mock/mock_storage.go git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage Storage
package storage

import (
	"context"
	entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"
)

type Storage interface {
	Download(ctx context.Context, input util.DownloadInput) (*sdk.DownloadInfo, error)
	Upload(ctx context.Context, t *entities.Task, filepath string)  (*sdk.UploadInfo, error)
}
