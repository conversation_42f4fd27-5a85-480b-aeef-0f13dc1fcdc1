package dnscache

import (
	"context"
	"crypto/tls"
	"fmt"
	"log"
	"net"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/Rican7/retry"
	"github.com/Rican7/retry/strategy"
	"github.com/stretchr/testify/require"
)

func getHttpClient(resolver *Resolver, isErr bool) *http.Client {
	dialer := &net.Dialer{
		Timeout: 1 * time.Second,
	}
	transport := &http.Transport{
		DialContext: func(ctx context.Context, network, address string) (net.Conn, error) {
			separator := strings.LastIndex(address, ":")
			domain := address[:separator]
			if isErr {
				domain = "test.err.com"
			}
			ip := ""
			err := retry.Retry(func(attempt uint) error {
				start := time.Now()
				resolveIp, err := resolver.FetchOneString(domain)
				ip = resolveIp
				fmt.Printf("dns parser duration: %v us\n", time.Since(start).Microseconds())
				return err
			}, strategy.Limit(3), strategy.Wait(10*time.Millisecond))
			if err != nil {
				return nil, err
			}
			completeAddress := ip + address[separator:]
			conn, err := dialer.DialContext(ctx, network, completeAddress)
			if err != nil {
				log.Printf("link ip: %s, err: %v", completeAddress, err)
			}
			return conn, err
		},
		DisableKeepAlives: true,
		TLSClientConfig:   &tls.Config{InsecureSkipVerify: true},
	}
	return &http.Client{
		Transport: transport,
	}
}

func requestGet(client *http.Client, url string) (*http.Response, error) {
	request, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.Panicf("new request failed: %v", err)
	}
	request = request.WithContext(context.Background())
	res, err := client.Do(request)
	return res, err
}

func TestDnsCache(t *testing.T) {
	testData := []struct {
		Host    string
		IsError bool
	}{
		{
			// 正常情况下,能解析成功也能缓存起来
			Host:    "git-biz.qianxin-inc.cn",
			IsError: false,
		},
		{
			// dns异常情况下,不解析成功也不能缓存起来
			Host:    "git-biz.qianxin-inc.cn",
			IsError: true,
		},
	}
	for _, data := range testData {
		url := `https://git-biz.qianxin-inc.cn`
		resolver := New(time.Second * 5)
		client := getHttpClient(resolver, data.IsError)
		_, err := requestGet(client, url)
		ips, ok := resolver.cache[data.Host]
		if data.IsError {
			require.Error(t, err)
			require.False(t, ok)
		} else {
			require.NoError(t, err)
			require.True(t, ok)
			log.Print(ips)
		}
	}
}
