package scanadapter

import (
	"context"

	api_scan "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git"
)

type ScanService interface {
	Ping(ctx context.Context) (*PingResponse, error)
	Scan(ctx context.Context, req *ScanRequest) (*ScanResponse, error)
	Update(ctx context.Context, req *UpdateRequest) (*Version, error)
	Reload(ctx context.Context, req *ReloadRequest) (*Version, error)
	Exit(ctx context.Context) error
}

type ScanRequest struct {
	Operations []ScanOperation
}

type ScanOperation struct {
	Path          string
	PathPrefixVar string
	Parameter     string
	OutputDir     string
}

type ScanResponse struct {
	Results map[string]*ScanResult
}

type ScanResult struct {
	Code    ScanCode
	Name    string
	Message string
	Version *Version
}

type Version struct {
	EngineVersion  string
	PatternVersion string
}

type PingResponse struct {
	Name           string
	AssetName      string
	StartupSeconds int64
	State          State
	Version        *Version
}

type UpdateRequest struct {
	Name string
}

type ReloadRequest struct {
	Name       string
	ReloadType ReloadType
	Config     string
}

type ScanCode int32

const (
	ScanCode_NOTSCAN          ScanCode = 0
	ScanCode_OK               ScanCode = 1
	ScanCode_DOWNLOAD_FAILURE ScanCode = 2
	ScanCode_SCAN_FAILURE     ScanCode = 3
	ScanCode_SHA1_FAILURE     ScanCode = 4
	ScanCode_SIZE_FAILURE     ScanCode = 5
	ScanCode_INVALID_PARAM    ScanCode = 6
	ScanCode_UPLOAD_FAILURE   ScanCode = 7
	ScanCode_FILE_UNSUPPORT   ScanCode = 8
	ScanCode_OTHER            ScanCode = 99
)

type State int32

const (
	State_UNKNOWN     State = 0
	State_IDLE        State = 1
	State_STARTUP     State = 2
	State_SCANNING    State = 3
	State_UPDATING    State = 4
	State_RELOADING   State = 5
	State_TERMINATING State = 9
)

type ReloadType int32

const (
	ReloadType_UNKNOWNTYPE ReloadType = 0
	ReloadType_VIRUS       ReloadType = 1
	ReloadType_CONFIG      ReloadType = 2
)

// Converters for callers that still use kamala types in some places
func ConvertOperationsToUnified(ops []*api_scan.ScanOperation) []ScanOperation {
	if len(ops) == 0 {
		return nil
	}
	unified := make([]ScanOperation, 0, len(ops))
	for _, op := range ops {
		if op == nil {
			continue
		}
		unified = append(unified, ScanOperation{
			Path:          op.GetPath(),
			PathPrefixVar: op.GetPathPrefixVar(),
			Parameter:     op.GetParameter(),
			OutputDir:     op.GetOutputDir(),
		})
	}
	return unified
}

func ConvertUnifiedResultsToKamala(results map[string]*ScanResult) map[string]*api_scan.ScanResult {
	if len(results) == 0 {
		return map[string]*api_scan.ScanResult{}
	}
	out := make(map[string]*api_scan.ScanResult, len(results))
	for k, v := range results {
		if v == nil {
			continue
		}
		out[k] = &api_scan.ScanResult{
			Code:    api_scan.ScanCode(v.Code),
			Name:    v.Name,
			Message: v.Message,
			Version: &api_scan.Version{EngineVersion: v.Version.EngineVersion, PatternVersion: v.Version.PatternVersion},
		}
	}
	return out
}

func ConvertUnifiedVersionToKamala(v *Version) *api_scan.Version {
	if v == nil {
		return nil
	}
	return &api_scan.Version{EngineVersion: v.EngineVersion, PatternVersion: v.PatternVersion}
}
