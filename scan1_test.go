package main

import (
	"context"
	"errors"
	"fmt"
	"io/fs"
	"math"
	"os"
	"path"
	"path/filepath"
	"reflect"
	"strconv"
	"testing"
	"time"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git"
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	worker_api "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/worker.git/api"

	scanadapter "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/adapters/scan"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers"
	mock_controllers "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers/mock"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/message"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/mock"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk"
	mock_storage "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/mock"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/proxy"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"
	"github.com/agiledragon/gomonkey/v2"
	sm "github.com/cch123/supermonkey"
	"github.com/panjf2000/ants"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func Test_deleteFailTask(t *testing.T) {

	s := &scan{
		resultChan: make(chan *LocalTask, 10),
	}

	tests := []struct {
		err error
	}{
		{
			err: status.Errorf(codes.NotFound, "testNotfound"),
		},
		{
			err: status.Errorf(codes.Unavailable, "testNotfound"),
		},
	}

	for _, test := range tests {

		var task = make(map[string]*LocalTask)
		// scanMap := make([]*kamala.ScanOperation, 0)

		task["0"] = &LocalTask{task: &entities.Task{}}
		task["1"] = &LocalTask{task: &entities.Task{}}
		task["2"] = &LocalTask{task: &entities.Task{}}
		task["3"] = &LocalTask{task: &entities.Task{}}

		scanMap := []scanadapter.ScanOperation{
			{Path: "0"},
			{Path: "1"},
			{Path: "2"},
		}

		s.deleteFailTask(scanMap, task, test.err)
		fmt.Println(len(task))
		assert.Equal(t, 1, len(task))
		_, ok := task["3"]
		assert.Equal(t, true, ok)
	}

}

func Test_dealScanFailed(t *testing.T) {
	var task = make(map[string]*LocalTask)
	// var scanMap map[string]string = make(map[string]string)

	task["0"] = &LocalTask{task: &entities.Task{}}
	task["1"] = &LocalTask{task: &entities.Task{}}
	task["2"] = &LocalTask{task: &entities.Task{}}
	task["3"] = &LocalTask{task: &entities.Task{}}

	scanMap := []scanadapter.ScanOperation{
		{Path: "0"},
		{Path: "1"},
		{Path: "2"},
	}

	s := &scan{
		resultChan: make(chan *LocalTask, 10),
	}

	s.dealScanFailed(task, scanMap, errors.New("err"))
	fmt.Println(len(task))
	assert.Equal(t, 1, len(task))
	_, ok := task["3"]
	assert.Equal(t, true, ok)

}

func Test_getAvgUpdateTime(t *testing.T) {
	tests := []struct {
		assert *entities.Asset
		want   int
	}{
		{
			assert: &entities.Asset{
				Name: "test_scanner",
				UpdateFeature: &entities.UpdateFeature{
					VersionDepot: &entities.VersionDepot{
						Versions: []*entities.Version{},
					},
				},
			},
			want: 0,
		},
		{
			assert: &entities.Asset{
				Name: "test_scanner",
				UpdateFeature: &entities.UpdateFeature{
					VersionDepot: &entities.VersionDepot{
						Versions: []*entities.Version{
							{
								UpdateDuration: "1.0",
							},
						},
					},
				},
			},
			want: 1,
		},
	}

	for _, test := range tests {
		ctrl := gomock.NewController(t)
		assetMock := mock_controllers.NewMockAssetController(ctrl)

		assetMock.EXPECT().GetAsset().Return(
			test.assert,
		).AnyTimes()

		s := &scan{
			assetController: assetMock,
		}

		res := s.getAvgUpdateTime()

		assert.Equal(t, test.want, res)
	}

}

func Test_isUniteUpdate(t *testing.T) {

	tests := []struct {
		assert *entities.Asset
		want   bool
	}{
		{
			assert: &entities.Asset{
				Name: "test_scanner",
				UpdateFeature: &entities.UpdateFeature{
					Kind: entities.UpgradeKind_DARWIN,
				},
			},
			want: true,
		},
		{
			assert: &entities.Asset{
				Name: "test_scanner",
				UpdateFeature: &entities.UpdateFeature{
					Kind: entities.UpgradeKind_UPDATER,
				},
			},
			want: true,
		},
		{
			assert: &entities.Asset{
				Name: "test_scanner",
				UpdateFeature: &entities.UpdateFeature{
					Kind: entities.UpgradeKind_SELF,
				},
			},
			want: false,
		},
	}

	for _, test := range tests {
		ctrl := gomock.NewController(t)
		assetMock := mock_controllers.NewMockAssetController(ctrl)

		assetMock.EXPECT().GetAsset().Return(
			test.assert,
		).AnyTimes()

		s := &scan{
			assetController: assetMock,
		}

		res := s.isUniteUpdate()

		assert.Equal(t, test.want, res)
	}

}

func Test_isUseDisk(t *testing.T) {

	tests := []struct {
		emptyDirSize uint64
		estimateSize uint64
		fileDirUsed  int64
		logDirUsed   int64
		taskSize     uint64
		want         bool
	}{
		{
			emptyDirSize: 50,
			estimateSize: 50,
			taskSize:     0,
			want:         true,
		},
		{
			emptyDirSize: 50,
			estimateSize: 50,
			taskSize:     1,
			want:         true,
		},
		{
			emptyDirSize: 40,
			estimateSize: 50,
			taskSize:     10,
			want:         true,
		},
		{
			emptyDirSize: 10,
			estimateSize: 0,
			taskSize:     10,
			want:         true,
		},
		{
			emptyDirSize: 40,
			estimateSize: 10,
			taskSize:     30,
			want:         true,
		},
		{
			emptyDirSize: 100,
			estimateSize: 0,
			taskSize:     10,
			want:         false,
		},
	}

	for _, test := range tests {

		ctrl := gomock.NewController(t)
		assetMock := mock_controllers.NewMockAssetController(ctrl)

		assetMock.EXPECT().GetAsset().Return(
			&entities.Asset{
				ScanFeature: &entities.ScanFeature{
					MaxFetchSize: 1000 * 1024 * 1024,
				},
			},
		).AnyTimes()

		pg := sm.Patch(util.GetDirSize, func(uri string) (uint64, error) {
			return 10, nil
		})
		defer pg.Unpatch()

		s := &scan{
			assetController: assetMock,
			fileDir:         "file",
			logDir:          "log",
			emptyDirSize:    test.emptyDirSize,
			estimateSize:    test.estimateSize,
		}

		res := s.isUseDisk(test.taskSize)

		assert.Equal(t, test.want, res)

	}
}

func Test_chooseDirList(t *testing.T) {
	tests := []struct {
		taskSize uint64
		want     int
	}{
		{
			taskSize: 1 * GB,
			want:     15,
		},
		{
			taskSize: 5 * GB,
			want:     15,
		},
		{
			taskSize: 10 * GB,
			want:     3,
		},
		{
			taskSize: 11 * GB,
			want:     1,
		},
	}

	for _, test := range tests {

		res := chooseDirList(test.taskSize)

		assert.Equal(t, test.want, len(res))
	}
}

func Test_removeInvalidFile(t *testing.T) {

	plusDir := "plusDirTest"
	dirName := "test"

	err := os.MkdirAll("plusDirTest/test", 0775)
	if err != nil {
		t.Error(err.Error())
	}
	f, err := os.Create(path.Join(plusDir, dirName, "test"))
	if err != nil {
		t.Error(err.Error())
	}
	f.Close()
	f, err = os.Create(path.Join(plusDir, dirName, LockFile))
	if err != nil {
		t.Error(err.Error())
	}
	f.Close()

	s := &scan{
		plusFileDir: plusDir,
	}

	s.removeInvalidFile(dirName)

	_, err = os.Stat(path.Join(plusDir, dirName, LockFile))

	if err != nil {
		t.Errorf("removeInvalidFile 删除了用来锁文件夹的文件")
	}
	_, err = os.Stat(path.Join(plusDir, dirName, "test"))

	assert.Equal(t, true, os.IsNotExist(err), "removeInvalidFile 没有删除垃圾文件")

	os.RemoveAll(plusDir)
}

func Test_defaultScanMap(t *testing.T) {
	tests := []struct {
		tasks map[string]*LocalTask
		want  int
	}{
		{
			tasks: map[string]*LocalTask{
				"1": {
					task: &entities.Task{Priority: entities.Priority_VERY_HIGH},
				},
				"2": {
					task: &entities.Task{Priority: entities.Priority_VERY_LOW},
				},
			},
			want: 1,
		},

		{
			tasks: map[string]*LocalTask{
				"1": {
					task: &entities.Task{Priority: entities.Priority_VERY_LOW},
				},
				"2": {
					task: &entities.Task{Priority: entities.Priority_VERY_LOW},
				},
			},
			want: 2,
		},
	}

	for _, test := range tests {
		res := defaultScanOperations(test.tasks)
		assert.Equal(t, test.want, len(res))
	}
}

func Test_generateRelativePath(t *testing.T) {
	plusDir := "plusTest"
	tests := []struct {
		isUsePlus bool
		src       string
		want      string
	}{
		{
			isUsePlus: true,
			src:       "plusTest/test",
			want:      "test",
		},
		{
			isUsePlus: false,
			src:       "plusTest/test",
			want:      "test",
		},
	}

	for _, test := range tests {
		s := &scan{
			plusFileDir: plusDir,
		}

		res := s.generateRelativePath(test.isUsePlus, test.src)
		assert.Equal(t, test.want, res)
	}
}

func Test_emptyDirIsInsufficient(t *testing.T) {
	tests := []struct {
		MaxFetchSize  uint64
		SharedDirSize uint64
		want          bool
	}{
		{

			MaxFetchSize:  10,
			SharedDirSize: 50,
			want:          true,
		},
		{

			MaxFetchSize:  0,
			SharedDirSize: 50,
			want:          false,
		},
	}

	for _, test := range tests {
		ctrl := gomock.NewController(t)
		assetMock := mock_controllers.NewMockAssetController(ctrl)

		assetMock.EXPECT().GetAsset().Return(
			&entities.Asset{
				ScanFeature: &entities.ScanFeature{
					MaxFetchSize: test.MaxFetchSize,
				},
				DeployFeature: &entities.DeployFeature{
					SharedDirSize: test.SharedDirSize,
				},
			},
		).AnyTimes()

		pg := sm.Patch(util.GetDirSize, func(uri string) (uint64, error) {
			return 10, nil
		})
		defer pg.Unpatch()

		s := &scan{
			assetController: assetMock,
			fileDir:         "file",
			logDir:          "log",
			emptyDirSize:    100,
		}

		res := s.emptyDirIsInsufficient()
		assert.Equal(t, test.want, res)
	}
}

func pushTaskToChan(ch chan *LocalTask, tasks []*LocalTask) {
	for _, task := range tasks {
		ch <- task
		time.Sleep(100 * time.Millisecond)
	}

}

func Test_loopUpload(t *testing.T) {
	defer framework.Init()()
	tests := []struct {
		StoreUri string
		want     int
	}{
		{
			StoreUri: "message||files/3e3c3c2fd417ed5dd39b06f3a145b0712164f31b/logs/qowl/storage",
			want:     1,
		},
		{
			StoreUri: "files/3e3c3c2fd417ed5dd39b06f3a145b0712164f31b/logs/qowl/storage",
			want:     1,
		},
	}

	tasks := []*LocalTask{}

	for i, test := range tests {
		tasks = append(tasks, &LocalTask{
			task: &entities.Task{
				Name:        "task" + strconv.Itoa(i),
				Size:        uint64(i) * 8 * MB,
				ScanTimeout: int64(60),
				StoreUri:    test.StoreUri,
			},
			totalTime: util.GetDownloadLadderTime(uint64(i)*8*MB)*time.Second*2 + time.Duration(math.Pow10(9)*60),
			lastTime:  util.GetDownloadLadderTime(uint64(i)*8*MB)*time.Second*2 + time.Duration(math.Pow10(9)*60) - 3*time.Second,
		})
	}

	pool, err := ants.NewPool(5)
	if err != nil {
		log.Errorf("failed to get new pool for storage, err: %v", err)
		assert.Equal(t, err, nil)
	}
	pool.Running()
	defer pool.Release()
	mockStorage := mock_storage.NewMockStorage(gomock.NewController(t))

	mockStorage.EXPECT().Upload(gomock.Any(), gomock.Any(), gomock.Any()).Return(
		&sdk.UploadInfo{}, nil,
	).AnyTimes()

	pg := sm.Patch(proxy.ChooseStorage, func(t *entities.Task, filepath string) bool {
		return true
	})
	defer pg.Unpatch()

	pg2 := sm.Patch(message.Upload, func(ctx context.Context, t *entities.Task, filepath string) (*sdk.UploadInfo, error) {
		return &sdk.UploadInfo{}, nil
	})
	defer pg2.Unpatch()

	s := &scan{
		LoopController: util.LoopController{},

		storageController: mockStorage,
		taskChan:          make(chan *LocalTask, 10),
		resultChan:        make(chan *LocalTask, 10),
		logChan:           make(chan *LocalTask, 10),
		fileDir:           "test_file_dir",
		logDir:            "test_log_dir",
		storagePool:       pool,
	}

	go pushTaskToChan(s.logChan, tasks)

	go s.loopUpload()
	s.Finalize()

}

func Test_blockCheckSpaceCondition(t *testing.T) {

	ctrl := gomock.NewController(t)
	assetMock := mock_controllers.NewMockAssetController(ctrl)

	assetMock.EXPECT().GetAsset().Return(
		&entities.Asset{
			ScanFeature: &entities.ScanFeature{
				MaxFetchSize: 200,
			},
		},
	).AnyTimes()

	s := scan{
		estimateSize:      100,
		assetController:   assetMock,
		spaceInsufficient: true,
	}

	s.blockCheckSpaceCondition()

	assert.Equal(t, false, s.spaceInsufficient)
}

func Test_selfUpdate(t *testing.T) {
	tests := []struct {
		kind entities.UpgradeKind
	}{
		{kind: entities.UpgradeKind_DARWIN},
		{kind: entities.UpgradeKind_SELF},
	}
	for _, test := range tests {
		ctrl := gomock.NewController(t)
		assetMock := mock_controllers.NewMockAssetController(ctrl)
		scannerMock := mock_controllers.NewMockScannerController(ctrl)
		s := &scan{assetController: assetMock, scannerController: scannerMock}
		assetMock.EXPECT().GetAsset().Return(&entities.Asset{UpdateFeature: &entities.UpdateFeature{Kind: test.kind}}).AnyTimes()
		scannerMock.EXPECT().Update().Return(&scanadapter.Version{}, nil).AnyTimes()
		s.selfUpdate()
	}
}
func Test_NewScan(t *testing.T) {
	ctrl := gomock.NewController(t)
	scannerController := mock_controllers.NewMockScannerController(ctrl)
	scannerController.EXPECT().GetScanner().Return(&entities.Scanner{StartupSeconds: 1}).AnyTimes()
	scannerController.EXPECT().SetState(gomock.Any()).Return().AnyTimes()
	scannerController.EXPECT().GetState().Return(int32(0)).AnyTimes()
	scannerController.EXPECT().Update().Return(&scanadapter.Version{}, nil).AnyTimes()
	scannerController.EXPECT().Ping().Return(&scanadapter.PingResponse{StartupSeconds: 1, Version: &scanadapter.Version{}}, nil).AnyTimes()

	assetController := mock_controllers.NewMockAssetController(ctrl)
	assetController.EXPECT().GetAsset().Return(&entities.Asset{
		ScanFeature: &entities.ScanFeature{
			MaxFetchCount: 2,
			MaxFetchSize:  2,
		},
		DeployFeature: &entities.DeployFeature{
			SharedDirSize: 2,
		},
	}).AnyTimes()

	dialer := mock.NewMockDialer(ctrl)
	relayClient := mock.NewMockTaskClient(ctrl)
	relayClient.EXPECT().FetchTasks(gomock.Any(), gomock.Any()).Return(&worker_api.FetchTasks_Response{}, nil).AnyTimes()
	dialer.EXPECT().FetchRelayClient(gomock.Any()).Return(relayClient, nil).AnyTimes()

	fileDir := "testfileDir"
	plusFileDir := "testplusFileDir"
	logDir := "testlogDir"

	os.MkdirAll(fileDir, os.ModePerm)
	os.MkdirAll(plusFileDir, os.ModePerm)
	os.MkdirAll(logDir, os.ModePerm)
	defer os.RemoveAll(fileDir)
	defer os.RemoveAll(plusFileDir)
	defer os.RemoveAll(logDir)

	guard := gomonkey.ApplyFunc(os.Getenv, func(s string) string {
		if s == "KAMALA_UPDATE_ROOT" {
			return "update"
		} else if s == "ENABLE_USE_PLUS_DISK" {
			return "yes"
		}
		return ""
	})
	defer guard.Reset()

	guard = gomonkey.ApplyFunc(GetBasicController, func(dialer util.Dialer) (controllers.ScannerController, controllers.AssetController) {
		return scannerController, assetController
	})
	defer guard.Reset()

	NewScan(dialer, fileDir, plusFileDir, logDir)
}

func Test_toReload(t *testing.T) {

	tests := []struct {
		attempt int
	}{
		{
			attempt: 0,
		},
		{
			attempt: 5,
		},
	}

	for _, test := range tests {
		ctrl := gomock.NewController(t)
		scannerController := mock_controllers.NewMockScannerController(ctrl)
		scannerController.EXPECT().GetScanner().Return(
			&entities.Scanner{
				StartupSeconds: 1,
			},
		).AnyTimes()

		assetMock := mock_controllers.NewMockAssetController(ctrl)
		assetMock.EXPECT().GetAsset().Return(
			&entities.Asset{
				UpdateFeature: &entities.UpdateFeature{
					Kind: entities.UpgradeKind_DARWIN,
				},
			},
		).AnyTimes()

		helperMock := mock.NewMockUpdateHelper(ctrl)
		helperMock.EXPECT().GetLatestDir().Return("test", nil).AnyTimes()

		s := &scan{
			scannerController: scannerController,
			assetController:   assetMock,
			helper:            helperMock,
			scannerStartTime:  time.Now().Add(-time.Duration(2) * time.Second).Unix(),
		}

		i := 0
		guard := gomonkey.ApplyPrivateMethod(reflect.TypeOf(s), "reload", func(s *scan, versionDir, engine, pattern string) error {
			if i < test.attempt {
				i++
				return fmt.Errorf("test")
			}
			return nil
		})

		defer guard.Reset()

		s.toReload("test", "", "")

	}

}

func Test_reload_success(t *testing.T) {
	mockScanner := mock_controllers.NewMockScannerController(gomock.NewController(t))
	mockScanner.EXPECT().Reload(gomock.Any()).Return(&scanadapter.Version{EngineVersion: "engine", PatternVersion: "patten"}, nil).AnyTimes()
	var s = &scan{scannerController: mockScanner}
	s.reload("", "engine", "patten")
}

func Test_handleScanResult_scanVersionOld(t *testing.T) {
	ctrl := gomock.NewController(t)
	scannerController := mock_controllers.NewMockScannerController(ctrl)
	scannerController.EXPECT().GetScanner().Return(
		&entities.Scanner{
			StartupSeconds: 1,
		},
	).AnyTimes()

	assetMock := mock_controllers.NewMockAssetController(ctrl)
	assetMock.EXPECT().GetAsset().Return(
		&entities.Asset{
			UpdateFeature: &entities.UpdateFeature{
				Kind: entities.UpgradeKind_DARWIN,
			},
		},
	).AnyTimes()

	helperMock := mock.NewMockUpdateHelper(ctrl)
	helperMock.EXPECT().GetLatestDir().Return("test", nil).AnyTimes()
	helperMock.EXPECT().GetDir(gomock.Any(), gomock.Any()).Return("", errors.New("test")).AnyTimes()
	s := &scan{
		scannerController: scannerController,
		assetController:   assetMock,
		helper:            helperMock,
		scannerStartTime:  time.Now().Add(-time.Duration(2) * time.Second).Unix(),
		resultChan:        make(chan *LocalTask, 10),
		logChan:           make(chan *LocalTask, 10),
	}

	localTask := &LocalTask{
		task: &entities.Task{},
	}
	result := &scanadapter.ScanResult{
		Version: &scanadapter.Version{EngineVersion: "old", PatternVersion: "old"},
	}

	s.handleScanResult(localTask, result, "new")

	assert.Equal(t, int32(3), localTask.task.MaxRetryTimes)
	assert.Equal(t, entities.ScanCode_OTHER, localTask.task.Code)

}

func Test_restartInit(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockScannerController := mock_controllers.NewMockScannerController(ctrl)
	mockAssetContrller := mock_controllers.NewMockAssetController(ctrl)
	mockAssetContrller.EXPECT().GetAsset().Return(&entities.Asset{UpdateFeature: &entities.UpdateFeature{Kind: entities.UpgradeKind_NONE}}).AnyTimes()
	times := 0
	mockScannerController.EXPECT().Ping().DoAndReturn(func() (*scanadapter.PingResponse, error) {
		if times == 0 {
			times++
			return &scanadapter.PingResponse{}, fmt.Errorf("test")
		} else if times == 1 {
			times++
			return &scanadapter.PingResponse{StartupSeconds: 150, Version: &scanadapter.Version{}}, nil
		}
		return &scanadapter.PingResponse{StartupSeconds: 5, Version: &scanadapter.Version{}}, nil
	}).AnyTimes()
	mockScannerController.EXPECT().GetState().Return(int32(util.IDLE)).AnyTimes()
	mockScannerController.EXPECT().SetState(gomock.Any()).Return().AnyTimes()
	s := scan{preStartupTime: time.Now().Add(-time.Duration(10) * time.Second).Unix(), scannerController: mockScannerController, assetController: mockAssetContrller}
	s.restartInit()
}

func Test_Lock(t *testing.T) {
	name := "test-"
	defer os.Remove(name)
	f, _ := os.Create(name)
	f.Close()

	l, err := tryLock(name)
	assert.Nil(t, err)

	l.Lock()
	l.UnLock()
	l.Release()

}

func Test_declearFlag(t *testing.T) {

	defer framework.Init()()

	PrintEnv()
	PrintArgs()
}

func Test_restart(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockScannerController := mock_controllers.NewMockScannerController(ctrl)

	mockScannerController.EXPECT().GetState().Return(
		int32(util.IDLE),
	).AnyTimes()

	mockScannerController.EXPECT().SetState(gomock.Any()).Return().AnyTimes()

	mockScannerController.EXPECT().Exit().Return(nil).AnyTimes()

	s := scan{
		preStartupTime:    time.Now().Add(-time.Duration(10) * time.Second).Unix(),
		scannerController: mockScannerController,
	}

	s.restart()
}

// func Test_reload_failed(t *testing.T) {
// 	mockScanner := mock_controllers.NewMockScannerController(gomock.NewController(t))
// 	mockScanner.EXPECT().Reload(gomock.Any()).Return(&kamala.Version{EngineVersion: "engine", PatternVersion: ""}, nil).AnyTimes()
// 	var s = &scan{
// 		scannerController: mockScanner,
// 	}

// 	s.reload("", "engine", "patten")
// }

// func Test_loopTryLockFile(t *testing.T) {
// 	s := &scan{}
// 	guard := gomonkey.ApplyFunc(chooseDirList, func(taskSize uint64) []DirInfo {
// 		return []DirInfo{
// 			{
// 				Name: "file1",
// 				Size: smallFile,
// 			},
// 		}
// 	})
// 	defer guard.Reset()

// 	guard = gomonkey.ApplyFunc(NewFileLock, func(name string) (*FileLock, error) {
// 		return &FileLock{}, nil
// 	})

// 	defer guard.Reset()

// 	fLock := &FileLock{}
// 	guard = gomonkey.ApplyMethodFunc(reflect.TypeOf(fLock), "Lock", func(*FileLock) error {
// 		return nil
// 	})
// 	defer guard.Reset()

// 	guard = gomonkey.ApplyPrivateMethod(reflect.TypeOf(s), "removeInvalidFile", func(s *scan, name string) {

// 	})
// 	defer guard.Reset()

// 	guard = gomonkey.ApplyFunc(util.GetDirSize, func(path string) (int64, error) {
// 		return 4 * GB, nil
// 	})
// 	defer guard.Reset()

// 	s.loopTryLockFile(3 * GB)

// }

// func Test_CreateJob(t *testing.T) {

// 	addr := "10.252.12.197:31056"

// 	_ = dialer.RegisterDialOption("CreateJob", dialer.DialWithRegistry(nil))
// 	conn := dialer.CreateClientConnection(dialer.WithTarget(addr), dialer.WithDialOptTag("DirectFetchVloader"))

// 	client := kamala_api_worker.NewJobClient(conn)

// 	job := entities.Job{
// 		Name: "test",
// 		Tasks: []*entities.Task{
// 			{
// 				Name:     "test01",
// 				Priority: entities.Priority_HIGH,

// 			},
// 		},
// 	}
// 	createJobRequest := kamala_api_worker.CreateJob_Request{
// 		Job: &job,
// 	}
// 	client.CreateJob(context.Background(), &createJobRequest)

// }

func Test_test(t *testing.T) {
	err := filepath.WalkDir("storage", func(path string, d fs.DirEntry, err error) error {
		if d.IsDir() {
			return nil
		}
		fmt.Println(path)
		return nil
	})

	fmt.Println(err)

}
