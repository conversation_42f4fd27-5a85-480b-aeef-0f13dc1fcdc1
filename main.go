package main

import (
	"flag"
	"fmt"
	"os"
	"syscall"
	"time"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git"

	_ "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/message"
	_ "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/http"
	_ "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/local"
	_ "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus"
	_ "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/relay"

	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers/asset"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers/scanner"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"

	_ "git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework-plugins.git"
	_ "git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework-plugins.git/log/file_log"
	_ "git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework-plugins.git/log/qlog"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/config"
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	"github.com/judwhite/go-svc/svc"
)

func main() {
	flag.String("MORPHEUS_API_SCANLOG", "morpheus.api.ScanLog", "address of MORPHEUS_API_SCANLOG.")
	flag.String("MORPHEUS_API_STORAGE", "morpheus.api.Storage", "address of MORPHEUS_API_STORAGE.")
	flag.String("KAMALA_API_TASK", "kamala.api.Task", "address of KAMALA_API_TASK.")
	flag.String("KAMALA_API_ASSET", "kamala.api.Asset", "address of KAMALA_API_ASSET.")
	if os.Getenv("HOSTNAME") != "" {
		flag.String("SCAN_API", fmt.Sprintf("%s:9001", os.Getenv("HOSTNAME")), "address of scan service")
	} else {
		flag.String("SCAN_API", ":9001", "address of scan service")
	}

	flag.String("KAMALA_SCANNER_FILE", "/tmp", "the dir of download file")
	flag.String("KAMALA_SCANNER_LOG", "/tmp", "the dir of result log file")
	flag.String("KAMALA_SCANNER_FILE_PLUS", "/tmp", "the dir of result log file")
	flag.String("KAMALA_SCANNER_DATA", "/tmp", "the dir of data file")

	flag.String("KAMALA_RELAY_API", ":9009", "address of relay service, (host port)")
	flag.String("KAMALA_RELAY_DIR", "/relay", "the dir of relay sample file")
	defer framework.Init()()

	PrintEnv()
	PrintArgs()

	dialer := util.NewGrpcDialer()
	if os.Getenv("KAMALA_SCANNER_FILE_PLUS") == "" {
		_ = os.Setenv("KAMALA_SCANNER_FILE_PLUS", os.Getenv("KAMALA_RELAY_DIR"))
		log.Warnf("kamala_scanner_file_plus env var is empty, using default dir: [%v]",
			os.Getenv("KAMALA_SCANNER_FILE_PLUS"))
	}
	log.Infof("plus file name: [%v]", os.Getenv("KAMALA_SCANNER_FILE_PLUS"))
	service := NewScan(dialer, os.Getenv("KAMALA_SCANNER_FILE"), os.Getenv("KAMALA_SCANNER_FILE_PLUS"),
		os.Getenv("KAMALA_SCANNER_LOG"))
	if err := svc.Run(service, syscall.SIGINT, syscall.SIGTERM); err != nil {
		log.Fatal(err)
	}
}

func PrintEnv() {
	for _, e := range os.Environ() {
		log.Infof("env: %v", e)
	}

	for _, a := range os.Args {
		log.Infof("command arg: %v", a)
	}
}

func PrintArgs() {
	log.Infof("arg config.type: %s", config.String("config.type"))
	log.Infof("arg config.etcd.addr: %s", config.String("config.etcd.addr"))
	log.Infof("arg registry.type: %s", config.String("registry.type"))
	log.Infof("arg registry.etcd.addr: %s", config.String("registry.etcd.addr"))
	log.Infof("arg registry.etcd.prefix: %s", config.String("registry.etcd.prefix"))
	log.Infof("arg logger.type: %s", config.String("logger.type"))
	log.Infof("arg logger.min_level: %s", config.String("logger.min_level"))
}

func GetBasicController(dialer util.Dialer) (controllers.ScannerController, controllers.AssetController) {
	scannerController := scanner.NewController(dialer)
	assetController := asset.NewController(dialer)

	scannerController.SetAssetController(assetController)
	assetController.SetScannerController(scannerController)

	for scannerController.GetScanner() == nil || assetController.GetAsset() == nil {
		time.Sleep(time.Millisecond)
	}
	return scannerController, assetController
}
