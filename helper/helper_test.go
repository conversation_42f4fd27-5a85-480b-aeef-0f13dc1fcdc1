package helper

import (
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
)

const TestUpdateRoot = "../testfile"

func TestUpdateHelper_GetUpdateDir(t *testing.T) {
	h := NewUpdateHelper(TestUpdateRoot)
	res := h.GetUpdateDir()
	assert.Equal(t, filepath.Join(TestUpdateRoot, "latest"), res)
}

func TestUpdateHelper_GetDir(t *testing.T) {
	h := NewUpdateHelper(TestUpdateRoot)
	tests := []struct {
		pattern string
		engine  string
		expect  string
	}{
		{
			pattern: "20210203200916",
			engine:  "20210203200916",
			expect:  "1612354532",
		},
		{
			pattern: "20210203200911",
			engine:  "20210203200916",
			expect:  "1612354532",
		},
	}
	actual, err := h.GetDir(tests[0].pattern, tests[0].engine)
	assert.Equal(t, nil, err)
	assert.Equal(t, tests[0].expect, actual)

	actual, err = h.GetDir(tests[1].pattern, tests[1].engine)
	assert.NotEqual(t, nil, err)
}

func TestUpdateHelper_GetIndex(t *testing.T) {
	h := NewUpdateHelper(TestUpdateRoot)
	expect := []struct {
		folder         string
		updateTime     int64
		engineVersion  string
		patternVersion string
		updateDuration float64
	}{
		{
			folder:         "1612354532",
			updateTime:     1612354532,
			engineVersion:  "20210203200916",
			patternVersion: "20210203200916",
			updateDuration: 21.059195698,
		},
		{
			folder:         "1612415440",
			updateTime:     1612415440,
			engineVersion:  "20210204130741",
			patternVersion: "20210204130741",
			updateDuration: 22.184058813,
		},
		{
			folder:         "1612436738",
			updateTime:     1612436738,
			engineVersion:  "20210204190005",
			patternVersion: "20210204190005",
			updateDuration: 20.88515773,
		},
		{
			folder:         "1612440932",
			updateTime:     1612440932,
			engineVersion:  "20210204200910",
			patternVersion: "20210204200910",
			updateDuration: 20.569936206,
		},
		{
			folder:         "1612523124",
			updateTime:     1612523124,
			engineVersion:  "20210205190005",
			patternVersion: "20210205190005",
			updateDuration: 20.140055102,
		},
		{
			folder:         "1612527322",
			updateTime:     1612527322,
			engineVersion:  "20210205200856",
			patternVersion: "20210205200856",
			updateDuration: 20.548646412,
		},
		{
			folder:         "1612609519",
			updateTime:     1612609519,
			engineVersion:  "20210206190005",
			patternVersion: "20210206190005",
			updateDuration: 20.274165467,
		},
		{
			folder:         "1612613731",
			updateTime:     1612613731,
			engineVersion:  "20210206200939",
			patternVersion: "20210206200939",
			updateDuration: 19.964300884,
		},
		{
			folder:         "1612695923",
			updateTime:     1612695923,
			engineVersion:  "20210207190005",
			patternVersion: "20210207190005",
			updateDuration: 20.346868472,
		},
		{
			folder:         "1612701322",
			updateTime:     1612701322,
			engineVersion:  "20210207203116",
			patternVersion: "20210207203116",
			updateDuration: 20.495990722,
		},
	}
	actual := h.GetIndex()
	for i, tt := range expect {
		assert.Equal(t, tt.folder, actual[i].Folder)
		assert.Equal(t, tt.updateDuration, actual[i].UpdateDuration)
		assert.Equal(t, tt.patternVersion, actual[i].PatternVersion)
		assert.Equal(t, tt.engineVersion, actual[i].EngineVersion)
		assert.Equal(t, tt.updateTime, actual[i].UpdateTime)
	}
}

// 单纯测试本地每个时间辍目录保存的版本信息汇总后与index.json文件版本的版本信息是一致的
func TestUpdateHelper_RebuildIndex(t *testing.T) {
	h := NewUpdateHelper(TestUpdateRoot)
	err := h.RebuildIndex()
	assert.Equal(t, nil, err)
}

// 测试本地没有index.json文件，只有一个个时间辍目录，此时触发重建索引流程，创建索引文件：index.json
func TestUpdateHelper_RebuildIndex2(t *testing.T) {
	tmpDir := "tmp"
	err := CopyDir(TestUpdateRoot, tmpDir)
	assert.Equal(t, nil, err)
	if checkFileIsExist(filepath.Join(tmpDir, IndexFile)) {
		t.Logf("remove index file: %v", filepath.Join(tmpDir, IndexFile))
		_ = os.Remove(filepath.Join(tmpDir, IndexFile))
	}
	expect := []Index{
		{
			Folder:         "1612695923",
			UpdateTime:     1612695923,
			EngineVersion:  "20210207190005",
			PatternVersion: "20210207190005",
			UpdateDuration: 20.346868472,
		},
		{
			Folder:         "1612701322",
			UpdateTime:     1612701322,
			EngineVersion:  "20210207203116",
			PatternVersion: "20210207203116",
			UpdateDuration: 20.495990722,
		},
	}
	h := NewUpdateHelper(tmpDir)
	err = h.RebuildIndex()
	assert.Equal(t, nil, err)
	assert.Equal(t, true, checkFileIsExist(filepath.Join(tmpDir, IndexFile)))
	assert.Equal(t, expect, h.GetIndex())
	if checkFileIsExist(tmpDir) {
		_ = os.RemoveAll(tmpDir)
	}
}

// 测试本地既没有时间辍目录，也没有index.json文件，此时则什么也不做返回：nil
func TestUpdateHelper_RebuildIndex3(t *testing.T) {
	tmpDir := "tmp"
	if !checkFileIsExist(tmpDir) {
		err := os.Mkdir(tmpDir, 0775)
		assert.Equal(t, nil, err)
	}
	h := NewUpdateHelper(tmpDir)
	err := h.RebuildIndex()
	assert.Equal(t, nil, err)
	// 重建索引后目录应该依然为空
	dirList, err := ioutil.ReadDir(tmpDir)
	assert.Equal(t, nil, err)
	assert.Equal(t, 0, len(dirList))
	if checkFileIsExist(tmpDir) {
		_ = os.RemoveAll(tmpDir)
	}
}

func TestUpdateHelper_Download(t *testing.T) {
	tests := []struct {
		errRequest error
		errDo      error
		resp       *http.Response
		errWant    error
	}{
		{
			errRequest: fmt.Errorf("request"),
			errWant:    fmt.Errorf("request"),
		},
		{
			errDo:   fmt.Errorf("do"),
			errWant: fmt.Errorf("do"),
		},
		{
			resp: &http.Response{
				StatusCode: 404,
			},
			errWant: fmt.Errorf("not found"),
		},
		// {
		// 	resp: &http.Response{
		// 		StatusCode: 200,
		// 		Body:       io.NopCloser(strings.NewReader("test")),
		// 	},
		// },
	}

	for _, test := range tests {

		h := NewUpdateHelper(TestUpdateRoot)
		guard := gomonkey.ApplyFunc(http.NewRequest, func(method string, url string, body io.Reader) (*http.Request, error) {
			return &http.Request{}, test.errRequest
		})
		defer guard.Reset()

		guard = gomonkey.ApplyMethod(reflect.TypeOf(&http.Client{}), "Do", func(h *http.Client, req *http.Request) (*http.Response, error) {
			return test.resp, test.errDo
		})
		defer guard.Reset()

		_, err := h.Download("")

		if test.errWant != nil {
			assert.NotNil(t, err)
		} else {
			assert.Nil(t, err)
		}
	}

}

func TestUpdateHelper_GetLatestDir(t *testing.T) {
	h := NewUpdateHelper(TestUpdateRoot)
	latestDir, err := h.GetLatestDir()
	assert.Equal(t, nil, err)
	assert.Equal(t, "1612701322", latestDir)
}

// 测试本地已经是最新版本
// func TestUpdateHelper_RestoreUpdateDir(t *testing.T) {
// 	test := &Index{
// 		Folder:         "1612701322",
// 		UpdateTime:     1612701322,
// 		EngineVersion:  "20210207203116",
// 		PatternVersion: "20210207203116",
// 		UpdateDuration: 20.495990722,
// 	}
// 	tmpDir := "tmp"
// 	err := CopyDir(TestUpdateRoot, tmpDir)
// 	assert.Equal(t, nil, err)
// 	h := NewUpdateHelper(tmpDir)
// 	dirName, err := h.RestoreUpdateDir(test, &VersionDefine{PatternVersionKind: VersionKindSTRING,
// 		EngineVersionKind: VersionKindSTRING})
// 	assert.Equal(t, AlreadyLatestVersion, dirName)
// 	assert.Equal(t, errors.New("version already latest, No version need to update"), err)
// 	_ = h.CleanUpdateDir()
// 	assert.Equal(t, false, checkFileIsExist(filepath.Join(h.GetUpdateDir(), "test.zip")))
// 	if checkFileIsExist(tmpDir) {
// 		_ = os.RemoveAll(tmpDir)
// 	}
// }

// 测试本地已经保存有10个版本，此时有新版本到来，将最老的版本删除并更新最新版本到index.json，同时创建新的时间辍目录删除老的时间辍目录
func TestUpdateHelper_RestoreUpdateDir2(t *testing.T) {
	test := &Index{
		Folder:         "1622701322",
		UpdateTime:     1622701322,
		EngineVersion:  "20210507203116",
		PatternVersion: "20210507203116",
		UpdateDuration: 20.495990722,
	}
	expect := []Index{

		{
			Folder:         "1612701322",
			UpdateTime:     1612701322,
			EngineVersion:  "20210207203116",
			PatternVersion: "20210207203116",
			UpdateDuration: 20.495990722,
		},
		{
			Folder:         "1622701322",
			UpdateTime:     1622701322,
			EngineVersion:  "20210507203116",
			PatternVersion: "20210507203116",
			UpdateDuration: 20.495990722,
		},
	}
	tmpDir := "tmp"
	err := CopyDir(TestUpdateRoot, tmpDir)
	assert.Equal(t, nil, err)
	h := NewUpdateHelper(tmpDir)
	expectDir := filepath.Join(tmpDir, test.Folder)
	dirName, err := h.RestoreUpdateDir(test, &VersionDefine{PatternVersionKind: VersionKindSTRING,
		EngineVersionKind: VersionKindSTRING})
	assert.Equal(t, expectDir, dirName)
	actual := h.GetIndex()
	assert.Equal(t, expect, actual)
	assert.Equal(t, true, checkFileIsExist(filepath.Join(expectDir, "test.zip")))
	assert.Equal(t, true, checkFileIsExist(filepath.Join(expectDir, InfoFile)))
	if checkFileIsExist(tmpDir) {
		_ = os.RemoveAll(tmpDir)
	}
}

func Test_compareVersionCore(t *testing.T) {

	tests := []struct {
		lv   string
		rv   string
		kind VersionKind
		want int
	}{
		{
			lv:   "1",
			rv:   "1",
			kind: VersionKindSTRING,
			want: 0,
		},
		{
			lv:   "1",
			rv:   "1",
			kind: VersionKindSEMVER,
			want: 0,
		},
		{
			lv:   "2",
			rv:   "1",
			kind: VersionKindSEMVER,
			want: 1,
		},
		{
			lv:   "1",
			rv:   "2",
			kind: VersionKindSEMVER,
			want: -1,
		},
	}

	for _, test := range tests {
		res, err := CompareVersionCore(test.lv, test.rv, test.kind)
		if err != nil {
			assert.Equal(t, test.want, res)
		}
	}

}

func Test_compareVersion(t *testing.T) {
	tests := []struct {
		EngineVersion   string
		PatternVersion  string
		evFromIndexBack string
		pvFromIndexBack string
		want            bool
	}{
		{
			EngineVersion:   "2",
			PatternVersion:  "2",
			evFromIndexBack: "1",
			pvFromIndexBack: "1",
			want:            true,
		},
		{
			EngineVersion:   "2",
			PatternVersion:  "2",
			evFromIndexBack: "2",
			pvFromIndexBack: "1",
			want:            true,
		},
		{
			EngineVersion:   "1",
			PatternVersion:  "2",
			evFromIndexBack: "1",
			pvFromIndexBack: "1",
			want:            true,
		},
		{
			EngineVersion:   "1",
			PatternVersion:  "2",
			evFromIndexBack: "2",
			pvFromIndexBack: "1",
			want:            false,
		},
	}

	for _, test := range tests {
		version := &AssetVersion{
			EngineVersion:  test.EngineVersion,
			PatternVersion: test.PatternVersion,
		}
		kind := VersionDefine{
			PatternVersionKind: VersionKindSEMVER,
			EngineVersionKind:  VersionKindSEMVER,
		}
		h := &updateHelper{
			indexFile:  "index.json",
			latestPath: "update/latest",
			updateRoot: "update",
		}

		guard := gomonkey.ApplyPrivateMethod(reflect.TypeOf(h), "getInfoFromIndexJson",
			func(x *updateHelper) []Index {
				index := []Index{
					{
						PatternVersion: test.pvFromIndexBack,
						EngineVersion:  test.evFromIndexBack,
					},
				}
				return index
			},
		)
		defer guard.Reset()

		res := h.compareVersion(version, &kind)
		assert.Equal(t, test.want, res)

	}
}

func Test_getSeparator(t *testing.T) {
	tests := []struct {
		in   string
		want string
	}{
		{
			in:   "+",
			want: "+",
		},
		{
			in:   "-",
			want: "-",
		},
		{
			in:   "-",
			want: "-",
		},
		{
			in:   "@",
			want: "",
		},
	}

	for _, test := range tests {
		res := getSeparator(test.in)
		assert.Equal(t, test.want, res)
	}
}

func Test_CleanUpdateDir(t *testing.T) {
	os.MkdirAll(LatestPath, os.ModePerm)
	os.MkdirAll(path.Join(LatestPath, "111"), os.ModePerm)

	h := updateHelper{
		latestPath: LatestPath,
	}
	h.CleanUpdateDir()

	os.RemoveAll(LatestPath)
}
