package helper

import (
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"math"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"
)

type Index struct {
	Folder         string  `json:"folder"`
	UpdateTime     int64   `json:"updateTime"`
	EngineVersion  string  `json:"engineVersion"`
	PatternVersion string  `json:"patternVersion"`
	UpdateDuration float64 `json:"updateDuration"`
	DarwinVersion  string  `json:"darwinVersion"`
}

type VersionKind int32
type VersionDefine struct {
	EngineVersionKind  VersionKind
	PatternVersionKind VersionKind
}

type AssetVersion struct {
	EngineVersion  string
	PatternVersion string
}

const (
	VersionKindUNKNOWNVERSION VersionKind = 0
	// 字符串
	// 按字符串序进行比较大小, 例如2.1>10.1
	VersionKindSTRING VersionKind = 1
	// 语义版本
	// 按语义含义比较大小, 例如2.2<2.10, 与标准SEMVER不同的是, 这里支持任意数量的.与-分隔, 在
	// 缀一样的情况下, 包含多余的后缀版本比较大, 例如2.1.3.4<2.1.3.4.5
	VersionKindSEMVER VersionKind = 2
)

const (
	TempSuffix   = ".temp"
	LatestPath   = "latest"
	IndexFile    = "index.json"
	InfoFile     = "info.json"
	FolderLen    = 10
	IndexMaxSize = 2
)

type UpdateHelper interface {
	// 获取最新版本库目录,真实最新的时间戳目录，返回为空表示没有找到
	GetLatestDir() (string, error)
	// 获取指定版本库目录,返回为空表示没有找到
	GetDir(PatternVersion, EngineVersion string) (string, error)
	// 根据版本信息，保存升级目录, 持久化保存当前目录内容
	RestoreUpdateDir(index *Index, kind *VersionDefine) (string, error)
	// 清理升级目录
	CleanUpdateDir() error
	// 获取升级的中转目录
	GetUpdateDir() string
	// 更新index文件
	RebuildIndex() error
	// 获取index文件信息
	GetIndex() []Index
	// 下载版本库文件
	Download(url string) (io.ReadCloser, error)
}

const AlreadyLatestVersion string = "AlreadyLatestVersion"

type updateHelper struct {
	indexFile  string // index.json
	latestPath string // latest目录
	updateRoot string // /update目录
}

func NewUpdateHelper(root string) UpdateHelper {
	updateRoot := root
	h := &updateHelper{
		updateRoot: updateRoot,
		indexFile:  filepath.Join(updateRoot, IndexFile),
		latestPath: filepath.Join(updateRoot, LatestPath),
	}
	return h
}

// 获取最新版本库目录,真实最新的时间戳目录
func (h *updateHelper) GetLatestDir() (string, error) {
	indexes := h.getInfoFromIndexJson()
	if len(indexes) == 0 {
		return "", errors.New("latest dir not exist")
	}
	return indexes[len(indexes)-1].Folder, nil
}

// 获取指定版本库目录
func (h *updateHelper) GetDir(PatternVersion, EngineVersion string) (string, error) {
	indexes := h.getInfoFromIndexJson()
	for _, index := range indexes {
		if index.EngineVersion == EngineVersion && index.PatternVersion == PatternVersion {
			return index.Folder, nil
		}
	}
	return "", errors.New("specify version director not exist")
}

// 获取临时中转的升级目录，也就是那个latest目录
func (h *updateHelper) GetUpdateDir() string {
	return h.latestPath
}

// 保存升级目录, 持久化保存当前目录内容
// 根据reload返回回来的版本信息，生成升级信息并落盘存储
func (h *updateHelper) RestoreUpdateDir(index *Index, kind *VersionDefine) (string, error) {
	// version := &AssetVersion{
	// 	EngineVersion:  index.EngineVersion,
	// 	PatternVersion: index.PatternVersion,
	// }
	// TODO 之前是为true代表：update之后的版本是小于等于本地index.json的版本
	// TODO 现在是为false代表：update之后的版本是小于等于本地index.json的版本
	// if h.compareVersion(version, kind) == false {
	// 	log.Warn("version already latest, No version need to update")
	// 	return AlreadyLatestVersion, errors.New("version already latest, No version need to update")
	// }
	// 创建时间戳目录
	timestampDir := filepath.Join(h.updateRoot, index.Folder)
	dir, err := h.createDir(timestampDir)
	if err != nil {
		_ = os.RemoveAll(timestampDir)
		return dir, err
	}
	// 将latest目录下的内容拷贝到刚刚创建的时间戳文件中
	err = CopyDir(h.latestPath, dir)
	if err != nil {
		return dir, err
	}
	// 将info信息存入info.json文件中去,也就是将刚刚从latest目录下copy过去info.json文件更新了
	err = h.storeJson(filepath.Join(dir, InfoFile), index)
	if err != nil {
		return dir, err
	}
	indexes := h.getInfoFromIndexJson()
	indexes = append(indexes, *index)
	indexes = h.resizeIndex(indexes)
	if err = h.createNewIndexJson(indexes); err != nil {
		return "", err
	}
	return dir, nil
}

// 清理升级目录
func (h *updateHelper) CleanUpdateDir() error {
	d, err := os.Open(h.latestPath)
	if err != nil {
		return err
	}
	defer d.Close()
	names, err := d.Readdirnames(-1)
	if err != nil {
		return err
	}
	for _, name := range names {
		err = os.RemoveAll(filepath.Join(h.latestPath, name))
		if err != nil {
			return err
		}
	}
	return nil
}

// 读取各个时间戳文件存储的版本信息，与index.json文件中版本信息对比，若不一致则更新index中版本信息
func (h *updateHelper) RebuildIndex() error {
	infoFromTimeFile := h.getAllVersionInfo()
	infoFromIndexFile := h.getInfoFromIndexJson()
	if equal := compareIndexes(infoFromTimeFile, infoFromIndexFile); equal == false {
		// 为假说明：时间戳文件中内容保存的版本信息被更新了，那么index.json文件内容也要更新
		// 这里需要维护一下时间戳文件的限制是因为：时间戳文件有可能被手动的添加几个导致超过了数量限制，所以需要维护一下
		infoFromTimeFile = h.resizeIndex(infoFromTimeFile)
		// 各个时间戳文件的内容与index.json文件的版本信息不一致，需要创建一个新的index文件来存储此时最新版本信息
		if err := h.createNewIndexJson(infoFromTimeFile); err != nil {
			return err
		}
		return nil
	}
	return nil
}

func (h *updateHelper) GetIndex() []Index {
	indexes := h.getInfoFromIndexJson()
	return indexes
}

func (h *updateHelper) Download(url string) (io.ReadCloser, error) {
	request, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}
	httpCli := &http.Client{
		Transport: &http.Transport{
			DisableKeepAlives: true,
			TLSClientConfig:   &tls.Config{InsecureSkipVerify: true},
		},
	}
	resp, err := httpCli.Do(request)
	if err != nil {
		return nil, fmt.Errorf("http get failed: %v", err)
	}
	if resp.StatusCode >= 400 {
		return nil, fmt.Errorf("http get failed: %v, code: %v", err, resp.StatusCode)
	}
	return resp.Body, nil
}

// 获取index.json文件中的汇总的版本信息
func (h *updateHelper) getInfoFromIndexJson() []Index {
	if ok := checkFileIsExist(h.indexFile); ok == false {
		return []Index{}
	}
	var indexes []Index
	if err := getInfoFromJsonFile(h.indexFile, &indexes); err != nil {
		return []Index{}
	}
	return indexes
}

// 将json数据类型的index，落盘存入指定文件
// 在新创建的时间戳目录下，检查info.json文件是否存在，若存在则将json内容写入
// 若不存在创建一个info.json文件再将json内容写入
func (h *updateHelper) storeJson(file string, in interface{}) error {
	if checkFileIsExist(file) == false {
		_, err := os.Create(file)
		if err != nil {
			return err
		}
	}
	data, err := json.MarshalIndent(in, "", "\t")
	if err != nil {
		return err
	}
	err = ioutil.WriteFile(file, data, 0666)
	if err != nil {
		return err
	}
	return nil
}

// 获取每个时间戳文件的版本信息汇总返回
// 1、如果时间戳目录中不存在info.json文件，则删除该时间戳目录
// 2、info.json文件有字段为空说明生成info.json文件的时候异常中断了，则删除该时间戳目录
func (h *updateHelper) getAllVersionInfo() []Index {
	var indexes []Index
	err := filepath.Walk(h.updateRoot, func(path string, f os.FileInfo, err error) error {
		if f != nil && f.IsDir() && (len(f.Name()) == FolderLen) {
			infoPath := filepath.Join(path, InfoFile)
			info, err := getInfo(infoPath)
			if err != nil || info.Folder == "" || info.UpdateDuration == 0 || info.UpdateTime == 0 {
				err = os.RemoveAll(path)
			} else {
				indexes = append(indexes, *info)
			}
		}
		return nil
	})
	if err != nil {
		return []Index{}
	}
	return indexes
}

// 维护时间戳文件数目限制
func (h *updateHelper) resizeIndex(indexes []Index) []Index {
	if len(indexes) > IndexMaxSize {
		for i, j := 0, len(indexes)-IndexMaxSize; i < j; i++ {
			_ = os.RemoveAll(filepath.Join(h.updateRoot, indexes[i].Folder))
		}
		indexes = indexes[len(indexes)-IndexMaxSize:]
	}
	return indexes
}

// 创建一个新的index.json文件
func (h *updateHelper) createNewIndexJson(indexes []Index) error {
	// 先写入临时文件再rename成索引
	// 若indexFile不存在，则会去创建一个
	tempPath := h.indexFile + TempSuffix
	if err := updateJson(tempPath, indexes); err != nil {
		return err
	}
	if err := renameFile(tempPath, h.indexFile); err != nil {
		return err
	}
	return nil
}

// 如果<=本地索引最新版本可以直接退出 TODO 修改为新逻辑
func (h *updateHelper) compareVersion(version *AssetVersion, kind *VersionDefine) bool {
	indexes := h.getInfoFromIndexJson()
	if len(indexes) == 0 {
		return true
	}
	evFromArgs := version.EngineVersion
	pvFromArgs := version.PatternVersion
	evFromIndexBack := indexes[len(indexes)-1].EngineVersion
	pvFromIndexBack := indexes[len(indexes)-1].PatternVersion
	// log.Infof("本地版本:[engine:%v, pattern:%v], 升级得到的版本:%+v, 表较方式:%v",
	// 	indexes[len(indexes)-1].EngineVersion, indexes[len(indexes)-1].PatternVersion, version, kind)

	evKind := kind.EngineVersionKind
	pvKind := kind.PatternVersionKind
	var EVUpdate, PVUpdate int
	var err error
	EVUpdate, err = CompareVersionCore(evFromArgs, evFromIndexBack, evKind)
	if err != nil {
		return false
	}
	PVUpdate, err = CompareVersionCore(pvFromArgs, pvFromIndexBack, pvKind)
	if err != nil {
		return false
	}

	switch {
	case EVUpdate == 1 && PVUpdate == 1:
		return true
	case EVUpdate == 0 && PVUpdate == 1:
		return true
	case EVUpdate == 1 && PVUpdate == 0:
		return true
	default:
		return false
	}
}

// 拷贝文件
func CopyFile(src, dest string) error {
	var err error
	var srcFileData *os.File
	var dstFileData *os.File
	var srcInfo os.FileInfo
	if srcFileData, err = os.Open(src); err != nil {
		return err
	}
	defer srcFileData.Close()
	if dstFileData, err = os.Create(dest); err != nil {
		return err
	}
	defer dstFileData.Close()
	if _, err = io.Copy(dstFileData, srcFileData); err != nil {
		return err
	}
	if srcInfo, err = os.Stat(src); err != nil {
		return err
	}
	return os.Chmod(dest, srcInfo.Mode())
}

// 拷贝src中的内容到dst目录中,若不存在则创建
func CopyDir(src, dest string) error {
	var err error
	var fdInfos []os.FileInfo
	var srcInfo os.FileInfo
	if srcInfo, err = os.Stat(src); err != nil {
		return err
	}
	if err = os.MkdirAll(dest, srcInfo.Mode()); err != nil {
		return err
	}
	if fdInfos, err = ioutil.ReadDir(src); err != nil {
		return err
	}
	for _, fd := range fdInfos {
		srcFilePath := path.Join(src, fd.Name())
		dstFilePath := path.Join(dest, fd.Name())
		if fd.IsDir() {
			if err = CopyDir(srcFilePath, dstFilePath); err != nil {
				return err
			}
		} else {
			if err = CopyFile(srcFilePath, dstFilePath); err != nil {
				return err
			}
		}
	}
	return nil
}

// 判断所给路径文件/文件夹是否存在
func checkFileIsExist(filename string) bool {
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		return false
	}
	return true
}

// 从指定文件中读取json内容
func getInfoFromJsonFile(path string, out interface{}) error {
	Buffer, err := ioutil.ReadFile(path)
	if err != nil {
		return err
	}
	err = json.Unmarshal(Buffer, out)
	if err != nil {
		return err
	}
	return nil
}

// 创建时间戳目录
func (h *updateHelper) createDir(path string) (string, error) {
	dirInfo, err := os.Stat(h.latestPath)
	if err != nil {
		return "", err
	}
	if err := os.MkdirAll(path, dirInfo.Mode()); err != nil {
		return "", err
	}
	return path, nil
}

// 从一个给定的路径中读取json内容，保存在index中返回，关键是他的返回值要赋值给本地缓存的，所以一定得留着
func getInfo(path string) (*Index, error) {
	if checkFileIsExist(path) == false {
		return nil, errors.New("failed to get infoFile path: %v, err: the file not exist")
	}
	var index Index
	// 从指定文件中获取得到json内容，保存在index中
	err := getJson(path, &index)
	if err != nil {
		return nil, errors.New("get json info from specify path file")
	}
	size := getDigitLen(index.UpdateTime)
	if size > 10 {
		index.UpdateTime /= int64(int(math.Pow10(size - 10)))
		_ = updateJson(path, index)
	}
	return &index, nil
}

func getDigitLen(x int64) int {
	cnt := 0
	for x != 0 {
		cnt++
		x /= 10
	}
	return cnt
}

func getSeparator(v string) string {
	switch {
	case strings.Contains(v, "+"):
		return "+"
	case strings.Contains(v, "-"):
		return "-"
	case strings.Contains(v, "."):
		return "."
	default:
		return ""
	}
}

func CompareVersionCore(lv, rv string, kind VersionKind) (int, error) {
	switch kind {
	case VersionKindSTRING:
		return strings.Compare(lv, rv), nil
	case VersionKindSEMVER:
		lFields := strings.Split(lv, getSeparator(lv))
		rFields := strings.Split(rv, getSeparator(rv))
		for i := 0; i < len(lFields); i++ {
			if i >= len(rFields) {
				return 1, nil // 左边>右边
			}

			if lFields[i] != "" && rFields[i] == "" {
				return 1, nil
			} else if lFields[i] == "" && rFields[i] != "" {
				return -1, nil
			} else if lFields[i] == "" && rFields[i] == "" {
				return 0, nil
			}

			lNum, err := strconv.Atoi(lFields[i])
			if err != nil {
				return 1, errors.New("invalid left version of SEMVER")
			}
			rNum, err := strconv.Atoi(rFields[i])
			if err != nil {
				return 1, errors.New("invalid right version of SEMVER")
			}

			if lNum > rNum {
				return 1, nil
			} else if lNum == rNum {
				continue
			} else {
				return -1, nil // 左边<右边
			}
		}
		if len(lFields) == len(rFields) {
			return 0, nil
		} else {
			return -1, nil
		}
	default:
		return 0, errors.New("unknown version kind")
	}
}

// 保存json 将json内容保存至 指定的目录下
func updateJson(path string, in interface{}) error {
	data, err := json.MarshalIndent(in, "", "\t")
	if err != nil {
		return err
	}
	err = ioutil.WriteFile(path, data, 0666)
	if err != nil {
		return err
	}
	return nil
}

// 读取json
func getJson(path string, out interface{}) error {
	Buffer, err := ioutil.ReadFile(path)
	if err != nil {
		return err
	}
	err = json.Unmarshal(Buffer, out)
	if err != nil {
		return err
	}
	return nil
}

func renameFile(src, dst string) error {
	if ok := checkFileIsExist(src); ok == false {
		return errors.New("assign path not exist")
	}
	return os.Rename(src, dst)
}

// 索引比较, equal(true)
func compareIndexes(indexes1, indexes2 []Index) bool {
	if len(indexes1) != len(indexes2) {
		return false
	}
	for i, j := 0, len(indexes1); i != j; i++ {
		if indexes1[i].Folder != indexes2[i].Folder {
			return false
		}
		if indexes1[i].UpdateTime != indexes2[i].UpdateTime {
			return false
		}
	}
	return true
}
