// Code generated by MockGen. DO NOT EDIT.
// Source: git-biz.qianxin-inc.cn/zion-infra/kamala/apis/worker.git/api (interfaces: AssetClient)
//
// Generated by this command:
//
//	mockgen -destination mock/asset_client_mock.go -package=mock git-biz.qianxin-inc.cn/zion-infra/kamala/apis/worker.git/api AssetClient
//

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	kamala_entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	api "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/worker.git/api"
	empty "github.com/golang/protobuf/ptypes/empty"
	gomock "go.uber.org/mock/gomock"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// MockAssetClient is a mock of AssetClient interface.
type MockAssetClient struct {
	ctrl     *gomock.Controller
	recorder *MockAssetClientMockRecorder
	isgomock struct{}
}

// MockAssetClientMockRecorder is the mock recorder for MockAssetClient.
type MockAssetClientMockRecorder struct {
	mock *MockAssetClient
}

// NewMockAssetClient creates a new mock instance.
func NewMockAssetClient(ctrl *gomock.Controller) *MockAssetClient {
	mock := &MockAssetClient{ctrl: ctrl}
	mock.recorder = &MockAssetClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAssetClient) EXPECT() *MockAssetClientMockRecorder {
	return m.recorder
}

// CreateAsset mocks base method.
func (m *MockAssetClient) CreateAsset(ctx context.Context, in *api.CreateAsset_Request, opts ...grpc.CallOption) (*kamala_entities.Asset, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateAsset", varargs...)
	ret0, _ := ret[0].(*kamala_entities.Asset)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAsset indicates an expected call of CreateAsset.
func (mr *MockAssetClientMockRecorder) CreateAsset(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAsset", reflect.TypeOf((*MockAssetClient)(nil).CreateAsset), varargs...)
}

// DeleteAsset mocks base method.
func (m *MockAssetClient) DeleteAsset(ctx context.Context, in *api.DeleteAsset_Request, opts ...grpc.CallOption) (*empty.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteAsset", varargs...)
	ret0, _ := ret[0].(*empty.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteAsset indicates an expected call of DeleteAsset.
func (mr *MockAssetClientMockRecorder) DeleteAsset(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAsset", reflect.TypeOf((*MockAssetClient)(nil).DeleteAsset), varargs...)
}

// GetAsset mocks base method.
func (m *MockAssetClient) GetAsset(ctx context.Context, in *api.GetAsset_Request, opts ...grpc.CallOption) (*kamala_entities.Asset, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAsset", varargs...)
	ret0, _ := ret[0].(*kamala_entities.Asset)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAsset indicates an expected call of GetAsset.
func (mr *MockAssetClientMockRecorder) GetAsset(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAsset", reflect.TypeOf((*MockAssetClient)(nil).GetAsset), varargs...)
}

// UpdateAsset mocks base method.
func (m *MockAssetClient) UpdateAsset(ctx context.Context, in *api.UpdateAsset_Request, opts ...grpc.CallOption) (*kamala_entities.Asset, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateAsset", varargs...)
	ret0, _ := ret[0].(*kamala_entities.Asset)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAsset indicates an expected call of UpdateAsset.
func (mr *MockAssetClientMockRecorder) UpdateAsset(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAsset", reflect.TypeOf((*MockAssetClient)(nil).UpdateAsset), varargs...)
}
