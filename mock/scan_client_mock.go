// Code generated by MockGen. DO NOT EDIT.
// Source: git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git/api (interfaces: ScanApiClient)
//
// Generated by this command:
//
//	mockgen -destination mock/scan_client_mock.go -package=mock git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git/api ScanApiClient
//

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	kamala_scan "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git"
	kamala_scan_api "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git/api"
	empty "github.com/golang/protobuf/ptypes/empty"
	gomock "go.uber.org/mock/gomock"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// MockScanApiClient is a mock of ScanApiClient interface.
type MockScanApiClient struct {
	ctrl     *gomock.Controller
	recorder *MockScanApiClientMockRecorder
	isgomock struct{}
}

// MockScanApiClientMockRecorder is the mock recorder for MockScanApiClient.
type MockScanApiClientMockRecorder struct {
	mock *MockScanApiClient
}

// NewMockScanApiClient creates a new mock instance.
func NewMockScanApiClient(ctrl *gomock.Controller) *MockScanApiClient {
	mock := &MockScanApiClient{ctrl: ctrl}
	mock.recorder = &MockScanApiClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockScanApiClient) EXPECT() *MockScanApiClientMockRecorder {
	return m.recorder
}

// Exit mocks base method.
func (m *MockScanApiClient) Exit(ctx context.Context, in *empty.Empty, opts ...grpc.CallOption) (*empty.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Exit", varargs...)
	ret0, _ := ret[0].(*empty.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Exit indicates an expected call of Exit.
func (mr *MockScanApiClientMockRecorder) Exit(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Exit", reflect.TypeOf((*MockScanApiClient)(nil).Exit), varargs...)
}

// Ping mocks base method.
func (m *MockScanApiClient) Ping(ctx context.Context, in *empty.Empty, opts ...grpc.CallOption) (*kamala_scan_api.Ping_Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Ping", varargs...)
	ret0, _ := ret[0].(*kamala_scan_api.Ping_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Ping indicates an expected call of Ping.
func (mr *MockScanApiClientMockRecorder) Ping(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Ping", reflect.TypeOf((*MockScanApiClient)(nil).Ping), varargs...)
}

// Reload mocks base method.
func (m *MockScanApiClient) Reload(ctx context.Context, in *kamala_scan_api.Reload_Request, opts ...grpc.CallOption) (*kamala_scan.Version, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Reload", varargs...)
	ret0, _ := ret[0].(*kamala_scan.Version)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Reload indicates an expected call of Reload.
func (mr *MockScanApiClientMockRecorder) Reload(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reload", reflect.TypeOf((*MockScanApiClient)(nil).Reload), varargs...)
}

// Scan mocks base method.
func (m *MockScanApiClient) Scan(ctx context.Context, in *kamala_scan_api.Scan_Request, opts ...grpc.CallOption) (*kamala_scan_api.Scan_Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Scan", varargs...)
	ret0, _ := ret[0].(*kamala_scan_api.Scan_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Scan indicates an expected call of Scan.
func (mr *MockScanApiClientMockRecorder) Scan(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Scan", reflect.TypeOf((*MockScanApiClient)(nil).Scan), varargs...)
}

// Update mocks base method.
func (m *MockScanApiClient) Update(ctx context.Context, in *kamala_scan_api.Update_Request, opts ...grpc.CallOption) (*kamala_scan.Version, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Update", varargs...)
	ret0, _ := ret[0].(*kamala_scan.Version)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockScanApiClientMockRecorder) Update(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockScanApiClient)(nil).Update), varargs...)
}
