// Code generated by MockGen. DO NOT EDIT.
// Source: git-biz.qianxin-inc.cn/zion-infra/kamala/apis/worker.git/api (interfaces: TaskClient)
//
// Generated by this command:
//
//	mockgen -destination mock/task_client_mock.go -package=mock git-biz.qianxin-inc.cn/zion-infra/kamala/apis/worker.git/api TaskClient
//

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	kamala_entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	api "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/worker.git/api"
	empty "github.com/golang/protobuf/ptypes/empty"
	gomock "go.uber.org/mock/gomock"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// MockTaskClient is a mock of TaskClient interface.
type MockTaskClient struct {
	ctrl     *gomock.Controller
	recorder *MockTaskClientMockRecorder
	isgomock struct{}
}

// MockTaskClientMockRecorder is the mock recorder for MockTaskClient.
type MockTaskClientMockRecorder struct {
	mock *MockTaskClient
}

// NewMockTaskClient creates a new mock instance.
func NewMockTaskClient(ctrl *gomock.Controller) *MockTaskClient {
	mock := &MockTaskClient{ctrl: ctrl}
	mock.recorder = &MockTaskClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTaskClient) EXPECT() *MockTaskClientMockRecorder {
	return m.recorder
}

// BatchCreateTasks mocks base method.
func (m *MockTaskClient) BatchCreateTasks(ctx context.Context, in *api.BatchCreateTasks_Request, opts ...grpc.CallOption) (*api.BatchCreateTasks_Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchCreateTasks", varargs...)
	ret0, _ := ret[0].(*api.BatchCreateTasks_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCreateTasks indicates an expected call of BatchCreateTasks.
func (mr *MockTaskClientMockRecorder) BatchCreateTasks(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreateTasks", reflect.TypeOf((*MockTaskClient)(nil).BatchCreateTasks), varargs...)
}

// CreateTask mocks base method.
func (m *MockTaskClient) CreateTask(ctx context.Context, in *api.CreateTask_Request, opts ...grpc.CallOption) (*kamala_entities.Task, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateTask", varargs...)
	ret0, _ := ret[0].(*kamala_entities.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTask indicates an expected call of CreateTask.
func (mr *MockTaskClientMockRecorder) CreateTask(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTask", reflect.TypeOf((*MockTaskClient)(nil).CreateTask), varargs...)
}

// DeleteTask mocks base method.
func (m *MockTaskClient) DeleteTask(ctx context.Context, in *api.DeleteTask_Request, opts ...grpc.CallOption) (*empty.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteTask", varargs...)
	ret0, _ := ret[0].(*empty.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTask indicates an expected call of DeleteTask.
func (mr *MockTaskClientMockRecorder) DeleteTask(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTask", reflect.TypeOf((*MockTaskClient)(nil).DeleteTask), varargs...)
}

// FetchTasks mocks base method.
func (m *MockTaskClient) FetchTasks(ctx context.Context, in *api.FetchTasks_Request, opts ...grpc.CallOption) (*api.FetchTasks_Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchTasks", varargs...)
	ret0, _ := ret[0].(*api.FetchTasks_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchTasks indicates an expected call of FetchTasks.
func (mr *MockTaskClientMockRecorder) FetchTasks(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchTasks", reflect.TypeOf((*MockTaskClient)(nil).FetchTasks), varargs...)
}

// UpdateTask mocks base method.
func (m *MockTaskClient) UpdateTask(ctx context.Context, in *api.UpdateTask_Request, opts ...grpc.CallOption) (*kamala_entities.Task, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateTask", varargs...)
	ret0, _ := ret[0].(*kamala_entities.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTask indicates an expected call of UpdateTask.
func (mr *MockTaskClientMockRecorder) UpdateTask(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTask", reflect.TypeOf((*MockTaskClient)(nil).UpdateTask), varargs...)
}
