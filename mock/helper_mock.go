// Code generated by MockGen. DO NOT EDIT.
// Source: git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/helper (interfaces: UpdateHelper)
//
// Generated by this command:
//
//	mockgen -destination mock/helper_mock.go -package=mock git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/helper UpdateHelper
//

// Package mock is a generated GoMock package.
package mock

import (
	io "io"
	reflect "reflect"

	helper "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/helper"
	gomock "go.uber.org/mock/gomock"
)

// MockUpdateHelper is a mock of UpdateHelper interface.
type MockUpdateHelper struct {
	ctrl     *gomock.Controller
	recorder *MockUpdateHelperMockRecorder
	isgomock struct{}
}

// MockUpdateHelperMockRecorder is the mock recorder for MockUpdateHelper.
type MockUpdateHelperMockRecorder struct {
	mock *MockUpdateHelper
}

// NewMockUpdateHelper creates a new mock instance.
func NewMockUpdateHelper(ctrl *gomock.Controller) *MockUpdateHelper {
	mock := &MockUpdateHelper{ctrl: ctrl}
	mock.recorder = &MockUpdateHelperMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUpdateHelper) EXPECT() *MockUpdateHelperMockRecorder {
	return m.recorder
}

// CleanUpdateDir mocks base method.
func (m *MockUpdateHelper) CleanUpdateDir() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CleanUpdateDir")
	ret0, _ := ret[0].(error)
	return ret0
}

// CleanUpdateDir indicates an expected call of CleanUpdateDir.
func (mr *MockUpdateHelperMockRecorder) CleanUpdateDir() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CleanUpdateDir", reflect.TypeOf((*MockUpdateHelper)(nil).CleanUpdateDir))
}

// Download mocks base method.
func (m *MockUpdateHelper) Download(url string) (io.ReadCloser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Download", url)
	ret0, _ := ret[0].(io.ReadCloser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Download indicates an expected call of Download.
func (mr *MockUpdateHelperMockRecorder) Download(url any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Download", reflect.TypeOf((*MockUpdateHelper)(nil).Download), url)
}

// GetDir mocks base method.
func (m *MockUpdateHelper) GetDir(PatternVersion, EngineVersion string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDir", PatternVersion, EngineVersion)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDir indicates an expected call of GetDir.
func (mr *MockUpdateHelperMockRecorder) GetDir(PatternVersion, EngineVersion any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDir", reflect.TypeOf((*MockUpdateHelper)(nil).GetDir), PatternVersion, EngineVersion)
}

// GetIndex mocks base method.
func (m *MockUpdateHelper) GetIndex() []helper.Index {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIndex")
	ret0, _ := ret[0].([]helper.Index)
	return ret0
}

// GetIndex indicates an expected call of GetIndex.
func (mr *MockUpdateHelperMockRecorder) GetIndex() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIndex", reflect.TypeOf((*MockUpdateHelper)(nil).GetIndex))
}

// GetLatestDir mocks base method.
func (m *MockUpdateHelper) GetLatestDir() (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestDir")
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestDir indicates an expected call of GetLatestDir.
func (mr *MockUpdateHelperMockRecorder) GetLatestDir() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestDir", reflect.TypeOf((*MockUpdateHelper)(nil).GetLatestDir))
}

// GetUpdateDir mocks base method.
func (m *MockUpdateHelper) GetUpdateDir() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUpdateDir")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetUpdateDir indicates an expected call of GetUpdateDir.
func (mr *MockUpdateHelperMockRecorder) GetUpdateDir() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpdateDir", reflect.TypeOf((*MockUpdateHelper)(nil).GetUpdateDir))
}

// RebuildIndex mocks base method.
func (m *MockUpdateHelper) RebuildIndex() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RebuildIndex")
	ret0, _ := ret[0].(error)
	return ret0
}

// RebuildIndex indicates an expected call of RebuildIndex.
func (mr *MockUpdateHelperMockRecorder) RebuildIndex() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RebuildIndex", reflect.TypeOf((*MockUpdateHelper)(nil).RebuildIndex))
}

// RestoreUpdateDir mocks base method.
func (m *MockUpdateHelper) RestoreUpdateDir(index *helper.Index, kind *helper.VersionDefine) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RestoreUpdateDir", index, kind)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RestoreUpdateDir indicates an expected call of RestoreUpdateDir.
func (mr *MockUpdateHelperMockRecorder) RestoreUpdateDir(index, kind any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RestoreUpdateDir", reflect.TypeOf((*MockUpdateHelper)(nil).RestoreUpdateDir), index, kind)
}
