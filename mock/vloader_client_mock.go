// Code generated by MockGen. DO NOT EDIT.
// Source: git-biz.qianxin-inc.cn/zion-infra/kamala/apis/vloader.git/api (interfaces: VirusLibraryClient)
//
// Generated by this command:
//
//	mockgen -destination mock/vloader_client_mock.go -package=mock git-biz.qianxin-inc.cn/zion-infra/kamala/apis/vloader.git/api VirusLibraryClient
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	kamala_api_vloader "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/vloader.git/api"
	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockVirusLibraryClient is a mock of VirusLibraryClient interface.
type MockVirusLibraryClient struct {
	ctrl     *gomock.Controller
	recorder *MockVirusLibraryClientMockRecorder
	isgomock struct{}
}

// MockVirusLibraryClientMockRecorder is the mock recorder for MockVirusLibraryClient.
type MockVirusLibraryClientMockRecorder struct {
	mock *MockVirusLibraryClient
}

// NewMockVirusLibraryClient creates a new mock instance.
func NewMockVirusLibraryClient(ctrl *gomock.Controller) *MockVirusLibraryClient {
	mock := &MockVirusLibraryClient{ctrl: ctrl}
	mock.recorder = &MockVirusLibraryClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockVirusLibraryClient) EXPECT() *MockVirusLibraryClientMockRecorder {
	return m.recorder
}

// DownloadVirusLibrary mocks base method.
func (m *MockVirusLibraryClient) DownloadVirusLibrary(ctx context.Context, in *kamala_api_vloader.DownloadVirusLibrary_Request, opts ...grpc.CallOption) (kamala_api_vloader.VirusLibrary_DownloadVirusLibraryClient, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DownloadVirusLibrary", varargs...)
	ret0, _ := ret[0].(kamala_api_vloader.VirusLibrary_DownloadVirusLibraryClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DownloadVirusLibrary indicates an expected call of DownloadVirusLibrary.
func (mr *MockVirusLibraryClientMockRecorder) DownloadVirusLibrary(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadVirusLibrary", reflect.TypeOf((*MockVirusLibraryClient)(nil).DownloadVirusLibrary), varargs...)
}

// GetVersion mocks base method.
func (m *MockVirusLibraryClient) GetVersion(ctx context.Context, in *kamala_api_vloader.GetVersion_Request, opts ...grpc.CallOption) (*kamala_api_vloader.GetVersion_Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetVersion", varargs...)
	ret0, _ := ret[0].(*kamala_api_vloader.GetVersion_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVersion indicates an expected call of GetVersion.
func (mr *MockVirusLibraryClientMockRecorder) GetVersion(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVersion", reflect.TypeOf((*MockVirusLibraryClient)(nil).GetVersion), varargs...)
}
