// Code generated by MockGen. DO NOT EDIT.
// Source: git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk (interfaces: StorageFileInterface)
//
// Generated by this command:
//
//	mockgen -destination /home/<USER>/qianxin/qax_warzone/collector/mock/storage_file_mock.go -package=mock git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk StorageFileInterface
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	http "net/http"
	reflect "reflect"

	base "git-biz.qianxin-inc.cn/zion-infra/data-foundation/apis/morpheus-api.git/generated-go/morpheus/base"
	sdk "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk"
	gomock "go.uber.org/mock/gomock"
)

// MockStorageFileInterface is a mock of StorageFileInterface interface.
type MockStorageFileInterface struct {
	ctrl     *gomock.Controller
	recorder *MockStorageFileInterfaceMockRecorder
	isgomock struct{}
}

// MockStorageFileInterfaceMockRecorder is the mock recorder for MockStorageFileInterface.
type MockStorageFileInterfaceMockRecorder struct {
	mock *MockStorageFileInterface
}

// NewMockStorageFileInterface creates a new mock instance.
func NewMockStorageFileInterface(ctrl *gomock.Controller) *MockStorageFileInterface {
	mock := &MockStorageFileInterface{ctrl: ctrl}
	mock.recorder = &MockStorageFileInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStorageFileInterface) EXPECT() *MockStorageFileInterfaceMockRecorder {
	return m.recorder
}

// CreateStorage mocks base method.
func (m *MockStorageFileInterface) CreateStorage(ctx context.Context, filePath, storageUri string) (*sdk.UploadInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateStorage", ctx, filePath, storageUri)
	ret0, _ := ret[0].(*sdk.UploadInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateStorage indicates an expected call of CreateStorage.
func (mr *MockStorageFileInterfaceMockRecorder) CreateStorage(ctx, filePath, storageUri any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateStorage", reflect.TypeOf((*MockStorageFileInterface)(nil).CreateStorage), ctx, filePath, storageUri)
}

// CreateStorageWithContent mocks base method.
func (m *MockStorageFileInterface) CreateStorageWithContent(ctx context.Context, content []byte, storageUri string) (*sdk.UploadInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateStorageWithContent", ctx, content, storageUri)
	ret0, _ := ret[0].(*sdk.UploadInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateStorageWithContent indicates an expected call of CreateStorageWithContent.
func (mr *MockStorageFileInterfaceMockRecorder) CreateStorageWithContent(ctx, content, storageUri any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateStorageWithContent", reflect.TypeOf((*MockStorageFileInterface)(nil).CreateStorageWithContent), ctx, content, storageUri)
}

// DownloadFile mocks base method.
func (m *MockStorageFileInterface) DownloadFile(ctx context.Context, url string) (*http.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DownloadFile", ctx, url)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DownloadFile indicates an expected call of DownloadFile.
func (mr *MockStorageFileInterfaceMockRecorder) DownloadFile(ctx, url any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadFile", reflect.TypeOf((*MockStorageFileInterface)(nil).DownloadFile), ctx, url)
}

// DownloadUri mocks base method.
func (m *MockStorageFileInterface) DownloadUri(ctx context.Context, resUri string) (*sdk.DownloadInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DownloadUri", ctx, resUri)
	ret0, _ := ret[0].(*sdk.DownloadInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DownloadUri indicates an expected call of DownloadUri.
func (mr *MockStorageFileInterfaceMockRecorder) DownloadUri(ctx, resUri any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadUri", reflect.TypeOf((*MockStorageFileInterface)(nil).DownloadUri), ctx, resUri)
}

// GetScanLogBySha1 mocks base method.
func (m *MockStorageFileInterface) GetScanLogBySha1(ctx context.Context, sha1 string) (*base.Storage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScanLogBySha1", ctx, sha1)
	ret0, _ := ret[0].(*base.Storage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScanLogBySha1 indicates an expected call of GetScanLogBySha1.
func (mr *MockStorageFileInterfaceMockRecorder) GetScanLogBySha1(ctx, sha1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScanLogBySha1", reflect.TypeOf((*MockStorageFileInterface)(nil).GetScanLogBySha1), ctx, sha1)
}

// GetStorage mocks base method.
func (m *MockStorageFileInterface) GetStorage(ctx context.Context, storageUri string) (*base.Storage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStorage", ctx, storageUri)
	ret0, _ := ret[0].(*base.Storage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStorage indicates an expected call of GetStorage.
func (mr *MockStorageFileInterfaceMockRecorder) GetStorage(ctx, storageUri any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStorage", reflect.TypeOf((*MockStorageFileInterface)(nil).GetStorage), ctx, storageUri)
}

// SetMaxErrorRetryTime mocks base method.
func (m *MockStorageFileInterface) SetMaxErrorRetryTime(retryTimes int) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetMaxErrorRetryTime", retryTimes)
}

// SetMaxErrorRetryTime indicates an expected call of SetMaxErrorRetryTime.
func (mr *MockStorageFileInterfaceMockRecorder) SetMaxErrorRetryTime(retryTimes any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMaxErrorRetryTime", reflect.TypeOf((*MockStorageFileInterface)(nil).SetMaxErrorRetryTime), retryTimes)
}

// Sha1Sum mocks base method.
func (m *MockStorageFileInterface) Sha1Sum(content []byte) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Sha1Sum", content)
	ret0, _ := ret[0].(string)
	return ret0
}

// Sha1Sum indicates an expected call of Sha1Sum.
func (mr *MockStorageFileInterfaceMockRecorder) Sha1Sum(content any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Sha1Sum", reflect.TypeOf((*MockStorageFileInterface)(nil).Sha1Sum), content)
}
