// Code generated by MockGen. DO NOT EDIT.
// Source: dialer.go
//
// Generated by this command:
//
//	mockgen -source=dialer.go -destination=../mock/dialer_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	kamala_api_model "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/model.git/v2/api"
	kamala_scan_api "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git/api"
	kamala_api_vloader "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/vloader.git/api"
	api "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/worker.git/api"
	sdk "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk"
	gomock "go.uber.org/mock/gomock"
)

// MockDialer is a mock of Dialer interface.
type MockDialer struct {
	ctrl     *gomock.Controller
	recorder *MockDialerMockRecorder
	isgomock struct{}
}

// MockDialerMockRecorder is the mock recorder for MockDialer.
type MockDialerMockRecorder struct {
	mock *MockDialer
}

// NewMockDialer creates a new mock instance.
func NewMockDialer(ctrl *gomock.Controller) *MockDialer {
	mock := &MockDialer{ctrl: ctrl}
	mock.recorder = &MockDialerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDialer) EXPECT() *MockDialerMockRecorder {
	return m.recorder
}

// FetchAssetClient mocks base method.
func (m *MockDialer) FetchAssetClient(ctx context.Context) (api.AssetClient, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchAssetClient", ctx)
	ret0, _ := ret[0].(api.AssetClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchAssetClient indicates an expected call of FetchAssetClient.
func (mr *MockDialerMockRecorder) FetchAssetClient(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchAssetClient", reflect.TypeOf((*MockDialer)(nil).FetchAssetClient), ctx)
}

// FetchRelayClient mocks base method.
func (m *MockDialer) FetchRelayClient(ctx context.Context) (api.TaskClient, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchRelayClient", ctx)
	ret0, _ := ret[0].(api.TaskClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchRelayClient indicates an expected call of FetchRelayClient.
func (mr *MockDialerMockRecorder) FetchRelayClient(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchRelayClient", reflect.TypeOf((*MockDialer)(nil).FetchRelayClient), ctx)
}

// FetchScannerClient mocks base method.
func (m *MockDialer) FetchScannerClient(ctx context.Context) (kamala_scan_api.ScanApiClient, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchScannerClient", ctx)
	ret0, _ := ret[0].(kamala_scan_api.ScanApiClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchScannerClient indicates an expected call of FetchScannerClient.
func (mr *MockDialerMockRecorder) FetchScannerClient(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchScannerClient", reflect.TypeOf((*MockDialer)(nil).FetchScannerClient), ctx)
}

// FetchStorageSDK mocks base method.
func (m *MockDialer) FetchStorageSDK(ctx context.Context) (*sdk.StorageFile, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchStorageSDK", ctx)
	ret0, _ := ret[0].(*sdk.StorageFile)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchStorageSDK indicates an expected call of FetchStorageSDK.
func (mr *MockDialerMockRecorder) FetchStorageSDK(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchStorageSDK", reflect.TypeOf((*MockDialer)(nil).FetchStorageSDK), ctx)
}

// FetchTaskClient mocks base method.
func (m *MockDialer) FetchTaskClient(ctx context.Context) (api.TaskClient, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchTaskClient", ctx)
	ret0, _ := ret[0].(api.TaskClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchTaskClient indicates an expected call of FetchTaskClient.
func (mr *MockDialerMockRecorder) FetchTaskClient(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchTaskClient", reflect.TypeOf((*MockDialer)(nil).FetchTaskClient), ctx)
}

// FetchVersionClient mocks base method.
func (m *MockDialer) FetchVersionClient(ctx context.Context) (kamala_api_model.VersionClient, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchVersionClient", ctx)
	ret0, _ := ret[0].(kamala_api_model.VersionClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchVersionClient indicates an expected call of FetchVersionClient.
func (mr *MockDialerMockRecorder) FetchVersionClient(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchVersionClient", reflect.TypeOf((*MockDialer)(nil).FetchVersionClient), ctx)
}

// FetchVloaderClient mocks base method.
func (m *MockDialer) FetchVloaderClient(ctx context.Context, asset string) (kamala_api_vloader.VirusLibraryClient, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchVloaderClient", ctx, asset)
	ret0, _ := ret[0].(kamala_api_vloader.VirusLibraryClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchVloaderClient indicates an expected call of FetchVloaderClient.
func (mr *MockDialerMockRecorder) FetchVloaderClient(ctx, asset any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchVloaderClient", reflect.TypeOf((*MockDialer)(nil).FetchVloaderClient), ctx, asset)
}
