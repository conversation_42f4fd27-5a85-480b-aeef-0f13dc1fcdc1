// Code generated by MockGen. DO NOT EDIT.
// Source: git-biz.qianxin-inc.cn/zion-infra/kamala/apis/vloader.git/api (interfaces: VirusLibrary_DownloadVirusLibraryClient)
//
// Generated by this command:
//
//	mockgen -destination mock/virus_library_download_client_mock.go -package=mock git-biz.qianxin-inc.cn/zion-infra/kamala/apis/vloader.git/api VirusLibrary_DownloadVirusLibraryClient
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	kamala_api_vloader "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/vloader.git/api"
	gomock "go.uber.org/mock/gomock"
	metadata "google.golang.org/grpc/metadata"
)

// MockVirusLibrary_DownloadVirusLibraryClient is a mock of VirusLibrary_DownloadVirusLibraryClient interface.
type MockVirusLibrary_DownloadVirusLibraryClient struct {
	ctrl     *gomock.Controller
	recorder *MockVirusLibrary_DownloadVirusLibraryClientMockRecorder
	isgomock struct{}
}

// MockVirusLibrary_DownloadVirusLibraryClientMockRecorder is the mock recorder for MockVirusLibrary_DownloadVirusLibraryClient.
type MockVirusLibrary_DownloadVirusLibraryClientMockRecorder struct {
	mock *MockVirusLibrary_DownloadVirusLibraryClient
}

// NewMockVirusLibrary_DownloadVirusLibraryClient creates a new mock instance.
func NewMockVirusLibrary_DownloadVirusLibraryClient(ctrl *gomock.Controller) *MockVirusLibrary_DownloadVirusLibraryClient {
	mock := &MockVirusLibrary_DownloadVirusLibraryClient{ctrl: ctrl}
	mock.recorder = &MockVirusLibrary_DownloadVirusLibraryClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockVirusLibrary_DownloadVirusLibraryClient) EXPECT() *MockVirusLibrary_DownloadVirusLibraryClientMockRecorder {
	return m.recorder
}

// CloseSend mocks base method.
func (m *MockVirusLibrary_DownloadVirusLibraryClient) CloseSend() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloseSend")
	ret0, _ := ret[0].(error)
	return ret0
}

// CloseSend indicates an expected call of CloseSend.
func (mr *MockVirusLibrary_DownloadVirusLibraryClientMockRecorder) CloseSend() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseSend", reflect.TypeOf((*MockVirusLibrary_DownloadVirusLibraryClient)(nil).CloseSend))
}

// Context mocks base method.
func (m *MockVirusLibrary_DownloadVirusLibraryClient) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockVirusLibrary_DownloadVirusLibraryClientMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockVirusLibrary_DownloadVirusLibraryClient)(nil).Context))
}

// Header mocks base method.
func (m *MockVirusLibrary_DownloadVirusLibraryClient) Header() (metadata.MD, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Header")
	ret0, _ := ret[0].(metadata.MD)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Header indicates an expected call of Header.
func (mr *MockVirusLibrary_DownloadVirusLibraryClientMockRecorder) Header() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Header", reflect.TypeOf((*MockVirusLibrary_DownloadVirusLibraryClient)(nil).Header))
}

// Recv mocks base method.
func (m *MockVirusLibrary_DownloadVirusLibraryClient) Recv() (*kamala_api_vloader.DownloadVirusLibrary_Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Recv")
	ret0, _ := ret[0].(*kamala_api_vloader.DownloadVirusLibrary_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Recv indicates an expected call of Recv.
func (mr *MockVirusLibrary_DownloadVirusLibraryClientMockRecorder) Recv() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Recv", reflect.TypeOf((*MockVirusLibrary_DownloadVirusLibraryClient)(nil).Recv))
}

// RecvMsg mocks base method.
func (m_2 *MockVirusLibrary_DownloadVirusLibraryClient) RecvMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "RecvMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecvMsg indicates an expected call of RecvMsg.
func (mr *MockVirusLibrary_DownloadVirusLibraryClientMockRecorder) RecvMsg(m any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvMsg", reflect.TypeOf((*MockVirusLibrary_DownloadVirusLibraryClient)(nil).RecvMsg), m)
}

// SendMsg mocks base method.
func (m_2 *MockVirusLibrary_DownloadVirusLibraryClient) SendMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "SendMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMsg indicates an expected call of SendMsg.
func (mr *MockVirusLibrary_DownloadVirusLibraryClientMockRecorder) SendMsg(m any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsg", reflect.TypeOf((*MockVirusLibrary_DownloadVirusLibraryClient)(nil).SendMsg), m)
}

// Trailer mocks base method.
func (m *MockVirusLibrary_DownloadVirusLibraryClient) Trailer() metadata.MD {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Trailer")
	ret0, _ := ret[0].(metadata.MD)
	return ret0
}

// Trailer indicates an expected call of Trailer.
func (mr *MockVirusLibrary_DownloadVirusLibraryClientMockRecorder) Trailer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Trailer", reflect.TypeOf((*MockVirusLibrary_DownloadVirusLibraryClient)(nil).Trailer))
}
