// Code generated by MockGen. DO NOT EDIT.
// Source: git-biz.qianxin-inc.cn/zion-infra/kamala/apis/model.git/v2/api (interfaces: VersionClient)
//
// Generated by this command:
//
//	mockgen -destination mock/version_client_mock.go -package=mock git-biz.qianxin-inc.cn/zion-infra/kamala/apis/model.git/v2/api VersionClient
//

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	kamala_api_model "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/model.git/v2/api"
	empty "github.com/golang/protobuf/ptypes/empty"
	gomock "go.uber.org/mock/gomock"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// MockVersionClient is a mock of VersionClient interface.
type MockVersionClient struct {
	ctrl     *gomock.Controller
	recorder *MockVersionClientMockRecorder
	isgomock struct{}
}

// MockVersionClientMockRecorder is the mock recorder for MockVersionClient.
type MockVersionClientMockRecorder struct {
	mock *MockVersionClient
}

// NewMockVersionClient creates a new mock instance.
func NewMockVersionClient(ctrl *gomock.Controller) *MockVersionClient {
	mock := &MockVersionClient{ctrl: ctrl}
	mock.recorder = &MockVersionClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockVersionClient) EXPECT() *MockVersionClientMockRecorder {
	return m.recorder
}

// BatchCreateVersion mocks base method.
func (m *MockVersionClient) BatchCreateVersion(ctx context.Context, in *kamala_api_model.BatchCreateVersion_Request, opts ...grpc.CallOption) (*kamala_api_model.BatchCreateVersion_Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchCreateVersion", varargs...)
	ret0, _ := ret[0].(*kamala_api_model.BatchCreateVersion_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCreateVersion indicates an expected call of BatchCreateVersion.
func (mr *MockVersionClientMockRecorder) BatchCreateVersion(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreateVersion", reflect.TypeOf((*MockVersionClient)(nil).BatchCreateVersion), varargs...)
}

// CreateVersion mocks base method.
func (m *MockVersionClient) CreateVersion(ctx context.Context, in *kamala_api_model.CreateVersion_Request, opts ...grpc.CallOption) (*kamala_api_model.CreateVersion_Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateVersion", varargs...)
	ret0, _ := ret[0].(*kamala_api_model.CreateVersion_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateVersion indicates an expected call of CreateVersion.
func (mr *MockVersionClientMockRecorder) CreateVersion(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateVersion", reflect.TypeOf((*MockVersionClient)(nil).CreateVersion), varargs...)
}

// DeleteVersion mocks base method.
func (m *MockVersionClient) DeleteVersion(ctx context.Context, in *kamala_api_model.DeleteVersion_Request, opts ...grpc.CallOption) (*empty.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteVersion", varargs...)
	ret0, _ := ret[0].(*empty.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteVersion indicates an expected call of DeleteVersion.
func (mr *MockVersionClientMockRecorder) DeleteVersion(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteVersion", reflect.TypeOf((*MockVersionClient)(nil).DeleteVersion), varargs...)
}

// GetMaxVersion mocks base method.
func (m *MockVersionClient) GetMaxVersion(ctx context.Context, in *kamala_api_model.GetMaxVersion_Request, opts ...grpc.CallOption) (*kamala_api_model.GetMaxVersion_Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMaxVersion", varargs...)
	ret0, _ := ret[0].(*kamala_api_model.GetMaxVersion_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaxVersion indicates an expected call of GetMaxVersion.
func (mr *MockVersionClientMockRecorder) GetMaxVersion(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxVersion", reflect.TypeOf((*MockVersionClient)(nil).GetMaxVersion), varargs...)
}

// GetVersion mocks base method.
func (m *MockVersionClient) GetVersion(ctx context.Context, in *kamala_api_model.GetVersion_Request, opts ...grpc.CallOption) (*kamala_api_model.GetVersion_Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetVersion", varargs...)
	ret0, _ := ret[0].(*kamala_api_model.GetVersion_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVersion indicates an expected call of GetVersion.
func (mr *MockVersionClientMockRecorder) GetVersion(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVersion", reflect.TypeOf((*MockVersionClient)(nil).GetVersion), varargs...)
}

// ListVersions mocks base method.
func (m *MockVersionClient) ListVersions(ctx context.Context, in *kamala_api_model.ListVersions_Request, opts ...grpc.CallOption) (*kamala_api_model.ListVersions_Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListVersions", varargs...)
	ret0, _ := ret[0].(*kamala_api_model.ListVersions_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListVersions indicates an expected call of ListVersions.
func (mr *MockVersionClientMockRecorder) ListVersions(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListVersions", reflect.TypeOf((*MockVersionClient)(nil).ListVersions), varargs...)
}

// UpdateVersion mocks base method.
func (m *MockVersionClient) UpdateVersion(ctx context.Context, in *kamala_api_model.UpdateVersion_Request, opts ...grpc.CallOption) (*kamala_api_model.UpdateVersion_Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateVersion", varargs...)
	ret0, _ := ret[0].(*kamala_api_model.UpdateVersion_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateVersion indicates an expected call of UpdateVersion.
func (mr *MockVersionClientMockRecorder) UpdateVersion(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateVersion", reflect.TypeOf((*MockVersionClient)(nil).UpdateVersion), varargs...)
}
