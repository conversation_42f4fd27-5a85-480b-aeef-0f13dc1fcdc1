# collector
variables:
  #GIT_SUBMODULE_STRATEGY: recursive
  CI_REGISTRY_IMAGE: harbor.ops.qianxin-inc.cn/zion/kamala-collector
  # go build 生成的二进制文件名
  PROJECT_NAME: kamala-collector
  # 镜像推送的darwin通道
  DARWIN_CHANNEL: kimage_collector
  GO_TEST_COMMAND: go test -v -coverprofile=./allcover.out ./...
  # gitlab runnner tag
  RUNNER_TAG: k8s


include:
  - project: 'zion-infra/kamala/warzone/ci-templete'
    ref: go122
    file: 'standard.yml'


