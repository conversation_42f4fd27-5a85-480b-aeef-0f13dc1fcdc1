#!/bin/bash

set -e
set -o pipefail

if [ "$1" = 'arm64' ]; then
    sed '1c FROM harbor.ops.qianxin-inc.cn/library/centos:centos8.3-arm64_v1.0' Dockerfile
    sed -i '1c FROM harbor.ops.qianxin-inc.cn/library/centos:centos8.3-arm64_v1.0' Dockerfile
elif [ "$1" = 'mips64le' ]; then
    sed '1c FROM harbor.ops.qianxin-inc.cn/library/debian:buster-20210816-mips64le' Dockerfile
    sed -i '1c FROM harbor.ops.qianxin-inc.cn/library/debian:buster-20210816-mips64le' Dockerfile
fi