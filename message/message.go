package message

import (
	"context"
	"encoding/base64"
	entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"
	"strings"
)

const (
	NAME   = "message"
	BASE64 = "base64"
	SEPARATOR = ":"
)

func Download(ctx context.Context, t *entities.Task, filepath string) (*sdk.DownloadInfo, error) {
	uri := t.GetFetchUri()
	var err error
	value := splitMessage(uri)
	content := t.GetMessage()
	var result []byte
	if value == BASE64 {
		result, err = base64.StdEncoding.DecodeString(content)
		if err != nil {
			return nil, err
		}
	} else {
		result = []byte(content)
	}
	err = util.SaveFile(result, filepath)
	if err != nil {
		return nil, err
	}
	return &sdk.DownloadInfo{Content: result, Length: uint64(len(result))}, nil
}

func Upload(ctx context.Context, t *entities.Task, filepath string) (*sdk.UploadInfo, error) {
	uri := t.GetStoreUri()
	value := splitMessage(uri)
	content, err := util.LoadFile(filepath)
	if err != nil {
		return nil, err
	}
	util.SetStoreData(t, content)

	var result string
	if value == BASE64 {
		result = base64.StdEncoding.EncodeToString(content)
	} else {
		result = string(content)
	}

	t.Message = result
	return &sdk.UploadInfo{}, nil
}

func splitMessage(uri string) string {
	var value string
	fields := strings.SplitN(uri, SEPARATOR, 2)
	if value = fields[0];len(fields) == 2 {
		value = fields[1]
	}
	return value
}