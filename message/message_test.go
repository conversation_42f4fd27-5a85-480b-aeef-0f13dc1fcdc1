package message

import (
	"context"
	"encoding/base64"
	"os"
	"path/filepath"
	"testing"
	"time"

	entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	"github.com/stretchr/testify/assert"
)

func createTestFile(file string) error {
	err := os.Mkdir(file, os.ModePerm)
	if err != nil {
		return err
	}
	return nil
}

func removeTestDir(dir string) error {
	d, err := os.Open(dir)
	if err != nil {
		return err
	}
	defer d.Close()
	names, err := d.Readdirnames(-1)
	if err != nil {
		return err
	}
	for _, name := range names {
		err = os.RemoveAll(filepath.Join(dir, name))
		if err != nil {
			return err
		}
	}
	if err := os.RemoveAll(dir); err != nil {
		return err
	}
	return nil
}

func TestDownload(t *testing.T) {
	err := createTestFile("testfile")
	assert.Equal(t, nil, err)
	// 采用message下载时，fetch_uri使用的是：message模式
	task := &entities.Task{
		Name:     "test1",
		FetchUri: "message:base64",
		Message:  base64.StdEncoding.EncodeToString([]byte("This is task1's result")),
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()
	info, err := Download(ctx, task, "./testfile/test1")
	if err != nil {
		t.Errorf("failed to download by message, error:%v", err)
	} else {
		t.Logf("download content : %v", string(info.Content))
	}
	// 采用message下载时，fetch_uri使用的是：message:base64模式，会对内容进行base64加密内容解密下载
	task = &entities.Task{
		Name:     "test2",
		FetchUri: "message",
		Message:  "This is test2's result",
	}
	info, err = Download(ctx, task, "./testfile/test2")
	if err != nil {
		t.Errorf("failed to download by message, error:%v", err)
	} else {
		t.Logf("download content : %v", string(info.Content))
	}
}

func TestUpload(t *testing.T) {
	// 采用message上传时，store_uri使用的是：message模式
	task := &entities.Task{
		Name:     "test1",
		StoreUri: "message",
		Message:  "This is test2's result",
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()
	_, err := Upload(ctx, task, "./testfile/test1")
	if err != nil {
		t.Errorf("failed to upload by message, error:%v", err)
	} else {
		t.Logf("upload content : %v", task.Message)
	}
	// 采用message上传时，store_uri使用的是：message:base64模式，会对内容进行base64加密上传
	task = &entities.Task{
		Name:     "test2",
		StoreUri: "message:base64",
		Message:  "This is test2's result",
	}
	_, err = Upload(ctx, task, "./testfile/test2")
	if err != nil {
		t.Errorf("failed to upload by message, error:%v", err)
	} else {
		t.Logf("upload content : %v", task.Message)
	}
	_ = removeTestDir("./testfile")
}
