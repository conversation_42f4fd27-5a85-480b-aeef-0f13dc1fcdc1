package main

import (
	"context"
	"fmt"
	"io/fs"
	"io/ioutil"
	"math"
	"os"
	"path"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"sync/atomic"
	"syscall"
	"time"

	scanadapter "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/adapters/scan"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers/scanner"

	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/helper"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/message"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers/task"
	vloaderController "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers/vloader"

	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/proxy"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"

	"git-open.qianxin-inc.cn/yuanruifeng/pqueue"

	entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"

	"github.com/judwhite/go-svc/svc"
	"github.com/panjf2000/ants"
)

// removed: toAdapterOps; callers now directly construct []scanadapter.ScanOperation

const InitVersion = "INIT_VERSION"

const (
	MB       = 1024 * 1024
	GB       = 1024 * MB
	LockFile = "lockfile"
)

var (
	smallFile  uint64 = 5 * GB
	middleFile uint64 = 10 * GB
	bigFile    uint64 = 20 * GB
	hugeFile   uint64 = 40 * GB

	smallFileEndPos  = 12
	middleFileEndPos = 14
	bigFileEndPos    = 15
	hugeFileEndPos   = 20
)

type DirInfo struct {
	Name string
	Size uint64
}

type FileLock struct {
	f       *os.File
	lockErr error
}

type LocalTask struct {
	id               int32
	task             *entities.Task
	filePath         string
	logPath          string
	logOutputDir     string // 鉴定结果输出的目录 位置：$KAMALA_SCANNER_LOG/$outdir_dir
	fetchTime        time.Time
	fetchDuration    time.Duration
	downloadDuration time.Duration
	scanDuration     time.Duration
	uploadDuration   time.Duration
	updateDuration   time.Duration
	lifeDuration     time.Duration

	downloadInfo *sdk.DownloadInfo
	uploadInfo   *sdk.UploadInfo

	fetchBatchCount    int
	scanBatchCount     int
	downloadRetryCount int
	uploadRetryCount   int
	scanRetryCount     int
	updateRetryCount   int
	waitDownload       uint64 // 表示触发下载暂停的具体样本大小

	downloadOk time.Time
	scanOk     time.Time
	uploadOk   time.Time

	waitScanTime     time.Duration
	waitDownloadTime time.Duration
	waitUploadTime   time.Duration
	waitUpdateTime   time.Duration

	totalTime   time.Duration
	lastTime    time.Duration
	usePlusDisk bool // 是否使用第二盘进行存储
	fileLock    *FileLock
}

func (s *scan) initDirList() {
	for _, dir := range getDirList() {
		dirPath := path.Join(s.plusFileDir, dir.Name)
		if !util.CheckFileIsExist(dirPath) {
			_ = os.MkdirAll(dirPath, os.ModePerm)
		}
		filePath := path.Join(dirPath, LockFile)
		if !util.CheckFileIsExist(path.Join(dirPath, LockFile)) {
			// log.Debugf("[test debug]:创建文件：%v", path.Join(dirPath, LockFile))
			_, _ = os.Create(filePath)
		}
	}
}

func (l *LocalTask) Less(other interface{}) bool {
	return l.task.Priority > other.(*LocalTask).task.Priority
}

func (l *LocalTask) Key() interface{} {
	return l
}

func (l *LocalTask) getOneScanDuration() time.Duration {
	int64ScanBatchCount := int64(l.scanBatchCount)
	if int64ScanBatchCount == 0 {
		return 0
	}
	return l.scanDuration / time.Duration(int64ScanBatchCount)
}

func (l *LocalTask) getOnceScanRetryCount() int {
	int64ScanBatchCount := l.scanBatchCount
	if int64ScanBatchCount == 0 {
		return 0
	}
	return l.scanRetryCount / int64ScanBatchCount
}

func (l *LocalTask) getOneFetchDuration() time.Duration {
	int64FetchBatchCount := int64(l.fetchBatchCount)
	if int64FetchBatchCount == 0 {
		return 0
	}
	return l.fetchDuration / time.Duration((int64FetchBatchCount))
}

func (l *LocalTask) getDownloadInfo() *sdk.DownloadInfo {
	if l.downloadInfo == nil {
		return &sdk.DownloadInfo{}
	}
	return l.downloadInfo
}

func (l *LocalTask) getUplaodInfo() *sdk.UploadInfo {
	if l.uploadInfo == nil {
		return &sdk.UploadInfo{}
	}
	return l.uploadInfo
}

type scan struct {
	util.LoopController
	scannerController controllers.ScannerController
	assetController   controllers.AssetController
	taskController    controllers.TaskController
	storageController storage.Storage
	vloaderController controllers.VloaderController

	taskChan   chan *LocalTask
	logChan    chan *LocalTask
	resultChan chan *LocalTask

	filePQueue pqueue.PQueue

	storagePool *ants.Pool
	updatePool  *ants.Pool

	taskId        int32
	taskCount     int32
	taskSize      int32  // TODO, fetch时可以不考虑size， 但下载时需要考虑， 同时由于日志目录与样本目录使用同一个卷， 因此需要同时考虑大小
	doneTaskCount uint32 // 鉴定器单次启动后已经扫描任务的总量

	helper helper.UpdateHelper
	// VirusLibraryClient kamala_api_vloader.VirusLibraryClient

	fileDir       string
	plusFileDir   string
	logDir        string
	preVersion    string
	latestVersion string // 记录collector现在所拥有的最新版本

	latestPing       time.Time
	preStartupTime   int64
	scannerStartTime int64
	emptyDirSize     uint64
	plusDirSize      uint64
	// 预估size，针对于size已知的样本，需要提前知道emptyDir空间是否足够,以判断是否采用第二块盘进行存储或此时停止fetch task
	estimateSize      uint64
	enableUsePlusDisk bool // 是否支持启用第二块盘的功能
	spaceInsufficient bool // 挂载的内存盘 emptyDir 空间是否不足
	enableOutputDir   bool // 鉴定器输出目录
}

func getDirList() []DirInfo {
	var dirList []DirInfo
	dirList = append(dirList, getSmallDirList()...)
	dirList = append(dirList, getMiddleDirList()...)
	dirList = append(dirList, getBigDirList()...)
	return dirList
}

func NewScan(dialer util.Dialer, fileDir, plusFileDir, logDir string) svc.Service {
	scannerController, assetController := GetBasicController(dialer)
	h := helper.NewUpdateHelper(os.Getenv("KAMALA_UPDATE_ROOT"))
	valoderController := vloaderController.NewController(scannerController, assetController, h, dialer)
	taskController := task.NewController(dialer, assetController, scannerController)
	concurrency := int(assetController.GetAsset().GetScanFeature().GetMaxFetchCount()/2 + 1)
	if concurrency <= 0 {
		concurrency = 1
	}
	pool, err := ants.NewPool(concurrency)
	if err != nil {
		log.Errorf("failed to get new pool for storage, err: %v", err)
	}

	updatePool, err := ants.NewPool(concurrency)
	if err != nil {
		log.Errorf("failed to get new pool for update, err: %v", err)
	}

	pool.Running()
	updatePool.Running()
	service := &scan{
		scannerController: scannerController,
		assetController:   assetController,
		taskController:    taskController,
		storageController: proxy.GetStorage(),
		vloaderController: valoderController,

		taskChan:   make(chan *LocalTask, assetController.GetAsset().GetScanFeature().GetMaxFetchCount()),
		logChan:    make(chan *LocalTask, assetController.GetAsset().GetScanFeature().GetMaxFetchCount()),
		resultChan: make(chan *LocalTask, assetController.GetAsset().GetScanFeature().GetMaxFetchCount()),

		filePQueue: pqueue.New(pqueue.Size(uint(assetController.GetAsset().GetScanFeature().GetMaxFetchSize()))),

		fileDir:     fileDir,
		plusFileDir: plusFileDir,
		logDir:      logDir,

		storagePool: pool,
		updatePool:  updatePool,
		helper:      h,
		// VirusLibraryClient: kamala_api_vloader.NewVirusLibraryClient(dialer_go_framework.ClientConn),
		preVersion:       InitVersion,
		latestVersion:    "",
		scannerStartTime: time.Now().Add(-time.Duration(scannerController.GetScanner().GetStartupSeconds()) * time.Second).Unix(),
		emptyDirSize:     assetController.GetAsset().GetDeployFeature().GetSharedDirSize(),
		plusDirSize:      100 * GB,
	}
	if !util.CheckFileIsExist(service.fileDir) {
		err = os.MkdirAll(service.fileDir, 0o755)
	}
	if !util.CheckFileIsExist(service.logDir) {
		err = os.MkdirAll(service.logDir, 0o755)
	}
	if !util.CheckFileIsExist(service.plusFileDir) {
		log.Fatalf("config not ready, KAMALA_SCANNER_FILE_PLUS dir: %v not exist", service.plusFileDir)
	}
	if err != nil {
		log.Fatalf("new scan server failed: %v", err)
	}
	log.Debugf("[test debug]:collector 刚刚启动时记录的start time：%v, %v", service.scannerStartTime, time.Now().Add(-time.Duration(service.scannerController.GetScanner().GetStartupSeconds())*time.Second))

	// 检查是否启用支持第二块盘进行存储
	if os.Getenv("ENABLE_USE_PLUS_DISK") == "yes" {
		service.enableUsePlusDisk = true
	}
	if os.Getenv("ENABLE_OUTPUT_DIR") == "yes" {
		service.enableOutputDir = true
	}
	log.Infof("[%v] enable use plus disk", service.enableUsePlusDisk)
	for {
		if asset := assetController.GetAsset(); asset == nil {
			time.Sleep(time.Second)
			log.Debugf("waiting to get asset info")
			continue
		} else if util.IsSelfUpdate(assetController.GetAsset()) {
			scannerController.SetState(util.FIRST_UPDATE)
			log.Debugf("self update type scanner first update,set state: %v", scannerController.GetState())
			begin := time.Now()
			version, err := scannerController.Update()
			if err != nil {
				time.Sleep(time.Second)
				log.Warnf("update when startup failed, err: %v", err)
				continue
			}
			// 在scan启动的时候，应该对scanner.Version里面的版本和升级信息也做好初始化，而不是等loopPing的时候做赋值操作
			// 因为Ping的时候其实是不会返回升级具体耗时的，所以应该在调用升级接口的时候就把具体的耗时统计出来，并赋值到scanner中
			// 在FetchTask时，上报的鉴定器信息时，具体升级的耗时也就上报上去了
			// 统一适配版本回填
			scannerController.GetScanner().Version.Engine = version.EngineVersion
			scannerController.GetScanner().Version.Pattern = version.PatternVersion
			scannerController.GetScanner().Version.UpdateTime = begin.Unix()
			scannerController.GetScanner().Version.UpdateDuration = fmt.Sprintf("%vs", time.Since(begin).Seconds())
			log.Infof("[%v] update when startup succeed, version: %+v", time.Since(begin), version)
		}
		break
	}

	for {
		res, err := scannerController.Ping()
		if err != nil {
			continue
		} else {
			service.preStartupTime = time.Now().Add(-time.Second * time.Duration(res.StartupSeconds)).Unix()
			log.Debugf("the preStartupTime: %v when NewScan ping response: %+v", service.preStartupTime, res)
			break
		}
	}
	scannerController.SetState(util.IDLE)
	log.Debugf("NewScan set state：IDLE,NewScan complete state: %v", scannerController.GetState())
	// TODO 每个collector启动之后需要确保第二块盘的相关目录是存在的
	service.initDirList()

	return service
}

func (s *scan) Init(env svc.Environment) error {
	if env.IsWindowsService() {
		// windows 环境特殊配置
	}
	log.Info("scan process init finished")
	return nil
}

func (s *scan) Start() error {
	s.Run(s.loopFetch)
	s.Run(s.loopDownload)
	s.Run(s.loopScan)
	s.Run(s.loopUpload)
	s.Run(s.loopDeliver)
	return nil
}

func (s *scan) Stop() error {
	s.scannerController.Finalize()
	s.assetController.Finalize()
	s.taskController.Finalize()

	s.LoopController.Stop = true
	s.Wait()
	return nil
}

func (s *scan) loopFetch() {
	ticker := time.Tick(300 * time.Millisecond)
	pingDuration := time.Second * 3
	for !s.LoopController.Stop {
		select {
		case <-ticker:
			if s.assetController.GetAsset() == nil {
				log.Warnf("[%v]asset is empty ....", s.assetController.GetAsset())
				continue
			}

			// 判断当前鉴定器状态是否为：Terminating 或 Startup，这两个状态分别表示：鉴定器正处于重启中，鉴定器重启完成正处于启动的准备阶段
			// 处于这两个状态下，暂时是先不做Fetch操作
			if s.scannerController.GetState() == util.TERMINATING || s.scannerController.GetState() == util.Startup {
				log.Infof("scanner: %v is restarting, state: %v", s.scannerController.GetScanner().GetName(),
					s.scannerController.GetState())
				continue
			}

			// 判断此时是否处于UpdateReady或者Updating状态，但若鉴定器的平均升级时间都是比较短的话，小于maxScanTimeout，
			// 则是可以做FetchTask的，若是升级时间比较大的话则直接continue等待升级完成；
			if s.scannerController.GetState() == util.UPDATE_READY || s.scannerController.GetState() == util.UPDATING {
				if s.getAvgUpdateTime() == 0 || int64(s.getAvgUpdateTime()) >= s.assetController.GetAsset().GetScanFeature().GetMaxScanTimeout()/1e9 {
					log.Infof("can't fetch task is updating, average [update time: %v] > [timeout: %v]",
						s.getAvgUpdateTime(), s.assetController.GetAsset().GetScanFeature().GetMaxScanTimeout()/1e9)
					continue
				}
			}

			count := int32(s.assetController.GetAsset().GetScanFeature().GetMaxFetchCount()) - atomic.LoadInt32(&s.taskCount)
			// log.Debugf("max fetch count: %v, taskCount: %v, count: %v", s.assetController.GetAsset().GetScanFeature().GetMaxFetchCount(),
			//	s.taskCount, count)
			if count <= 0 {
				// 默认模式下超过3s没有fetch操作就fetch0一下
				if time.Since(s.latestPing) >= pingDuration && s.taskController.GetMode() == 0 {
					fetchName := s.assetController.GetAsset().GetScanFeature().GetFetchName()
					if fetchName == "" {
						fetchName = s.assetController.GetAsset().GetName()
					}
					s.latestPing = time.Now()
					_ = s.taskController.FetchTask(fetchName, 0)
				}
				continue
			}

			// 此时collector本身是有能力承接新的 task了，但是在未启用超大磁盘的情况下，若是此时内存盘的空间大小不足则暂停 FetchTask
			if !s.enableUsePlusDisk && s.spaceInsufficient {
				log.Warnf("[enableUsePlusDisk:%v]empty dir space insufficient stop to fetch task ....", s.enableUsePlusDisk)
				time.Sleep(time.Second)
				continue
			}

			fetchName := s.assetController.GetAsset().GetScanFeature().GetFetchName()
			if fetchName == "" {
				fetchName = s.assetController.GetAsset().GetName()
			}

			begin := time.Now()
			s.latestPing = begin
			taskList := s.taskController.FetchTask(fetchName, count)
			duration := time.Since(begin)

			taskListSize := len(taskList)
			if taskListSize == 0 {
				// slow down fetch task when no task
				time.Sleep(time.Second)
				continue
			}
			// 高优先级先入channel
			sort.Slice(taskList, func(i, j int) bool {
				return taskList[i].Priority > taskList[j].Priority
			})
			for _, t := range taskList {
				atomic.AddInt32(&s.taskCount, 1)
				id := atomic.AddInt32(&s.taskId, 1)
				s.taskChan <- &LocalTask{id: id, task: t, fetchTime: begin, fetchDuration: duration, fetchBatchCount: taskListSize}
			}
		}
	}
	log.Infof("loopFetch closed")
}

func (s *scan) loopDownload() {
	ticker := time.Tick(1 * time.Second)
	for !s.LoopController.Stop {
		select {
		case <-ticker:
			// log.Debugf("[test debug]:loop download流程中, taskChan中没有任务了, taskChan len: %v", len(s.taskChan))
			continue
		case t := <-s.taskChan:
			taskSize := t.task.GetSize()
			var size int64
			var storageErr error
			if t.task.GetFetchUri() != "" {
				size, storageErr = util.GetStorageSize(t.task.GetFetchUri())
			} else {
				size, storageErr = util.GetStorageSize(t.task.GetSha1())
			}
			if storageErr != nil {
				log.Errorf("get storage size failed: %v", storageErr)
			}
			taskSize = uint64(size)
			// TODO Zip压缩包的样本实际的大小未知，将来针对这一情况需要做特殊处理
			t.task.Size = taskSize
			if s.enableUsePlusDisk {
				t.usePlusDisk = s.isUseDisk(taskSize)
			}
			// log.Infof("[loopDownload is use the second disk: %v", t.usePlusDisk)
			//if taskSize != 0 && taskSize != 1 {
			//	log.Debugf("[test debug]loopDownload:样本大小已知: %.2fMB, taskName: %v, 预计需要使用: %.2fMB 的emptyDir",
			//		float64(t.task.GetSize())/MB, t.task.GetName(), float64(s.estimateSize)/MB)
			//} else {
			//	log.Debugf("[test debug]loopDownload:样本大小未知: %v, taskName: %v, 预计需要使用: %.2fMB 的emptyDir",
			//		t.task.GetSize(), t.task.GetName(), float64(s.estimateSize)/MB)
			//}
			if !s.enableUsePlusDisk && taskSize != 0 && taskSize != 1 {
				maxFetchSize := s.getMaxFetchSize()
				// 如果该样本本身大小直接超过 MaxFetchSize的大小，那么该任务直接判定为失败，超过我们对外所承诺的最大样本大小上限值
				if taskSize > maxFetchSize {
					log.Warnf("[sha1: %v][taskSize: %v] > [maxFetchSize:%v]", t.task.GetSha1(),
						taskSize, s.estimateSize, maxFetchSize)
					t.task.Code = entities.ScanCode_DOWNLOAD_FAILURE
					t.downloadInfo = &sdk.DownloadInfo{Length: 0}
					t.task.Message = fmt.Sprintf("[sha1: %v] sample size : %v > maxFetchSize: %v",
						t.task.GetSha1(), taskSize, maxFetchSize)
					s.resultChan <- t
					break
				}
				atomic.AddUint64(&s.estimateSize, taskSize)

				log.Debugf("预估使用space: %v, emptyDir： %v, maxFetchSize: %v", s.estimateSize, s.emptyDirSize, maxFetchSize)
				if s.estimateSize > maxFetchSize {
					log.Warnf("[sha1: %v][taskSize: %v][estimateSize: %v] > [maxFetchSize:%v]  "+
						"can't fetch task", t.task.GetSha1(), taskSize, s.estimateSize, maxFetchSize)
					s.spaceInsufficient = true
					// atomic.AddUint64(&s.estimateSize, -taskSize)
					// 若此时内存盘空间不足，阻塞循环检测空间是否释放出来可以继续读取taskChan进行样本下载
					s.blockCheckSpaceCondition()
				} else {
					s.spaceInsufficient = false
				}
			}
			begin := time.Now()
			err := s.storagePool.Submit(func(t *LocalTask) func() {
				return func() {
					if timeout, err := s.toDownload(t); err != nil {
						log.Errorf("[%v] failed to download downloadTimeout: %v, err: %v", t.downloadDuration, timeout, err)
					}
				}
			}(t))

			duration := time.Since(begin)
			if err != nil {
				msg := fmt.Sprintf("[%v] failed to download task, err: %v", duration, err)
				t.task.Code = entities.ScanCode_OTHER
				t.task.Message = msg
				log.Errorf("[%v] failed to download task, err: %v, task: %v", duration, err, t.task)
				s.resultChan <- t
			}
		}
	}
	log.Info("loopDownload closed")
}

func (s *scan) loopScan() {
	scanLocalTasks := make(map[string]*LocalTask, 0)
	ticker := time.Tick(100 * time.Millisecond)
	for !s.LoopController.Stop {
		if s.assetController.GetAsset() == nil {
			continue
		}

		// 判断鉴定器是否到了单次启动后最大可处理的任务数量，若是到了则需要重启鉴定器, ReloadTaskCount=0表示没有限制
		if s.assetController.GetAsset().GetLivingFeature().GetReloadTaskCount() != 0 &&
			s.doneTaskCount >= s.assetController.GetAsset().GetLivingFeature().GetReloadTaskCount() {
			log.Infof("block to restart doneTaskCount: %v >= ReloadTaskCount: %v", s.doneTaskCount,
				s.assetController.GetAsset().GetLivingFeature().GetReloadTaskCount())
			s.blockRestart()
			s.doneTaskCount = 0
		}

		// 判断鉴定器是否到了单次启动后最大可生存的秒数，若是到了则需要重启鉴定器, ReloadInterval=0表示没有限制
		if s.assetController.GetAsset().GetLivingFeature().GetReloadInterval() != 0 &&
			s.scannerController.GetScanner().GetStartupSeconds() >= (s.assetController.GetAsset().GetLivingFeature().GetReloadInterval()/1e9) {
			log.Infof("block to restart StartupSeconds: %v >= ReloadInterval: %v", s.scannerController.GetScanner().GetStartupSeconds(),
				s.assetController.GetAsset().GetLivingFeature().GetReloadInterval()/1e9)
			s.blockRestart()
		}

		isTimeout := false
		select {
		case <-ticker:
			isTimeout = true
		default:
			if os.Getenv("IS_TRUST_SCANNER_STATE") == "true" {
				// 先ping一下， 如果ping失败，则不进行扫描
				resp, err := s.scannerController.Ping()
				if err != nil {
					log.Errorf("ping scanner failed: %v", err)
					time.Sleep(100 * time.Millisecond)
					continue
				}
				if int32(resp.State) == int32(scanadapter.State_SCANNING) {
					log.Debugf("scanner is scanning, skip scan")
					continue
				}
			}
			localT := s.filePQueue.Pop()
			if localT != nil {
				s.doneTaskCount++
				var err error
				dest := ""
				localTask := localT.(*LocalTask)
				log.Debugf("[test debug]:LocalTask : %+v", localTask)
				if localTask.usePlusDisk {
					dest, err = filepath.Rel(s.plusFileDir, localTask.filePath)
					if err != nil {
						log.Errorf("generate relative path failed: %v", err)
					}
				} else {
					dest = path.Base(localTask.filePath)
				}
				log.Debugf("[test debug]:scan file: %v", dest)

				if s.enableOutputDir {
					outputDir := fmt.Sprintf("%x", localTask.id)
					outputPath := path.Join(s.logDir, outputDir)

					err = os.MkdirAll(outputPath, os.ModePerm)
					if err != nil {
						log.Errorf("create log output dir err:%v", err)
					}
					err = os.Chmod(outputPath, 0o777)
					if err != nil {
						log.Errorf("change directory %v permission err:%v", outputPath, err)
					}

					localTask.logOutputDir = outputDir
				}
				scanLocalTasks[dest] = localTask
				log.Debugf("[test debug]:scanLocalTasks : %+v", scanLocalTasks)

			} else {
				time.Sleep(100 * time.Millisecond)
			}
		}
		// 如果是自升级的鉴定器并且当前的状态是处于UpdateReady的，则需要将目前已经领取到的任务全部的处理掉
		if util.IsSelfUpdate(s.assetController.GetAsset()) && s.scannerController.GetState() == util.UPDATE_READY {
			// 对于平均升级时间比较短的，在升级时间到了之后，更新状态为：UpdateReady，此时Fetch并不会停止，
			// 而此时scan流程不在进行扫描而是更新状态为：Updating 让他去升级，升级完成了再扫描，Fetch和Download都会正常的进行
			if s.getAvgUpdateTime() != 0 && int64(s.getAvgUpdateTime()) < s.assetController.GetAsset().GetScanFeature().GetMaxScanTimeout()/1e9 {
				s.scannerController.SetState(util.UPDATING)
				log.Infof("now state is：UPDATE_READY, average update time < timeout, set state to：%v to updating", s.scannerController.GetState())
				continue
			}

			if len(s.taskChan) != 0 && s.filePQueue.Len() != 0 {
				log.Infof("now state is：UPDATE_READY, average update time > timeout, taskChan length: %v, "+
					"filePQueue length: %v", s.scannerController.GetState(), len(s.taskChan), s.filePQueue.Len())
				s.batchScan(isTimeout, scanLocalTasks)
				continue
			} else {
				log.Debugf("local tasks already fetched scan complete, taskChan length：%v, filePQueue len: %v",
					len(s.taskChan), s.filePQueue.Len())
				s.scannerController.SetState(util.UPDATING)
			}
		}

		if s.scannerController.GetState() == util.IDLE {
			// scanLocalTasks ==> map[落盘的样本文件名及路径]localTask
			log.Debugf("scanLocalTasks: %v, isTimeout: %v, taskChan: %v, filePQueue: %v", len(scanLocalTasks), isTimeout, len(s.taskChan), s.filePQueue.Len())
			s.batchScan(isTimeout, scanLocalTasks)
		}
	}
}

func (s *scan) setCommonField(scanLocalTasks map[string]*LocalTask, scanOperations []scanadapter.ScanOperation, duration time.Duration, count int) {
	// set common field
	// 这里不能遍历results是因为可能扫描失败后,results不返回结果或返回结果不全
	for _, v := range scanOperations {
		localTask := scanLocalTasks[v.Path]
		localTask.scanDuration = duration
		localTask.scanRetryCount = count
		localTask.scanBatchCount = len(scanOperations)

		localTask.scanOk = time.Now()
	}
}

func (s *scan) dealScanFailed(scanLocalTasks map[string]*LocalTask, scanOperations []scanadapter.ScanOperation, err error) {
	// 扫描动作失败， 这一堆的所有task全部置为失败， 直接返回扫描结果
	for _, v := range scanOperations {
		localTask := scanLocalTasks[v.Path]
		localTask.task.Code = entities.ScanCode_SCAN_FAILURE
		localTask.task.Message = "kamala scan error: " + err.Error()
		s.resultChan <- localTask
		delete(scanLocalTasks, v.Path)
	}
}

// assignVersion是统一升级指定的版本
func (s *scan) handleScanResult(localTask *LocalTask, result *scanadapter.ScanResult, assignVersion string) {
	// 判断扫描结果中的version字段的Pattern和Engine是否为空，若是为空则使用Ping返回得到的Pattern和Engine
	pattern := ""
	engine := ""
	if result.Version != nil {
		pattern = result.Version.PatternVersion
		engine = result.Version.EngineVersion
	}
	var pingResponse *scanadapter.PingResponse
	var err error
	if pattern == "" {
		pingResponse, err = s.scannerController.Ping()
		if err != nil {
			log.Errorf("ping scanner failed: %v", err)
			pattern = s.scannerController.GetScanner().GetVersion().GetPattern()
		} else if pingResponse.Version != nil {
			pattern = pingResponse.Version.PatternVersion
		}
	}

	if engine == "" {
		pingResponse, err = s.scannerController.Ping()
		if err != nil {
			log.Errorf("ping scanner failed: %v", err)
			engine = s.scannerController.GetScanner().GetVersion().GetEngine()
		} else if pingResponse.Version != nil {
			engine = pingResponse.Version.EngineVersion
		}
	}

	// 先加功能之后重构
	// 只有统一升级的鉴定器才需要处理
	if s.isUniteUpdate() {
		// 如果指定版本为空，则指定版本为最新版本
		if assignVersion == "" {
			latest, err := s.helper.GetLatestDir()
			if err == nil {
				s.latestVersion = latest
			} else {
				log.Errorf("get latest version: %v", err)
			}
			assignVersion = s.latestVersion
		}
		// 鉴定器重启后，如果扫描版本在/update目录下不存在则认为版本太旧，如果在但是扫描版本不等于指定版本，则同样认为版本太旧
		scanVersionDir, err := s.helper.GetDir(pattern, engine)
		if err != nil || scanVersionDir != assignVersion {
			// 说明：用旧版本扫描的任务直接置为失败，同时把s.preVersion置为初始状态，强制reload
			if err != nil {
				log.Infof("current scanner version is not in the version dir, [pattern: %s, engine: %s]", engine, pattern)
			}
			if scanVersionDir != s.latestVersion {
				log.Infof("current scanner version dir is: %s, latest is : %s", scanVersionDir, s.latestVersion)
			}
			s.preVersion = InitVersion
			result.Code = scanadapter.ScanCode(entities.ScanCode_OTHER)
			localTask.task.MaxRetryTimes = 3
			result.Message = fmt.Sprintf("scanner version [pattern:%s, engine: %s] is old", engine, pattern)
		}
	}

	localTask.task.Code = entities.ScanCode(result.Code)
	localTask.task.Message = result.Message

	localTask.task.PatternVersion = pattern
	localTask.task.EngineVersion = engine

	// 去掉超大样本的前缀
	resultName := result.Name
	// log.Debugf("scan complete modify upload path, result name: %v", resultName)
	log.Debugf("scan complete modify upload path, before result name: %v, result: %v", resultName, result)
	if strings.HasPrefix(result.Name, s.plusFileDir) {
		rNs := strings.Split(result.Name, "/")
		resultName = rNs[len(rNs)-1]
	}
	fullLogPath := resultName
	if !path.IsAbs(resultName) {
		fullLogPath = path.Join(s.logDir, localTask.logOutputDir, resultName)
	}
	log.Debugf("scan complete modify upload path, result path: %v, result: %v", fullLogPath, result)

	if result.Code != scanadapter.ScanCode_OK {
		// 部分鉴定器会在返回扫描失败时, 生成日志文件, 这里拼好日志文件路径用于删除
		if result.Name != "" {
			localTask.logPath = fullLogPath
			log.Debugf("scan complete, log_path: %s", localTask.logPath)
			log.Debugf("scan complete, log_name: %s", resultName)
		}
		s.resultChan <- localTask
	} else if result.Name == "" { // v.Code == ScanCode_OK
		// 返回扫描成功， 但未给出扫描日志路径
		log.Infof("scan complete, no scan log generated")
		localTask.task.Code = entities.ScanCode_SCAN_FAILURE
		localTask.task.Message = "no scan log generated"
		s.resultChan <- localTask
	} else {
		localTask.logPath = fullLogPath
		log.Debugf("scan complete, log_path: %s", localTask.logPath)
		log.Debugf("scan complete, log_name: %s", resultName)
		s.logChan <- localTask
	}
}

func (s *scan) loopUpload() {
	ticker := time.Tick(1 * time.Second)
	for !s.LoopController.Stop {
		select {
		case <-ticker:
			continue
		case t := <-s.logChan:
			err := s.storagePool.Submit(func(t *LocalTask) func() {
				return func() {
					uploadTimeout := t.lastTime - 5*time.Second
					ctx, cancel := context.WithTimeout(context.Background(), uploadTimeout)
					defer cancel()
					var uploadInfo *sdk.UploadInfo
					var err error
					var uploadOutputDirErr error

					log.Debugf("store uri:%v,", t.task.GetStoreUri())
					t.task.StoreUri = util.TaskSubst(t.task, t.task.GetStoreUri(), t.logPath)

					// storeUriTemplate := "files/${FILESHA1}/logs/${ASSET}/${LOGSHA1}/storage"
					storeUriTemplate, err := util.GetChildStoreUri(t.task.Parameter)
					log.Debugf("child store uri template:%v, error:%v", storeUriTemplate, err)

					begin := time.Now()
					if s.enableOutputDir && len(storeUriTemplate) > 0 {
						uploadOutputDirErr = s.uploadOutputDir(ctx, t.logOutputDir, storeUriTemplate, t)
					}

					// upload the log file
					if strings.Contains(t.task.GetStoreUri(), "message") && proxy.ChooseStorage(t.task, t.logPath) {
						log.Debugf("store_uri:%v of after split according to ||", t.task.GetStoreUri())
						uploadInfo, err = message.Upload(ctx, t.task, t.logPath)
					} else {
						uploadInfo, err = s.storageController.Upload(ctx, t.task, t.logPath)
					}

					// prepare to enter the phase loopDeliver
					t.uploadDuration = time.Since(begin)
					t.uploadInfo = uploadInfo

					if uploadOutputDirErr != nil && fmt.Sprintf("%v", uploadOutputDirErr) != "<nil>" {
						log.Errorf("[%v] failed to upload outputDir, err: %v, uri: %v", t.uploadDuration, err, t.task.FetchUri)
						t.task.Code = entities.ScanCode_UPLOAD_FAILURE
						t.task.Message = fmt.Sprintf("faild to upload output dir, err:%v", uploadOutputDirErr)
					}

					if err != nil {
						log.Errorf("[%v] failed to upload task, err: %v, uri: %v",
							t.uploadDuration, err, t.task.FetchUri)
						t.task.Code = entities.ScanCode_UPLOAD_FAILURE
						t.task.Message = err.Error()
					}
					log.Infof("localTask:%v, uploadDuration:%v, uploadRetryCount:%v, uploadTimeout:%v", t.task.GetName(),
						t.uploadDuration, t.uploadRetryCount, uploadTimeout)
					t.waitUploadTime = time.Since(t.scanOk)
					t.uploadOk = time.Now()
					s.resultChan <- t
				}
			}(t))
			if err != nil {
				msg := fmt.Sprintf("[%v] failed to upload task, err: %v", t.uploadDuration, err)
				t.task.Code = entities.ScanCode_OTHER
				t.task.Message = msg
				log.Errorf("[%v] failed to upload task, err: %v, task: %v", t.uploadDuration, err, t)
				t.waitUploadTime = time.Since(t.scanOk)
				t.uploadOk = time.Now()
				s.resultChan <- t
			}
		}
	}
}

func (s *scan) uploadOutputDir(ctx context.Context, logOutPutDir, storeUriTemplate string, t *LocalTask) error {
	logOutPutDirPath := path.Join(s.logDir, logOutPutDir)

	err := filepath.WalkDir(logOutPutDirPath, func(name string, d fs.DirEntry, err error) error {
		if err != nil {
			log.Errorf("filepath.Walk Stat %s failed: %v\n", name, err)
			return err
		}

		if d.IsDir() {
			return nil
		}

		if strings.Contains(name, t.logPath) {
			log.Debugf("ignore scan log, name:%v, logpath:%v", name, t.logPath)
			return nil
		}

		logPath := name
		// 过滤掉size=0的 logPath
		if fi, err := os.Stat(logPath); err == nil {
			if fi.Size() == 0 {
				log.Debugf("ignore zero size file, name:%v", name)
				return nil
			}
		}

		storeUri := storeUriTemplate
		// storeUri := util.TaskSubst(t.task, storeUriTemplate, logPath)
		if strings.Contains(storeUriTemplate, "%s") {
			storeUri = fmt.Sprintf(storeUriTemplate, filepath.Base(name))
		}

		log.Debugf("upload %v to uri %v", logPath, storeUri)

		t := &entities.Task{
			StoreUri: storeUri,
		}

		_, uploadErr := s.storageController.Upload(ctx, t, logPath)

		return uploadErr
	})

	return err
}

func (s *scan) loopDeliver() {
	ticker := time.Tick(1 * time.Second)
	for !s.LoopController.Stop {
		if s.assetController.GetAsset() == nil {
			continue
		}

		select {
		case data := <-s.resultChan:
			err := s.updatePool.Submit(func(data *LocalTask) func() {
				return func() {
					s.toDeliver(data)
				}
			}(data))
			if err != nil {
				log.Errorf("failed to submit update pool task, err: %v", err)
			}
		case <-ticker:
			break
		}
	}
	log.Info("loopDeliver closed")
}

// 根据asset中UpdateFeature.VersionDopt中的Versions数组中的版本信息以及升级时间，计算该鉴定器平均升级耗时情况
func (s *scan) getAvgUpdateTime() int {
	versions := s.assetController.GetAsset().GetUpdateFeature().GetVersionDepot().GetVersions()
	log.Debugf("len: %v, versions: %+v", len(versions), versions)
	if len(versions) == 0 {
		log.Warn("asset version array is empty")
		return 0
	}
	var totalTime int
	var count int
	for _, v := range versions {
		if v.GetUpdateDuration() != "" {
			t, _ := strconv.Atoi(strings.SplitN(v.GetUpdateDuration(), ".", 2)[0])
			totalTime += t
			count++
		}
	}
	if count == 0 {
		log.Warn("all update_duration is empty in asset version array")
		return 0
	}
	return totalTime / count
}

func (s *scan) selfUpdate() {
	if s.isUniteUpdate() {
		// 如果是统一升级模式的，把preVersion中保存的版本目录设置为初始状态：INIT_VERSION
		s.preVersion = "INIT_VERSION"
	} else if util.IsSelfUpdate(s.assetController.GetAsset()) {
		// 自升级的鉴定器容器重启之后，都应该进行一次初始的升级操作
		begin := time.Now()
		version, err := s.scannerController.Update()
		for {
			if err != nil {
				time.Sleep(time.Second)
				log.Warnf("update failed when scanner restart, err: %v", err)
				continue
			}
			break
		}
		log.Infof("[duration: %v] update succeed when scanner restart, version: %+v", time.Since(begin), version)
	}
}

// 循环的去判断是否满足下载条件了，即：
// 1、需要能够抢到对应的对应的文件锁，抢到对应文件锁，则抢到了文件锁所在目录的操作权限；
// 2、抢到一个目录的操作权以后，需要先遍历一次这个目录中的所有文件，判断是否有异常退出和被调度的pod们残留的文件，如果发现有此时应该将其全部删掉；
// 3、在删掉残留文件之后判断此时目录的剩余空间是否足够，需要有其总容量的一半以上的剩余空间；
func (s *scan) loopTryLockFile(taskSize uint64) (DirInfo, *FileLock) {
	var ret DirInfo
	fLock := &FileLock{}
	// Select the appropriate directory to lock based on the specific task size, mainly for cases where taskSize is known
	dirList := chooseDirList(taskSize)
	// 记录已经清理过的目录，避免重复清理
	cleanedDirs := make(map[string]bool)

	for {
		var lockErr error
		for _, dir := range dirList {
			lockPath := path.Join(s.plusFileDir, dir.Name, LockFile)
			fLock, lockErr = NewFileLock(lockPath)
			if lockErr != nil {
				log.Errorf("try to lock, open file: %v failed: %v", lockPath, lockErr)
				continue
			}
			log.Debugf("[test debug]loopTryLockFile: try to lock directory: %v", dir.Name)
			lockErr = fLock.Lock()
			if lockErr != nil {
				fLock.Release()
				log.Warnf("[test debug]loopTryLockFile: process: %v, try to lock directory: %v, [EWOULDBLOCK: %v], lock failed: %v",
					os.Getpid(), dir.Name, lockErr == syscall.EWOULDBLOCK, lockErr)
				continue
			}
			// At this point, the lock has been successfully acquired
			log.Debugf("[test debug]loopTryLockFile: successfully acquired a directory lock: %v, error info: %v", dir.Name, fLock.lockErr)

			// 只在第一次成功获取锁时清理无效文件，避免重复操作
			if !cleanedDirs[dir.Name] {
				s.removeInvalidFile(dir.Name)
				cleanedDirs[dir.Name] = true
			}

			// After deleting invalid files, check if there is enough space in the locked directory
			plusUsed, _ := util.GetDirSize(path.Join(s.plusFileDir, dir.Name))
			plusFree := dir.Size - plusUsed
			log.Debugf("[test debug]loopTryLockFile: check locked directory: %v space usage, Size level: %.2fMB, Free: %.2fMB, Used: %.2fMB", dir.Name, float64(dir.Size)/MB, float64(plusFree)/MB, float64(plusUsed)/MB)
			if uint64(plusUsed) >= dir.Size || plusFree <= 0 || uint64(taskSize) > plusFree || plusFree < (dir.Size/2) {
				err := fLock.UnLock()
				if err != nil {
					log.Errorf("[test debug]loopTryLockFile: acquired directory lock: %v but space condition not met, and failed to unlock, error info: %v", dir.Name, err)
				}
				log.Debugf("[test debug]loopTryLockFile: acquired directory lock: %v but space condition not met, and unlocked successfully", dir.Name)
				fLock.Release()
				lockErr = fmt.Errorf("dir: %v no enough space, free space: %v", dir.Name, plusFree)
				time.Sleep(500 * time.Microsecond)
				continue
			}
			fLock.lockErr = lockErr
			ret = dir
			break
		}
		if lockErr == nil {
			log.Debugf("[test debug]loopTryLockFile: successfully acquired a directory lock: %v, error info: %v", ret.Name, fLock.lockErr)
			break
		}
		log.Warnf("[test debug]loopTryLockFile: failed to acquire a directory lock, keep trying")
		time.Sleep(time.Microsecond * 1000)
	}
	return ret, fLock
}

// 输入为：被锁住目录的目录名
func (s *scan) removeInvalidFile(dirName string) {
	files, err := ioutil.ReadDir(path.Join(s.plusFileDir, dirName))
	if err != nil {
		log.Errorf("[test debug]removeInvalidFile:读取目录 %v 失败: %v", dirName, err)
		return
	}

	log.Debugf("[test debug]removeInvalidFile:检测目录：%v 下面是否存在残留文件, 目录中此时的文件个数为: %v ",
		dirName, len(files))

	// 抢到一个目录之后先遍历这个文件中的所有文件，并且尝试加锁，如果能够加上说明这个文件属于残留文件，其原本所在的进程异常退出了，需要删掉
	for _, file := range files {
		if file.Name() == LockFile {
			continue
		}
		pathName := path.Join(s.plusFileDir, dirName, file.Name())
		f, err := os.Open(pathName)
		if err != nil {
			log.Debugf("[test debug]removeInvalidFile:无法打开文件 %v: %v", pathName, err)
			continue
		}

		// 使用 defer 确保文件句柄总是被关闭
		func() {
			defer func() {
				if closeErr := f.Close(); closeErr != nil {
					log.Debugf("[test debug]removeInvalidFile:关闭文件 %v 失败: %v", pathName, closeErr)
				}
			}()

			lockErr := syscall.Flock(int(f.Fd()), syscall.LOCK_EX|syscall.LOCK_NB)
			if lockErr == nil {
				log.Debugf("[test debug]removeInvalidFile:遍历文件并且尝试删除异常退出pod残留的文件：%v", pathName)
				err = util.RemoveFile(pathName)
				if err != nil {
					log.Errorf("[test debug]removeInvalidFile:删除异常退出pod残留的文件：%v 失败了：%v", pathName, err)
				} else {
					log.Infof("[test debug]removeInvalidFile:删除异常退出pod残留的文件：%v 成功", pathName)
				}
				// 尝试删除对应的日志文件
				if logErr := util.RemoveFile(fmt.Sprintf("%v.log", pathName)); logErr != nil {
					log.Debugf("[test debug]removeInvalidFile:删除日志文件 %v.log 失败: %v", pathName, logErr)
				}
			}
		}()
	}
}

// 循环的去获取高阶目录的使用权限，即：
// 此时所在的目录的逻辑容量已经存不下样本的实际大小内容了，那么应该申请向拥有更大空间容量的高阶目录申请，将样本转存至高阶目录
// 例如：此时落盘的是5G的目录，但样本存进去之后才发现其实他是一个8G的样本，那么应该向拥有更大容量空间的目录申请将样本转存过去
// 于是尝试对10G和20G的目录申请锁，得到锁并且其剩余空间足以存下这个样本，那么就将这个样本通过mv命令移动过去
func (s *scan) getHighLevelFileLock(size uint64) (DirInfo, *FileLock) {
	fLock := &FileLock{}
	var dirList []DirInfo
	var ret DirInfo
	var err error
	if size < middleFile {
		dirList = getMiddleDirList()
		dirList = append(dirList, getBigDirList()...)
	}

	if size > middleFile {
		dirList = getBigDirList()
	}
	for {
		var lockErr error
		for _, dir := range dirList {
			// 判断目录的剩余空间是不是足够的能装的下此时的这个样本，转不下就没必要来一次系统调用去抢锁了
			plusUsed, _ := util.GetDirSize(path.Join(s.plusFileDir, dir.Name))
			if s.plusDirSize-uint64(plusUsed) < size {
				continue
			}
			fLock, err = NewFileLock(path.Join(s.plusFileDir, dir.Name, LockFile))
			if err != nil {
				log.Debugf("[test debug]getHighLevelFileLock:New文件锁失败：%v", err)
				continue
			}
			lockErr = fLock.Lock()
			if lockErr != nil {
				fLock.Release()
			}
			if lockErr == nil {
				ret = dir
				break
			}
		}
		if lockErr == nil {
			break
		}
		time.Sleep(1000 * time.Microsecond)
	}
	return ret, fLock
}

func (s *scan) toDownload(t *LocalTask) (time.Duration, error) {
	var filePath string       // 最终样本文件落盘的具体路径
	var dir DirInfo           // 目录信息，用来记录最终抢到的目录的信息，名称和对应大小
	var saveDirLock *FileLock // 锁住落盘目录的那把锁
	fileName := fmt.Sprintf("%v_%x_%s", time.Now().UnixNano(), t.id, t.task.GetName())
	if s.enableUsePlusDisk && t.usePlusDisk {
		// TODO 一旦确定使用第二块盘进行下载，则尝试去锁住一个目录然后进行下载过程，下载流程完了就可以把锁释放了
		log.Debugf("[test debug]toDownload:任务: %v 开始抢目录锁: %v 满足所有条件, 目录大小限制: %.3fG",
			t.task.GetName(), dir.Name, float64(dir.Size)/GB)
		dir, saveDirLock = s.loopTryLockFile(t.task.GetSize())
		log.Debugf("[test debug]toDownload:任务: %v 抢到目录锁: %v 满足所有条件, 目录大小限制: %.3fG, 错误信息：%v",
			t.task.GetName(), dir.Name, float64(dir.Size)/GB, saveDirLock.lockErr)
		defer saveDirLock.UnLock()
		defer saveDirLock.Release()
		// TODO 此时已经可以确定到底需要落盘到第二盘的哪个具体路径了，对这个文件上一个文件锁，其他的进程在下载前遍历一次他要能锁住的那个目录
		// TODO 中的所有文件并且尝试去上锁如果能上锁就说明：那个进程所在的pod已经被调度了或者异常退出了，这里就可以将其删除了
		filePath = path.Join(s.plusFileDir, dir.Name, fileName)
	} else {
		filePath = path.Join(s.fileDir, fileName)
	}

	if t.task.GetScanTimeout() == 0 {
		if t.task.ScanTimeout = s.assetController.GetAsset().GetScanFeature().GetMaxScanTimeout(); t.task.ScanTimeout == 0 {
			// 如果asset中的max_scan_timeout值为零那么就使用默认值：600s
			t.task.ScanTimeout = int64(math.Pow10(9) * 600)
		}
	}

	taskSize := t.task.GetSize()
	var size int64
	var storageErr error
	if t.task.GetFetchUri() != "" {
		size, storageErr = util.GetStorageSize(t.task.GetFetchUri())
	} else {
		size, storageErr = util.GetStorageSize(t.task.GetSha1())
	}
	if storageErr != nil {
		log.Errorf("get storage size failed: %v", storageErr)
	}
	taskSize = uint64(size)
	log.Debugf("got task size：%v", taskSize)
	t.totalTime = util.GetDownloadLadderTime(taskSize)*2*time.Second + time.Duration(t.task.GetScanTimeout())

	downloadTimeout := time.Duration(float64(t.totalTime) * 0.8)
	ctx, cancel := context.WithTimeout(context.Background(), downloadTimeout)
	defer cancel()
	t.task.FetchUri = util.TaskSubst(t.task, t.task.FetchUri, "")
	// TODO 在调用下载时，先判断一下是否需要采用message方式
	var downloadInfo *sdk.DownloadInfo = &sdk.DownloadInfo{}
	var err error
	input := util.DownloadInput{
		Uri:      t.task.GetFetchUri(),
		Sha1:     t.task.GetSha1(),
		Size:     t.task.GetSize(),
		FilePath: filePath,
	}
	if strings.Contains(t.task.GetFetchUri(), "message") {
		begin := time.Now()
		downloadInfo, err = message.Download(ctx, t.task, filePath)
		t.downloadDuration = time.Since(begin)
		log.Debugf("message download info:%+v, err:%v", downloadInfo, err)
	} else {
		begin := time.Now()
		downloadInfo, err = s.storageController.Download(ctx, input)
		t.downloadDuration = time.Since(begin)
		log.Debugf("morpheus download info:%+v, err:%v", downloadInfo, err)
	}

	t.downloadInfo = downloadInfo
	t.filePath = filePath
	t.downloadOk = time.Now()
	t.waitDownloadTime = time.Since(t.fetchTime)
	if t.downloadDuration < downloadTimeout {
		t.lastTime = t.totalTime - t.downloadDuration
	} else {
		t.lastTime = t.totalTime - downloadTimeout
	}
	log.Debugf("totalTime:%v, downloadTimeout:%v, lastTime:%v in loopDownload", t.totalTime, downloadTimeout, t.lastTime)
	log.Debugf("localTask:%v, downloadDuration:%v, downloadRetryCount:%v", t.task.GetName(),
		t.downloadDuration, t.downloadRetryCount)
	// log.Debugf("[test debug]toDownload:对任务：%v 完成下载", t.task.GetName())
	if err != nil {

		if t.downloadInfo == nil {
			log.Errorf("download info is nil, err:%v", err)
			t.downloadInfo = &sdk.DownloadInfo{}
		}

		log.Errorf("download err:%v", err)
		// 反正是下载失败了，那么判断以下这个文件是否存在，存在则需要删掉，因为这个落盘文件可能也是一个不成完整的文件
		// 为了保险起见在最上层判断其是否存在，存在则删除
		_ = util.RemoveFile(filePath)
		// 下载失败时，对于size已知的情况下若是采用的第一块盘下载，则预计使用大小应该减去此时的size
		if t.task.GetSize() != 0 && t.task.GetSize() != 1 && !t.usePlusDisk {
			atomic.AddUint64(&s.estimateSize, -t.task.GetSize())
		}
		// code为：InvalidArgument 说明是sha1不一致, 否则是下载失败错误
		if status.Code(err) == codes.InvalidArgument {
			t.task.Code = entities.ScanCode_SHA1_FAILURE
		} else {
			t.task.Code = entities.ScanCode_DOWNLOAD_FAILURE
		}
		// 对于下载失败的情况下载内容带小为0，该值显示置为0因为在DeliverTask时， 计算estimateSize时需要减去本次样本实际大小值，
		// 而该值在下载失败情况下应该是0，estimateSize不准确的话会直接影响到 FetchTask
		t.downloadInfo.Length = 0
		t.task.Message = err.Error()
		s.resultChan <- t
	} else {
		if s.enableUsePlusDisk && t.usePlusDisk {
			// 在使用第二块盘的时候，如果下载回来的样本大小直接超过了抢到的落盘目录的空间大小，
			// 或者下载好了这个样本之后导致此时目录的空间不足，需要去申请高阶目录将其转移过去
			dirSize, _ := util.GetDirSize(path.Join(s.plusFileDir, dir.Name))
			if t.downloadInfo.Length > dir.Size || dirSize > dir.Size {
				if t.downloadInfo.Length > dir.Size {
					log.Errorf("[test debug]toDownload:Task: %v, 下载回来东西比目录空间大了, Length: %v", t.task.GetName(), downloadInfo.Length)
				} else if dirSize > dir.Size {
					log.Errorf("[test debug]toDownload:Task: %v, 下载回来东西导致目录空间不足了, Length: %v", t.task.GetName(), downloadInfo.Length)
				}
				destDir, fLock := s.getHighLevelFileLock(t.downloadInfo.Length)
				log.Debugf("[test debug]toDownload:成功获取得到一个高阶文件锁：%v, size: %v, lockErr: %v", destDir.Name, dir.Size, fLock.lockErr)
				dest := path.Join(s.plusFileDir, destDir.Name, fileName)
				util.MoveFile(filePath, dest)
				err = fLock.UnLock()
				log.Debugf("[test debug]toDownload:对高阶目录: %v 进行解锁是否成功：%v\t具体错误信息：%v", destDir.Name, err == nil, err)
				fLock.Release()
				log.Debugf("[test debug]toDownload:成功将文件: %v 转移至: %v", filePath, path.Join(s.plusFileDir, destDir.Name, fileName))
				filePath = path.Join(s.plusFileDir, destDir.Name, fileName)
			}

			// log.Debugf("[test debug]toDownload:应该对样本文件: %v 加锁直至Deliver", filepath)
			// TODO 下载并成功落盘后应该立即将这个文件锁住，锁的生命周期是从它进入taskChan一直到这个要被Deliver的时候
			sampleFileLock, err := NewFileLock(filePath)
			if err != nil {
				log.Errorf("try to lock sample file failed: %v", err)
			} else {
				// 只要这个样本文件落盘成功并且真实存在那么应该就是一定能锁住的，因为不会再有哪个进程会去锁住它，它是唯一的
				err = sampleFileLock.Lock()
				if err != nil {
					sampleFileLock.Release()
					log.Errorf("try to lock sample file lock error: %v", err)
				}
				log.Debugf("[test debug]toDownload:成功对样本文件: %v 加锁", filePath)
				t.fileLock = sampleFileLock
			}
		}
		if t.task.GetSize() != 0 && t.task.GetSize() != 1 && !t.usePlusDisk && downloadInfo.Length != t.task.GetSize() {
			log.Debugf("[test debug]toDownload:未解压之前样本的大小: %.2fMB 解压之后样本的大小: %.2fMB", float64(t.task.GetSize())/MB, float64(downloadInfo.Length)/MB)
			// 对于样本size已知的情况，如果下载完成之后发现落盘的实际size和task.size不等说明是一个压缩包，
			// 但是前面计算预估占用emptyDir空间大小的时候只是加了它没有解压之前的大小，此时需要将解压后多出来的那段大小加上
			diff := downloadInfo.Length - t.task.GetSize()
			atomic.AddUint64(&s.estimateSize, diff)
		}

		// // 查看文件权限， 改变权限
		// fi, _ := os.Stat(filePath)
		// log.Infof("chmod pre, file: %s, perm: %s", filePath, fi.Mode().String())

		// err = os.Chmod(filePath, 0755)
		// if err != nil {
		// 	log.Errorf("chmod file: %s", filePath)
		// } else {
		// 	log.Info("chmod file: %s to 0755", filePath)
		// }

		// fi, _ = os.Stat(filePath)
		// log.Infof("chmod after, file: %s, perm: %s", filePath, fi.Mode().String())
		if err = s.filePQueue.Push(t); err != nil {
			log.Errorf("failed to push filePQueue, err: %v", err)
		}
	}
	return downloadTimeout, err
}

func (s *scan) toWithField(data *LocalTask) {
	log.WithField("mnt_type", "collector_scan").
		WithField("monitor",
			"life_duration,fetch_duration,scan_duration,update_duration,"+
				"deliverTask_rate,pattern_version,engine_version,"+
				"download_grpc_duration,download_http_duration,"+
				"upload_grpc_duration,upload_http_duration,"+
				"scanner_name,content_size").
		WithField("mnt_v_life", data.lifeDuration).
		WithField("mnt_v_fetch", data.getOneFetchDuration()).
		WithField("mnt_v_download_grpc", data.getDownloadInfo().GetGrpcDuration()).
		WithField("mnt_v_download_http", data.getDownloadInfo().GetHttpDuration()).
		WithField("mnt_v_scan", data.getOneScanDuration()).
		WithField("mnt_v_scan_count", data.scanBatchCount).
		WithField("mnt_v_upload_grpc", data.getUplaodInfo().GetGrpcDuration()).
		WithField("mnt_v_upload_http", data.getUplaodInfo().GetHttpDuration()).
		WithField("mnt_v_update", data.updateDuration).
		WithField("mnt_v_content_size", data.downloadInfo.GetContentSize()).
		WithField("mnt_scanner_name", s.scannerController.GetScanner().GetName()).
		WithField("mnt_download_source", data.downloadInfo.Source).
		WithField("mnt_state", data.task.GetState()).
		WithField("mnt_priority", data.task.GetPriority()).
		WithField("mnt_code", data.task.GetCode()).
		WithField("mnt_asset", data.task.GetAssetName()).
		WithField("mnt_pattern_version", data.task.GetPatternVersion()).
		WithField("mnt_engine_version", data.task.GetEngineVersion()).
		WithField("mnt_wait_download", data.waitDownloadTime).
		WithField("mnt_wait_scan", data.waitScanTime).
		WithField("mnt_wait_upload", data.waitUploadTime).
		WithField("mnt_wait_update", data.waitUpdateTime).
		Infof("deliverTask %+v", data.task)
}

func (s *scan) toDeliver(data *LocalTask) {
	for i := 0; ; i++ {
		begin := time.Now()
		monitor := make(map[string]string)
		s.addTaskInfo(data, monitor)
		monitor["life_duration"] = time.Since(data.fetchTime).String()
		if data.uploadOk.Unix() == 0 {
			data.waitUpdateTime = 0
		} else {
			data.waitUpdateTime = time.Since(data.uploadOk)
		}
		err := s.taskController.DeliverTask(data.task, monitor)
		if err == nil {
			data.updateDuration = time.Since(begin)
			data.lifeDuration = time.Since(data.fetchTime)
			// 内部设置了有重试次数3次，外部为一个死循，循环的终止条件为：内部调用worker的update服务成功，
			// 所以如果死循环中i的值都到了1或2或之后的数，那么说明内部retry肯定全部失败了且达到了3次最大值
			// 所以重试的总次数计算方式为：假设外部循环第5次成功，内部在第2次成功，说明外部循环中前4次的内部重试都失败了；
			// 总重试次数 = （5 - 1） * 3 + 第5次的内部重试次数 = 4 * 3 + 2
			retryCount, _ := strconv.Atoi(monitor["update_retry_count"])
			data.updateRetryCount = 3*i + retryCount
			log.Debugf("localTask:%v, updateDuration:%v, updateRetryCount:%v",
				data.task.GetName(), data.updateDuration, data.updateRetryCount)
			s.toWithField(data)

			if data.fileLock != nil {
				_ = data.fileLock.UnLock()
				data.fileLock.Release()
				log.Debugf("[test debug]toDeliver:成功对样本文件: %v 解锁", data.filePath)
			}

			logErr := util.RemoveFile(data.logPath)
			if logErr != nil {
				log.Warnf("failed to remove log: %v, err: %v", data.logPath, logErr)
			}
			if s.enableOutputDir {
				outputDir := path.Join(s.logDir, data.logOutputDir)
				outputDirErr := util.RemoveDir(outputDir)
				if outputDirErr != nil {
					log.Warnf("failed to remove outputDir:%v, err%v", outputDir, outputDirErr)
				}
			}
			fileErr := util.RemoveFile(data.filePath)
			if fileErr != nil {
				log.Warnf("failed to remove file: %v, err: %v", data.filePath, fileErr)
			}

			if data.task.GetSize() != 0 && data.task.GetSize() != 1 && !data.usePlusDisk {
				log.Debugf("[test debug]toDeliver:样本大小已知: %.2fM, taskName: %v, 空间释放前的estimateSize: %.2fM",
					float64(data.task.GetSize())/MB, data.task.GetName(), float64(s.estimateSize)/MB)
				atomic.AddUint64(&s.estimateSize, -data.downloadInfo.Length)
				log.Debugf("[test debug]toDeliver:样本大小已知: %.2fM, taskName: %v, emptyDir释放空间：%.2fM, "+
					"空间释放后的estimateSize: %.2fM", float64(data.task.GetSize())/MB, data.task.GetName(),
					float64(data.downloadInfo.Length)/MB, float64(s.estimateSize)/MB)
			}
			atomic.AddInt32(&s.taskCount, -1)
			break
		} else {
			log.Warnf("failed to update task, retry: %v, err: %v, task: %+v", i, err, data)
		}
	}
}

func (s *scan) batchScan(isTimeout bool, scanLocalTasks map[string]*LocalTask) {
	// len 配置
	if len(scanLocalTasks) >= int(s.assetController.GetAsset().GetScanFeature().GetMaxScanCount()) ||
		((isTimeout || s.LoopController.Stop) && len(scanLocalTasks) > 0) {
		log.Debugf("scanLocalTasks length: %+v, MaxScanCount: %v", scanLocalTasks,
			s.assetController.GetAsset().GetScanFeature().GetMaxScanCount())

		// 判断是否为统一升级，如果不是那么就使用默认scanMap进行扫描
		if s.isUniteUpdate() {
			// versionScanMap（为指定版本情况）：k：指定版本目录，v:任务路径到参数的映射, 也就是要传给Scan接口的入口参数：scanMap
			versionScanMap, err := s.getUnitUpdateScanMap(scanLocalTasks, s.helper)
			if err != nil || len(versionScanMap) == 0 {
				log.Errorf("versionDirScanMap len: %v, get scan map error : %v", len(versionScanMap), err)
			}
			// log.Debugf("versionDirScanMap: %+v", versionScanMap)
			for version, scanMap := range versionScanMap {
				if len(scanMap) == 0 {
					log.Warnf("the scanMap is empty and scanLocalTasks len: %v", len(scanLocalTasks))
					continue
				}

				versions := strings.Split(version, "+")

				var patternVersion string = ""
				var engineVersion string = ""
				if len(versions) == 2 {
					engineVersion = versions[0]
					patternVersion = versions[1]
				}
				// log.Infof("get sample version engineVersion:%v patternVersion:%v", engineVersion, patternVersion)

				versionDir, engine, pattern, err := s.vloaderController.DownloadVirusLibrary(engineVersion, patternVersion)
				if err != nil {
					log.Errorf("get virus library err :%v", err.Error())
					s.deleteFailTask(scanMap, scanLocalTasks, err)
					continue
				}
				// log.Infof("get virus library success versionDir: %v", versionDir)

				// 如果是统一升级的情况下，会做Reload操作，不是的话toReload将什么也不做
				s.toReload(versionDir, engine, pattern)
				s.toScan(scanMap, scanLocalTasks, versionDir)
			}
		} else {
			scanOperation := defaultScanOperations(scanLocalTasks)
			s.toScan(scanOperation, scanLocalTasks, "")
		}
	}
}

func (s *scan) toScan(scanOperations []scanadapter.ScanOperation, scanLocalTasks map[string]*LocalTask, assignVersion string) {
	scanTimeout, maxLastTime := s.getScanTimeout(scanOperations, scanLocalTasks)

	log.Debugf("scanTimeout:%v, maxLastTime:%v in loopScan", scanTimeout, maxLastTime)
	ctx, cancel := context.WithTimeout(context.Background(), scanTimeout)
	defer cancel()
	var count int
	begin := time.Now()

	results, err := s.scannerController.Scan(ctx, scanOperations)
	duration := time.Since(begin)
	log.Debugf("scan complete scanMap: %+v, result: %+v, err: %v", scanOperations, results, err)
	if results != nil {
		count = results.RetryCount
		log.Debugf("task count: %v, scan duration: %v, scanRetryCount: %v\t, scan result : %+v",
			len(scanOperations), duration, count, results.Result)
	} else {
		log.Error("the scan result struct is empty")
	}
	s.setCommonField(scanLocalTasks, scanOperations, duration, count)
	// 设置扫描流程后的剩余时间：
	// 例如：此时最大的剩余时间maxLastTime=100s，而扫描的超时时间scanTimeout为：50s
	// 但：实际的扫描时间只用了20s，那么理论上来说100-20s以后的所有时间都是为后续流程所用
	// 所以：将每个task扫描后的可用剩余时间设置为：maxLastTime=100 - duration=20 ==> 80s
	// 如果：扫描的耗时达到了它的超时时间，那么此时扫描后的可用剩余时间设置为：scanTimeout=扫描前剩余的最大剩余时间/2
	if duration < scanTimeout {
		maxLastTime = maxLastTime - duration
	} else {
		maxLastTime = scanTimeout
	}

	for _, scamOperation := range scanOperations {
		scanLocalTasks[scamOperation.Path].lastTime = maxLastTime
	}

	if err != nil {
		log.Errorf("scan failed: %v", err)
		s.dealScanFailed(scanLocalTasks, scanOperations, err)
		// continue
		return
	}
	// 当设置了FailTolerance参数，并且重试达到上限制且所有task的Code都是Scan_FAILURE 则触发鉴定器的重启
	if s.assetController.GetAsset().GetScanFeature().GetFailTolerance() != 0 &&
		count >= int(s.assetController.GetAsset().GetScanFeature().GetFailTolerance()) &&
		scanner.CheckScanCode(results.Result) {
		// 这里一旦开始调用重启接口，就会将状态置为Terminating，此时不会进行fetch操作的，只有成功重启之后才会开始fetch操作
		// 注意：如果重启完成那么状态就会转为IDLE，Fetch操作将开始正常运行，同时scan线程也将继续执行进行扫描工作，
		// 若是此时鉴定器重启同时taskChan中其实已经累积有任务了，鉴定器现在开始进行扫描工作的话会有下面的问题
		// 对于自升级模式下，此时的鉴定器就是出生的婴儿，她需要准备和升级，准备就是直到我们能Ping通它，
		// 所以鉴定器重启之后，Ping重启鉴定器以及显示Update的操作，就放在调用重启接口这里，而不是Fetch操作里面了
		log.Errorf("all task: %v scan code: SCAN_FAILURE to restart scanner", results.Result)
		s.blockRestart()
	}
	log.Debugf("scan complete  handlerResult: %+v, result: %+v, err: %v", scanOperations, results, err)
	for k, result := range results.Result {
		// 结果中带有超大样本目录的前缀会找不到任务，去掉
		fmt.Printf("trim k, before: %s\n", k)
		if strings.HasPrefix(k, s.plusFileDir) {
			k = strings.TrimPrefix(k, s.plusFileDir+"/")
		}
		fmt.Printf("trim k, after: %s, task: %v", k, scanLocalTasks[k])
		unified := &scanadapter.ScanResult{
			Code:    scanadapter.ScanCode(result.GetCode()),
			Name:    result.GetName(),
			Message: result.GetMessage(),
		}
		if v := result.GetVersion(); v != nil {
			unified.Version = &scanadapter.Version{EngineVersion: v.GetEngineVersion(), PatternVersion: v.GetPatternVersion()}
		}
		s.handleScanResult(scanLocalTasks[k], unified, assignVersion)
		delete(scanLocalTasks, k)
	}
	log.Debugf("scan complete  handlerResultOk: %+v, result: %+v, err: %v", scanOperations, results, err)
}

// 重启后的初始化操作，对于自升级需要显示的进行一次Update操作
func (s *scan) restartInit() {
	var curStartupTime int64
	begin := time.Now()
	for {
		// 为了防止异步退出的那种鉴定器，这种鉴定器在调用Exit接口后依然是能够Ping通的，但是它的返回信息还是老鉴定器的，采用如下做法；
		// 在scan service中记录下上一次鉴定器的启动时间，通过Ping一下鉴定器根据返回值中startup(表示从鉴定器启动到Ping鉴定器的那一刻
		// 的时间间隔)就能算出它的启动时间，记录下来，当鉴定器重启之后，此时会去Ping鉴定器，将Startup取出来，用同样的方式算出鉴定器
		// 启动的时间，如果本次算出的启动时间比记录的启动时间大，就是说距离当前时间点近，那么说明：重启后的鉴定器通了；
		response, err := s.scannerController.Ping()
		if err != nil {
			log.Warnf("[curState: %v] restart ping scanner error: %v", s.scannerController.GetState(), err)
			time.Sleep(time.Millisecond * 1000)
			continue
		} else if curStartupTime = time.Now().Add(-time.Duration(response.StartupSeconds) * time.Second).Unix(); !(curStartupTime > s.preStartupTime) {
			log.Infof("ping restarted scanner, scanner: %v, preStartup time: %v, curStartup time: %v",
				response.Name, s.preStartupTime, curStartupTime)
			time.Sleep(time.Millisecond * 1000)
			continue
		} else {
			s.preStartupTime = curStartupTime
			// 只会对自升级类型鉴定器进行显示的升级操作，统一升级的什么也不做
			s.selfUpdate()
			s.scannerController.SetState(util.IDLE)
			log.Debugf("restart init set state：IDLE，restart init complete, restartInit duration: %v, "+
				"curState: %v", time.Since(begin), s.scannerController.GetState())
			log.Infof("scanner: %v restart success....", response.Name)
			break
		}
	}
}

func (s *scan) toReload(versionDir, engine, pattern string) {
	if s.isUniteUpdate() {
		pingStartTime := time.Now().Add(-time.Duration(s.scannerController.GetScanner().GetStartupSeconds()) * time.Second).Unix()
		log.Debugf("[test debug]:鉴定器：%v  是否主动重启：%v，本地记录的启动时间是: %v, 实际Ping到的启动时间是: %v",
			s.scannerController.GetScanner().GetName(), s.scannerStartTime < pingStartTime, s.scannerStartTime, pingStartTime)
		log.Debugf("[test debug]:鉴定器：%v  是否主动重启：%v collector记录的启动时间是: %v Ping scanner到的启动时间是: %v",
			s.scannerController.GetScanner().GetName(), s.scannerStartTime < pingStartTime,
			time.Unix(s.scannerStartTime, 0),
			time.Now().Add(-time.Duration(s.scannerController.GetScanner().GetStartupSeconds())*time.Second))
		if s.scannerStartTime < pingStartTime {
			s.preVersion = InitVersion
			s.scannerStartTime = pingStartTime

			s.reload(versionDir, engine, pattern)

			s.preVersion = versionDir
			// err := retry.Retry(func(attempt uint) error {
			// 	reloadErr := s.reload(versionDir)
			// 	if reloadErr != nil {
			// 		log.Errorf("scanner trigger restart lead to reload failed, retry count: %v", attempt)
			// 	}
			// 	return reloadErr
			// }, strategy.Limit(60), strategy.Wait(2*time.Second))
			// if err == nil {
			// 	s.preVersion = versionDir
			// }
		}
		if versionDir != s.preVersion {
			s.reload(versionDir, engine, pattern)
			log.Debugf("version dir: %s, pre version: %s, engine:%v, pattern:%v", versionDir, s.preVersion, engine, pattern)
		}
		// else if lv, _ := s.helper.GetLatestDir(); versionDir == "" && s.latestVersion != lv {
		// 	// 当前版本和上一个版本一样都是没有指定特定版本扫描，那么就使用最新版本扫描，但是有可能当前的最新版本已经不是最新的了
		// 	// 所以去读取index.json获取一下最新版本目录与collector保存的最新版本目录比较一下，不同则需要reload最新版本
		// 	s.reload(versionDir, engine, pattern)
		// 	log.Debugf("version dir is empty, pre version: %s", lv)
		// }
		s.preVersion = versionDir
	}
}

func (s *scan) restart() error {
	s.scannerController.SetState(util.TERMINATING)
	err := s.scannerController.Exit()
	if err != nil {
		return err
	}
	// 调用重启接口完成之后，不是立刻的进入IDLE状态，而是进入Startup状态，
	// 是因为鉴定器虽然重启成功但是可能尚未准备完全，待鉴定器可以被Ping通并且自升级类鉴定器显示升级完成后，
	// 表示鉴定器准备好了，可以由状态Startup转为IDLE了，
	s.scannerController.SetState(util.Startup)
	return nil
}

func (s *scan) getScanTimeout(scanOperations []scanadapter.ScanOperation, scanLocalTasks map[string]*LocalTask) (time.Duration, time.Duration) {
	var maxLastTime time.Duration = 0
	for _, scanOperation := range scanOperations {
		if scanLocalTasks[scanOperation.Path] == nil {
			log.Errorf("scanLocalTasks[%v] is null point", scanOperation)
		} else if scanLocalTasks[scanOperation.Path].lastTime > maxLastTime {
			maxLastTime = scanLocalTasks[scanOperation.Path].lastTime
		}
	}
	// TODO: 为什么除以2,  目前判定为不正确，待完善
	// 就是max_scan_timeout命名有1500s 但是scan 800s就超时了。 这里就是因为除以了2
	scanTimeout := time.Duration(float64(maxLastTime) * 0.5)
	// 保证scanTimeout不会超过asset中的max_scan_timeout
	if scanTimeout == 0 || scanTimeout > time.Duration(s.assetController.GetAsset().GetScanFeature().GetMaxScanTimeout()) {
		scanTimeout = time.Duration(s.assetController.GetAsset().GetScanFeature().GetMaxScanTimeout()) * time.Nanosecond
		log.Debugf("scanTimeout more than the max_scan_timeout in asset, reset scanTimeout:%v", scanTimeout)
	}
	return scanTimeout, maxLastTime
}

func (s *scan) isUniteUpdate() bool {
	if s.assetController.GetAsset().GetUpdateFeature().GetKind() == entities.UpgradeKind_DARWIN ||
		s.assetController.GetAsset().GetUpdateFeature().GetKind() == entities.UpgradeKind_UPDATER {
		return true
	}
	return false
}

func (s *scan) reload(versionDir, engine, pattern string) error {
	for {
		version, err := s.scannerController.Reload(versionDir)
		if err == nil && version != nil && engine == version.EngineVersion && pattern == version.PatternVersion {
			return nil
		}
		log.Errorf("reload virus bibary failed, dir:%v, engine:%v, patten:%v, reload version:%v, err:%v", versionDir, engine, pattern, version, err)
		time.Sleep(time.Second * 1)
	}

	// 对于指定版本的情况，逻辑走到这里说明：指定版本的版本目录肯定是存在的，否则在getScanMap的时候对应的task就被处理掉了，不会拿来扫描
	// if versionDir != "" {

	// }
	// else {
	// 	// 如果updater还没有来得及起来，但是扫描需求已经到了，并且需要最新版本扫描，那么在加载最新版本目录的时候肯定就失败了
	// 	// 此时若再继续提交扫描肯定就会报："[Errno 104] Connection reset by peer" 扫描失败
	// 	// 所以在这里一直等，直到updater加载完成，最新版本目录完全创建且能够顺利的获取得到，那么就可以进行加载并提交扫描了
	// 	var latestVersionDir string
	// 	var err error
	// 	for {
	// 		latestVersionDir, err = s.helper.GetLatestDir()
	// 		s.latestVersion = latestVersionDir
	// 		if err != nil {
	// 			log.Errorf("Get latest director:latest Version Director error : %v", latestVersionDir, err)
	// 			time.Sleep(time.Second)
	// 			continue
	// 		}
	// 		break
	// 	}

	// 	version, err := s.scannerController.Reload(latestVersionDir)
	// 	if err != nil {
	// 		log.Errorf("Reload specify version : %v error : %v", version, err)
	// 		return status.Errorf(codes.Internal, "Reload specify version : %v error : %v", version, err)
	// 	}
	// }
	// return nil
}

func (s *scan) blockRestart() {
	count := 0
	begin := time.Now()
	for {
		err := s.restart()
		if err != nil {
			count++
			log.Errorf("restart scanner failed: %v, retry count: %v", err, count)
			time.Sleep(100 * time.Millisecond)
			continue
		}
		log.Debugf("block restart complete, restart duration: %v, curState: %v", time.Since(begin), s.scannerController.GetState())
		break
	}
	// 阻塞重启之后，我们应该立刻做重启后的初始化操作
	// 即：对鉴定器进行Ping操作，对于自升级的鉴定器来说应该显示的进行升级操作
	s.restartInit()
}

// 这个是专门获取统一升级类鉴定器的scanMap（scan接口的输入）
// 按照优先级分堆，HIGH_PRIORITY和OTHER,若有最高优先级就直获取最高优先级的ScanMap
func (s *scan) getUnitUpdateScanMap(tasks map[string]*LocalTask, h helper.UpdateHelper) (map[string][]scanadapter.ScanOperation, error) {
	// k：版本目录名，v：传给scan的参数即：[]*kamala.ScanOperation(4.0接口鉴定接口输入参数的集合)
	highPriorityQue := make(map[string]*LocalTask, 0)
	otherPriorityQue := make(map[string]*LocalTask, 0)
	for k, v := range tasks {
		// k:落盘的样本路径
		if v.task.GetPriority() == entities.Priority_VERY_HIGH {
			highPriorityQue[k] = v
		} else {
			otherPriorityQue[k] = v
		}
	}
	if len(highPriorityQue) != 0 {
		assignVersion, err := s.getPriorityScanMap(highPriorityQue, tasks, false, h)
		return assignVersion, err
	}
	assignVersionMap, err := s.getPriorityScanMap(otherPriorityQue, tasks, true, h)
	return assignVersionMap, err
}

// 处理已经按照优先级分好堆的tasks，传入的参数：要么是最高优先级的，要么就全是非最高优先级的
func (s *scan) getPriorityScanMap(tasks, srcTasks map[string]*LocalTask, flag bool, h helper.UpdateHelper) (map[string][]scanadapter.ScanOperation, error) {
	latestVersion := ""
	// k：版本目录名，v：传给scan的参数（适配器统一类型）
	highAssignVersion := make(map[string][]scanadapter.ScanOperation)
	for k, v := range tasks {

		// 没有指定版本
		if v.task.PatternVersion == "" && v.task.EngineVersion == "" {
			latestVersion = ""
			v.waitScanTime = time.Since(v.downloadOk)
			highAssignVersion[""] = append(highAssignVersion[""], createScanOperation(k, v))
			continue
		}
		// 若根据task中patterVersion和EngineVersion无法找到对应版本目录，则：将错误信息写入resultChan，然后删除task

		version := fmt.Sprintf("%v+%v", v.task.GetEngineVersion(), v.task.GetPatternVersion())
		log.Debugf("get version from task version:%v", version)

		latestVersion = version
		v.waitScanTime = time.Since(v.downloadOk)
		highAssignVersion[version] = append(highAssignVersion[version], createScanOperation(k, v))
	}
	if true {
		assignVersion := make(map[string][]scanadapter.ScanOperation)
		assignVersion[latestVersion] = highAssignVersion[latestVersion]
		return assignVersion, nil
	}
	return highAssignVersion, nil
}

// 将一批任务设置为失败，并存扫描列表中删除
func (s *scan) deleteFailTask(scanMap []scanadapter.ScanOperation, srcTasks map[string]*LocalTask, err error) {
	if status.Code(err) == codes.NotFound {
		for _, v := range scanMap {
			log.Errorf("get virus library failed, kamala scan warn: %v", err.Error())
			localTask := srcTasks[v.Path]
			localTask.task.Code = entities.ScanCode_OTHER
			localTask.task.Message = "get virus library failed, kamala scan warn: " + err.Error()
			s.resultChan <- localTask
			delete(srcTasks, v.Path)
		}
	} else {
		for _, v := range scanMap {
			localTask := srcTasks[v.Path]
			localTask.task.Code = entities.ScanCode_OTHER
			localTask.task.MaxRetryTimes = 3
			localTask.task.Message = fmt.Sprintf("get virus library failed, MaxRetryTimes:%v, RetryTimes:%v, kamala scan warn:%v", localTask.task.MaxRetryTimes, localTask.task.RetryTimes, err.Error())
			log.Errorf("get virus library failed, MaxRetryTimes:%v, RetryTimes:%v, kamala scan warn:%v", localTask.task.MaxRetryTimes, localTask.task.RetryTimes, err.Error())
			s.resultChan <- localTask
			delete(srcTasks, v.Path)
		}
	}
}

// 默认ScanOperation是指那些非统一升级的鉴定器的scan接口需要的输入参数，统一升级特殊在于它需要去一些额外操作去根据具体指定的版本分堆，
// 并提前判断指定版本的版本库目录是否存在，若是不存在，这个任务将被视为版本参数非法而被处理掉；
func defaultScanOperations(tasks map[string]*LocalTask) []scanadapter.ScanOperation {
	result := make([]scanadapter.ScanOperation, 0)

	for k, v := range tasks {
		if v.task.GetPriority() == entities.Priority_VERY_HIGH {
			v.waitScanTime = time.Since(v.downloadOk)
			result = append(result, createScanOperation(k, v))
		}
	}

	if len(result) == 0 {
		for k, v := range tasks {
			v.waitScanTime = time.Since(v.downloadOk)
			result = append(result, createScanOperation(k, v))
		}
		return result
	}
	return result
}

func createScanOperation(sampleName string, localTask *LocalTask) scanadapter.ScanOperation {
	// 这里对超大样本进行支持
	prefix := ""
	// 这里对超大样本进行支持
	if os.Getenv("ENABLE_USE_PLUS_DISK") == "yes" && strings.HasPrefix(sampleName, "file") {
		if os.Getenv("KAMALA_SCANNER_FILE_PLUS") != "" && os.Getenv("KAMALA_SCANNER_FILE_PLUS") != os.Getenv("KAMALA_RELAY_DIR") {
			prefix = "KAMALA_SCANNER_FILE_PLUS"
		} else {
			prefix = "KAMALA_RELAY_DIR"
		}
	}

	scamOperation := scanadapter.ScanOperation{
		Path:          sampleName,
		PathPrefixVar: prefix,
		Parameter:     localTask.task.GetParameter(),
		OutputDir:     localTask.logOutputDir,
	}

	return scamOperation
}

func (s *scan) addTaskInfo(data *LocalTask, monitor map[string]string) {
	monitor["fetch_duration"] = data.getOneFetchDuration().String()
	monitor["scan_duration"] = data.getOneScanDuration().String()

	monitor["download_retry_count"] = strconv.Itoa(data.getDownloadInfo().RetryCount)
	monitor["scan_retry_count"] = strconv.Itoa(data.getOnceScanRetryCount())
	monitor["upload_retry_count"] = strconv.Itoa(data.getUplaodInfo().RetryCount)
	monitor["update_retry_count"] = strconv.Itoa(data.updateRetryCount)

	monitor["update_duration"] = data.updateDuration.String()
	monitor["deliverTask_rate"] = "1"
	monitor["pattern_version"] = "1"
	monitor["engine_version"] = "1"

	monitor["download_total_duration"] = data.downloadDuration.String()
	monitor["download_grpc_duration"] = data.getDownloadInfo().GetGrpcDuration().String()
	monitor["download_http_duration"] = data.getDownloadInfo().GetHttpDuration().String()

	monitor["upload_total_duration"] = data.uploadDuration.String()
	monitor["upload_grpc_duration"] = data.getUplaodInfo().GetGrpcDuration().String()
	monitor["upload_http_duration"] = data.getUplaodInfo().GetHttpDuration().String()
	monitor["scanner_name"] = "1"
	monitor["content_size"] = strconv.Itoa(data.downloadInfo.GetContentSize())

	monitor["mnt_scanner_name"] = s.scannerController.GetScanner().GetName()
	monitor["mnt_download_source"] = data.downloadInfo.Source
	monitor["mnt_state"] = data.task.GetState().String()
	monitor["mnt_priority"] = data.task.GetPriority().String()
	monitor["mnt_asset"] = data.task.GetAssetName()
	monitor["mnt_code"] = data.task.GetCode().String()
	monitor["mnt_pattern_version"] = data.task.GetPatternVersion()
	monitor["mnt_engine_version"] = data.task.GetEngineVersion()
}

func (s *scan) isUseDisk(taskSize uint64) bool {
	ret := false
	totalUsed, free := s.getSampleDirAndLogDirSpace()
	// TODO 适配支持对当前内存盘使用空间的管理, 确保不因文件下载过多/过大
	if taskSize != 0 && taskSize != 1 {
		atomic.AddUint64(&s.estimateSize, taskSize)
		if s.estimateSize > (s.emptyDirSize/2) ||
			s.estimateSize > s.assetController.GetAsset().GetScanFeature().GetMaxFetchSize() {
			atomic.AddUint64(&s.estimateSize, -taskSize)
			return true
		}

		if uint64(totalUsed) >= s.emptyDirSize {
			atomic.AddUint64(&s.estimateSize, -taskSize)
			return true
		}
		log.Debugf("[test debug]:判断是否使用第二盘流程，样本大小已知为：%.2fM, 此时estimateSize：%.2fM, "+
			"空闲emptyDir大小为：%.2fM", float64(taskSize)/MB, float64(s.estimateSize)/MB, float64(free)/MB)
		if free < taskSize {
			atomic.AddUint64(&s.estimateSize, -taskSize)
			ret = true
		}
	} else {
		ret = true
	}
	return ret
}

func (s *scan) getSampleDirAndLogDirSpace() (uint64, uint64) {
	fileDirUsed, _ := util.GetDirSize(s.fileDir)
	logDirUsed, _ := util.GetDirSize(s.logDir)
	free := s.emptyDirSize - fileDirUsed - logDirUsed
	return fileDirUsed + logDirUsed, free
}

// task size大于GB的时候应该直接去抢10G和20G的目录列表，对于大于10G的情况应该直接去抢20G的目录列表
func chooseDirList(taskSize uint64) []DirInfo {
	var dirList []DirInfo
	if taskSize > 5*GB && taskSize <= 10*GB {
		dirList = getMiddleDirList()
		dirList = append(dirList, getBigDirList()...)
	} else if taskSize > 10*GB && taskSize <= 20*GB {
		dirList = getBigDirList()
	} else if taskSize > 20*GB {
		//  创建新方法，获取超大目录
		log.Debugf("[chooseDirList]task size: %v > 20G, choose huge dir list", taskSize)
		dirList = getHugeDirList()
	} else {
		dirList = getDirList()
	}
	return dirList
}

func getHugeDirList() []DirInfo {
	var dirList []DirInfo
	for i := bigFileEndPos + 1; i <= hugeFileEndPos; i++ {
		dirList = append(dirList, DirInfo{Size: hugeFile, Name: fmt.Sprintf("file%v", strconv.Itoa(i))})
	}
	return dirList
}

func getSmallDirList() []DirInfo {
	var dirList []DirInfo
	for i := 1; i <= smallFileEndPos; i++ {
		dirList = append(dirList, DirInfo{Size: smallFile, Name: fmt.Sprintf("file%v", strconv.Itoa(i))})
	}
	return dirList
}

func getMiddleDirList() []DirInfo {
	var dirList []DirInfo
	for i := smallFileEndPos + 1; i <= middleFileEndPos; i++ {
		dirList = append(dirList, DirInfo{Size: middleFile, Name: fmt.Sprintf("file%v", strconv.Itoa(i))})
	}
	return dirList
}

func getBigDirList() []DirInfo {
	var dirList []DirInfo
	for i := middleFileEndPos + 1; i <= bigFileEndPos; i++ {
		dirList = append(dirList, DirInfo{Size: bigFile, Name: fmt.Sprintf("file%v", strconv.Itoa(i))})
	}
	return dirList
}

func NewFileLock(fileName string) (*FileLock, error) {
	f, err := os.Open(fileName)
	if err != nil {
		return nil, fmt.Errorf("new file lock failed open error: %v", err)
	}
	return &FileLock{f: f}, nil
}

func (lock *FileLock) Release() {
	_ = lock.f.Close()
}

func (lock *FileLock) Lock() error {
	if lock == nil || lock.f == nil {
		return fmt.Errorf("lock error: FileLock: %v or FileLock.f: %v is nil", lock, lock.f)
	}
	return syscall.Flock(int(lock.f.Fd()), syscall.LOCK_EX|syscall.LOCK_NB)
}

func (lock *FileLock) UnLock() error {
	if lock == nil || lock.f == nil {
		return fmt.Errorf("lock error: FileLock: %v or FileLock.f: %v is nil", lock, lock.f)
	}
	return syscall.Flock(int(lock.f.Fd()), syscall.LOCK_UN)
}

func tryLock(filePath string) (*FileLock, error) {
	sampleFileLock, err := NewFileLock(filePath)
	if err != nil {
		log.Errorf("try to lock sample file failed: %v", err)
		return sampleFileLock, err
	}
	// 只要这个样本文件落盘成功并且真实存在那么应该就是一定能锁住的，因为不会再有哪个进程会去锁住它，它是唯一的
	err = sampleFileLock.Lock()
	if err != nil {
		sampleFileLock.Release()
		log.Errorf("try to lock sample file lock error: %v", err)
	}
	return sampleFileLock, nil
}

func (s *scan) generateRelativePath(isUseDisk bool, src string) string {
	if isUseDisk {
		dest, err := filepath.Rel(s.plusFileDir, src)
		if err != nil {
			log.Errorf("generate relative path failed: %v", err)
		}
		return dest
	} else {
		return path.Base(src)
	}
}

// func (s *scan) retryReload(versionDir string) {

// 	attempt := 0
// 	for {
// 		reloadErr := s.reload(versionDir)
// 		if reloadErr == nil {
// 			break
// 		}
// 		attempt++
// 		time.Sleep(time.Second * 2)
// 		log.Errorf("scanner reload failed, retry count: %v", attempt)
// 	}
// 	// err := retry.Retry(func(attempt uint) error {
// 	// 	retryErr := s.reload(versionDir)
// 	// 	if retryErr != nil {
// 	// 		log.Warnf("[%v] retry to reload version dir: %v failed: %v", attempt, versionDir, retryErr)
// 	// 		return retryErr
// 	// 	}
// 	// 	return nil
// 	// }, strategy.Limit(5), strategy.Wait(time.Second))
// 	// if err != nil {
// 	// 	log.Errorf("retry reload dir: %v failed: %v", versionDir, err)
// 	// }
// }

func (s *scan) emptyDirIsInsufficient() bool {
	totalUsed, _ := s.getSampleDirAndLogDirSpace()
	maxFetchSize := int64(s.assetController.GetAsset().GetScanFeature().GetMaxFetchSize())
	if maxFetchSize == 0 {
		maxFetchSize = int64(s.assetController.GetAsset().GetDeployFeature().GetSharedDirSize() / 2)
	}
	if totalUsed >= uint64(maxFetchSize) {
		// 当前已用空间达到设置的上限值，触发停止继续 FetchTask
		return true
	}
	return false
}

func (s *scan) blockCheckSpaceCondition() {
	for {
		if s.estimateSize <= s.getMaxFetchSize() {
			s.spaceInsufficient = false
			return
		} else {
			log.Warnf("[estimateSize: %v > maxFetchSize: %v]continue wait space release", s.estimateSize, s.getMaxFetchSize())
			time.Sleep(time.Second)
			continue
		}
	}
}

func (s *scan) getMaxFetchSize() uint64 {
	maxFetchSize := s.assetController.GetAsset().GetScanFeature().GetMaxFetchSize()
	log.Info("get max fetch size: %v", maxFetchSize)
	if maxFetchSize == 0 {
		maxFetchSize = s.emptyDirSize / 2
		log.Info("get max fetch size is zero set it to emptyDirSize/2: %v", maxFetchSize)
	}
	return maxFetchSize
}
