VERSION=$(shell git describe --tags --always --dirty --dirty="-alpha")
GIT_BRANCH=$(shell git branch --no-color 2> /dev/null | sed -e '/^[^*]/d' -e 's/* \(.*\)/\1/' -e 's/\//\_/g')
TIME=$(shell date +%Y.%m.%d-%Hh%Mm)
GIT_BRANCH=$(shell git branch --no-color 2> /dev/null | sed -e '/^[^*]/d' -e 's/* \(.*\)/\1/' -e 's/\//\_/g')
PUBLISH_PATH=harbor.ops.qianxin-inc.cn/zion/
OUTPUT=kamala-collector
ARM_MACHINE=arm64
MIPS_MACHINE=mips64le

all: version

.PHONY: build v3

# Usage:
#   make build            -> default (v4) build
#   make build v3         -> build with v3 adapter tag
build:
	@if echo "$(MAKECMDGOALS)" | grep -qw v3; then \
		echo "[build] using scan_v3"; \
		go mod edit -require=git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git@v1.0.0; \
		go get git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git@v1.0.0; \
		go mod tidy; \
		go build -tags scan_v3 -o $(OUTPUT) ; \
	else \
		echo "[build] default"; \
		go mod edit -require=git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git@v2.0.0+incompatible; \
		go get git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git@v2.0.0+incompatible; \
		go mod tidy; \
		go build -o $(OUTPUT) ; \
	fi

v3:
	@true

build_arm64:
	GOOS=linux GOARCH=$(ARM_MACHINE) CGO_ENABLED=0 go build -o $(OUTPUT)

build_mips64le:
	GOOS=linux GOARCH=$(MIPS_MACHINE) CGO_ENABLED=0 go build -o $(OUTPUT)

test:
	@if echo "$(MAKECMDGOALS)" | grep -qw v3; then \
		echo "[test] using scan_v3"; \
		go mod edit -require=git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git@v1.0.0; \
		go get git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git@v1.0.0; \
		go mod tidy; \
		go test -tags scan_v3 -gcflags=all=-l -covermode set -coverprofile coverage.txt ./... ; \
	else \
		echo "[test] default"; \
		go mod edit -require=git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git@v2.0.0+incompatible; \
		go get git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git@v2.0.0+incompatible; \
		go mod tidy; \
		go test -gcflags=all=-l -covermode set -coverprofile coverage.txt ./... ; \
	fi


image: 
	docker build -t $(PUBLISH_PATH)$(OUTPUT):${GIT_BRANCH}-${VERSION}-${TIME} .

image_arm64: build_arm64
	./make.sh arm64 && docker build -t $(PUBLISH_PATH)$(OUTPUT):${BRANCH}-${VERSION}-${TIME}-$(ARM_MACHINE) . && git checkout Dockerfile

image_mips64le: build_mips64le
	./make.sh mips64le && docker build -t $(PUBLISH_PATH)$(OUTPUT):${BRANCH}-${VERSION}-${TIME}-$(MIPS_MACHINE) . && git checkout Dockerfile

publish: image
	docker push $(PUBLISH_PATH)$(OUTPUT):${GIT_BRANCH}-${VERSION}-${TIME}

publish_arm64: image_arm64
	docker push $(PUBLISH_PATH)$(OUTPUT):${BRANCH}-${VERSION}-${TIME}-$(ARM_MACHINE)

publish_image_mips64le: image_mips64le
	docker push $(PUBLISH_PATH)$(OUTPUT):${BRANCH}-${VERSION}-${TIME}-$(MIPS_MACHINE)
clean:
	go clean && rm -f $(OUTPUT)

version:
	@echo ${VERSION}

.PHONY: build test image clean version publish

