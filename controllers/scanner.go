//go:generate mockgen -source=scanner.go -destination=mock/mock_scanner.go -package=mock_controllers
package controllers

import (
	"context"

	entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	scanadapter "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/adapters/scan"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"
)

type ScannerController interface {
	//TODO 细化scanner的属性读取接口
	GetScanner() *entities.Scanner

	SetAssetController(AssetController)

	Scan(ctx context.Context, scanOperation []scanadapter.ScanOperation) (*util.ScanResult, error)
	Update() (*scanadapter.Version, error)
	Reload(assignVersionDir string) (*scanadapter.Version, error)
	Exit() error

	Finalize()
	GetState() int32
	SetState(state int32)
	Ping() (*scanadapter.PingResponse, error)
}
