package asset

import (
	"context"
	"reflect"
	"time"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	worker_api "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/worker.git/api"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers"

	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"
)

type controller struct {
	util.LoopController
	asset      *entities.Asset
	lastUpdate time.Time

	scannerController controllers.ScannerController

	dialer util.Dialer
}

func NewController(dialer util.Dialer) controllers.AssetController {
	c := &controller{
		dialer: dialer,
	}

	c.Run(c.loopGetAsset)
	return c
}

func (c *controller) SetScannerController(sc controllers.ScannerController) {
	c.scannerController = sc
}

func (c *controller) GetAsset() *entities.Asset {
	return c.asset
}

func (c *controller) getAsset(name string) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	logger := log.WithContext(ctx)
	assetClient, err := c.dialer.FetchAssetClient(ctx)
	if err != nil {
		logger.Warnf("failed to get asset client, err: %v", err)
		return
	}

	response, err := assetClient.GetAsset(ctx, &worker_api.GetAsset_Request{
		Name: name,
	})
	if err != nil {
		logger.Warnf("failed to fetch asset: %v, err: %v", name, err)
		return
	}

	// log.Infof("get asset: (%+v), current asset: (%+v)", response, c.asset)

	if !reflect.DeepEqual(c.asset, response) {
		log.Debugf("success to get asset differently: %+v", response)
		c.asset = response
	}
	c.lastUpdate = time.Now()
}

func (c *controller) loopGetAsset() {
	tick := time.Tick(100 * time.Millisecond) //tick to check the stop
	interval := time.Tick(3 * time.Second)

	for !c.Stop {

		select {
		case <-tick:
			continue
		case <-interval:
			name := c.scannerController.GetScanner().GetAssetName()
			if name == "" {
				continue
			}
			c.getAsset(name)
		}
	}
}
