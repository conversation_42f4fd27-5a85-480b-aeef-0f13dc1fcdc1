package asset

import (
	"context"
	"fmt"
	"testing"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git"
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/dialer"
	entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	worker_api "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/worker.git/api"
	mockController "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers/mock"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/mock"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestGetAsset(t *testing.T) {

	// 调用dailer.FetchAssetClient 失败
	t.Run("Call FetchAssetClient failed", func(t *testing.T) {
		assetController := &controller{}
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		dialer := mock.NewMockDialer(ctrl)
		dialer.EXPECT().FetchAssetClient(gomock.Any()).Return(nil, fmt.Errorf("%s", "fetch error")).Times(1)
		assetController.dialer = dialer
		assetController.getAsset("bd")
		assert.Nil(t, assetController.asset)
	})

	t.Run("Call GetAsset failed", func(t *testing.T) {
		assetController := &controller{}
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		dialer := mock.NewMockDialer(ctrl)
		mockAssetClient := mock.NewMockAssetClient(ctrl)
		mockAssetClient.EXPECT().GetAsset(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("get asser error")).Times(1)
		dialer.EXPECT().FetchAssetClient(gomock.Any()).DoAndReturn(func(ctx context.Context) (worker_api.AssetClient, error) {
			return mockAssetClient, nil
		}).Times(1)
		assetController.dialer = dialer
		assetController.getAsset("bd")
		assert.Nil(t, assetController.asset)
	})

	t.Run("success", func(t *testing.T) {
		assetController := &controller{}
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		dialer := mock.NewMockDialer(ctrl)
		mockAssetClient := mock.NewMockAssetClient(ctrl)
		mockAssetClient.EXPECT().GetAsset(gomock.Any(), gomock.Any()).Return(&entities.Asset{
			Name: "bd",
		}, nil).Times(1)
		dialer.EXPECT().FetchAssetClient(gomock.Any()).DoAndReturn(func(ctx context.Context) (worker_api.AssetClient, error) {
			return mockAssetClient, nil
		}).Times(1)
		assetController.dialer = dialer
		assetController.getAsset("bd")
		assert.Equal(t, "bd", assetController.asset.GetName())

	})
}

func TestGetAssetFromRel(t *testing.T) {
	defer framework.Init()()

	ctx := context.Background()
	// name := os.Getenv("KAMALA_API_ASSET")

	conn := dialer.CreateClientConnection(dialer.WithTarget("10.252.16.140:30979"))

	assetClient := worker_api.NewAssetClient(conn)

	response, err := assetClient.GetAsset(ctx, &worker_api.GetAsset_Request{
		Name: "typer",
	})

	fmt.Println(response.GetScanFeature())
	fmt.Println(err)
}

func Test_SetScannerController(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockAssetClient := mock.NewMockAssetClient(ctrl)
	mockAssetClient.EXPECT().GetAsset(gomock.Any(), gomock.Any()).Return(
		&entities.Asset{
			Name: "test",
		}, nil,
	).AnyTimes()
	mockDialer := mock.NewMockDialer(ctrl)
	mockDialer.EXPECT().FetchAssetClient(gomock.Any()).Return(mockAssetClient, nil).AnyTimes()

	mockScanner := mockController.NewMockScannerController(ctrl)
	mockScanner.EXPECT().GetScanner().Return(
		&entities.Scanner{
			Name:      "test",
			AssetName: "test",
		},
	).AnyTimes()

	assetController := NewController(mockDialer)
	assetController.SetScannerController(mockScanner)
	assetController.GetAsset()

}
