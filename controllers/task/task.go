package task

import (
	"context"
	"strconv"
	"strings"
	"sync/atomic"
	"time"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/apis/worker.git/api"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"
	"github.com/Rican7/retry"
	"github.com/Rican7/retry/strategy"
	"google.golang.org/genproto/protobuf/field_mask"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type controller struct {
	util.LoopController
	Dialer            util.Dialer
	assetController   controllers.AssetController
	scannerController controllers.ScannerController
	relayMode         int32
}

func NewController(dialer util.Dialer, assetController controllers.AssetController, scannerController controllers.ScannerController) controllers.TaskController {
	c := &controller{
		Dialer:            dialer,
		assetController:   assetController,
		scannerController: scannerController,
	}
	// 初次启动时候先判断一次fetch的模式，0默认，1走relay
	atomic.StoreInt32(&c.relayMode, c.getRelayState())
	// 每隔3s判断一次relay服务是否存在
	c.Run(c.loopCheckRelay)
	return c
}

func (c *controller) loopCheckRelay() {
	tick := time.Tick(100 * time.Millisecond) //tick to check the stop
	interval := time.Tick(3 * time.Second)

	for !c.Stop {
		select {
		case <-tick:
			continue
		case <-interval:
			atomic.StoreInt32(&c.relayMode, c.getRelayState())
		}
	}
}

// if relay available return 1; else return 0
func (c *controller) getRelayState() int32 {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	relayClient, _ := c.Dialer.FetchRelayClient(ctx)
	_, err := relayClient.FetchTasks(ctx, &api.FetchTasks_Request{
		FetchUnit: []*api.FetchUnit{
			{
				Asset:  c.assetController.GetAsset().GetName(),
				Count:  uint32(0),
				Source: c.scannerController.GetScanner(),
			},
		},
	})

	if err != nil {
		return 0
	} else {
		return 1
	}
}

func (c *controller) getRelayClient(ctx context.Context) (api.TaskClient, error) {
	var relayClient api.TaskClient
	err := retry.Retry(func(attempt uint) error {
		var err error
		relayClient, err = c.Dialer.FetchRelayClient(ctx)
		if err != nil {
			log.Warnf("failed to get relay client, err: %v", err)
		}
		return err
	}, strategy.Limit(3), strategy.Wait(100*time.Millisecond))

	return relayClient, err
}

func (c *controller) getTaskClient(ctx context.Context) (api.TaskClient, error) {
	var taskClient api.TaskClient
	err := retry.Retry(func(attempt uint) error {
		var err error
		taskClient, err = c.Dialer.FetchTaskClient(ctx)
		if err != nil {
			log.Warnf("failed to get task client, err: %v", err)
		}
		return err
	}, strategy.Limit(10), strategy.Wait(100*time.Millisecond))

	return taskClient, err
}

func (c *controller) FetchTask(assetName string, count int32) []*entities.Task {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	logger := log.WithContext(ctx)

	taskClient := c.getTaskClientOrRelayClient(ctx)

	// fetch(0)上报状态时，取asset中的最新版本进行状态的上报，因为assetController中会
	// loopGetAsset，获取得到的都是最新的asset，用这个里面保存的版本信息去上报状态
	scanner := c.scannerController.GetScanner()
	if scanner == nil {
		logger.Warnf("get scanner:%v is empty", assetName)
		return nil
	}
	version := &entities.Version{
		Pattern: scanner.GetVersion().GetPattern(),
		Engine:  scanner.GetVersion().GetEngine(),
	}
	source := &entities.Scanner{
		Name:           scanner.GetName(),
		AssetName:      scanner.GetAssetName(),
		StartupSeconds: scanner.GetStartupSeconds(),
		ScanCount:      scanner.GetScanCount(),
		Version:        version,
		TaskNames:      scanner.GetTaskNames(),
		CreateTime:     scanner.GetCreateTime(),
		UpdateTime:     scanner.GetUpdateTime(),
	}

	if util.IsUniteUpdate(c.assetController.GetAsset()) {
		versions := c.assetController.GetAsset().GetUpdateFeature().GetVersionDepot().GetVersions()
		// 初始情况可能没有版本，等待升级
		if len(versions) == 0 {
			logger.Warnf("asset:%v versions is empty", assetName)
		} else {
			source.Version.Pattern = versions[len(versions)-1].GetPattern()
			source.Version.Engine = versions[len(versions)-1].GetEngine()
		}
	}
	begin := time.Now()
	r, err := taskClient.FetchTasks(ctx, &api.FetchTasks_Request{
		FetchUnit: []*api.FetchUnit{
			{
				Asset:  assetName,
				Count:  uint32(count),
				Source: source,
			},
		},
	})
	/*
		r, err := taskClient.FetchTasks(ctx, &api.FetchTasks_Request{
			Parent:      "assets/" + assetName,
			Count:       count,
			ScannerName: c.scannerController.GetScanner().Name,
		})
	*/

	if err != nil {
		logger.Errorf("failed to fetch tasks, err %v, asset: %v", err, assetName)
		return nil
	} else {
		if r != nil && len(r.Tasks) > 0 {
			logger.Debugf("[%v] fetch tasks, name: %v, count %v, tasks: %v",
				time.Since(begin), assetName, len(r.Tasks), r.Tasks)
		}

		return r.Tasks
	}
}

func (c *controller) GetBacktrace(t *entities.Task) string {
	if t.Code != entities.ScanCode_OK {
		z := strings.SplitN(t.Message, "EOFError: child aborted with code -11", 2)
		if len(z) == 2 {
			return strings.Trim(z[1], " \n")
		}
	}
	return ""
}
func (c *controller) DeliverTask(t *entities.Task, monitor map[string]string) error {
	logger := log.WithContext(context.Background())
	if t.Code != entities.ScanCode_OK {
		t.State = entities.TaskState_TASK_FAILED
	} else {
		t.State = entities.TaskState_TASK_SUCCEED
	}
	monitor["mnt_state"] = t.GetState().String()
	backtrace := c.GetBacktrace(t)
	if len(backtrace) > 0 {
		monitor["backtrace"] = backtrace
	}
	request := api.UpdateTask_Request{
		UpdateMask: &field_mask.FieldMask{
			Paths: []string{
				"State",
				"Code",
				"Message",
				"EngineVersion",
				"PatternVersion",
				//"FetchUri", // the uri may be substitued
				"StoreUri", // the uri may be substitued
				"StoreHash",
				"StoreSize",
				"MaxRetryTimes",
			},
		},
		Task:    t,
		Monitor: monitor,
	}

	begin := time.Now()
	err := retry.Retry(func(attempt uint) error {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		taskClient := c.getTaskClientOrRelayClient(ctx)

		begin := time.Now()
		_, err := taskClient.UpdateTask(ctx, &request)
		if err != nil {
			logger.Warnf("[%v] failed to update task: %+v, err: %v, retry: %v", time.Since(begin), request, err, attempt)
			//if status.Code(err) == codes.NotFound || status.Code(err) == codes.FailedPrecondition {
			//	return nil
			//}
			// avoid local code is completed, but mongo's code is dispatching.
			if status.Code(err) == codes.NotFound || status.Code(err) == codes.AlreadyExists {
				log.Warnf("update warn:%v", err)
				return nil
			}
		}
		// 设置update的重试次数，即直到retry遇到返回的error为nil了为止的attempt值
		monitor["update_retry_count"] = strconv.Itoa(int(attempt))
		return err
	}, strategy.Limit(3), strategy.Wait(time.Second))
	duration := time.Since(begin)
	if err != nil {
		logger.Errorf("[%v] failed to update task, err: %v, request: %v", duration, err, request)
	} else {
		logger.Debugf("[%v] succeed to update task, request: %v", duration, request)
	}
	return err
}

func (c *controller) GetMode() int32 {
	return atomic.LoadInt32(&c.relayMode)
}

func (c *controller) getTaskClientOrRelayClient(ctx context.Context) api.TaskClient {
	var taskClient api.TaskClient

	if atomic.LoadInt32(&c.relayMode) == 1 {
		taskClient, _ = c.getRelayClient(ctx)
	} else {
		taskClient, _ = c.getTaskClient(ctx)
	}

	return taskClient
}

func (c *controller) isUnitUpdate() bool {
	if c.assetController.GetAsset().GetUpdateFeature().GetUpdaterInterval() != 0 &&
		c.assetController.GetAsset().GetUpdateFeature().GetUpdaterVolumeSize() != 0 {
		return true
	}
	return false
}
