package task

import (
	"fmt"
	"reflect"
	"testing"

	entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	mock_controllers "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers/mock"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/mock"
	sm "github.com/cch123/supermonkey"
	"go.uber.org/mock/gomock"
	"github.com/stretchr/testify/assert"
)

func Test_controller_loopCheckRelay(t *testing.T) {
	type fields struct {
	}
	tests := []struct {
		name string
		stop bool
	}{
		{
			name: "",
			stop: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &controller{}
			sm.Patch((*controller).getRelayState, func(c *controller) int32 {
				c.Stop = true
				return 1
			})
			c.loopCheckRelay()
		})
	}
}

func Test_controller_DeliverTask(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			wantErr: true,
		},
		{
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			dialer := mock.NewMockDialer(ctrl)
			taskClient := mock.NewMockTaskClient(ctrl)
			dialer.EXPECT().FetchTaskClient(gomock.Any()).Return(taskClient, nil).AnyTimes()
			if !tt.wantErr {
				taskClient.EXPECT().UpdateTask(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			} else {
				taskClient.EXPECT().UpdateTask(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("update failed")).AnyTimes()
			}
			c := &controller{
				Dialer: dialer,
			}
			if err := c.DeliverTask(&entities.Task{}, make(map[string]string)); (err != nil) != tt.wantErr {
				t.Errorf("controller.DeliverTask() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_controller_FetchTask(t *testing.T) {
	tests := []struct {
		name      string
		wantErr   bool
		relayMode int
		want      []*entities.Task
	}{
		// TODO: Add test cases.
		{
			wantErr:   true,
			relayMode: 1,
		},
		{
			wantErr:   true,
			relayMode: 0,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			dialer := mock.NewMockDialer(ctrl)
			taskClient := mock.NewMockTaskClient(ctrl)
			assetController := mock_controllers.NewMockAssetController(ctrl)
			scannerController := mock_controllers.NewMockScannerController(ctrl)
			assetController.EXPECT().GetAsset().Return(&entities.Asset{}).AnyTimes()
			scannerController.EXPECT().GetScanner().Return(&entities.Scanner{}).AnyTimes()
			dialer.EXPECT().FetchTaskClient(gomock.Any()).Return(taskClient, nil).AnyTimes()
			dialer.EXPECT().FetchRelayClient(gomock.Any()).Return(taskClient, nil).AnyTimes()
			if !tt.wantErr {
				taskClient.EXPECT().FetchTasks(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			} else {
				taskClient.EXPECT().FetchTasks(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("update failed")).AnyTimes()
			}
			c := &controller{
				Dialer:            dialer,
				relayMode:         int32(tt.relayMode),
				scannerController: scannerController,
				assetController:   assetController,
			}
			if got := c.FetchTask("bd", 3); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("controller.FetchTask() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_isUnitUpdate(t *testing.T) {
	tests := []struct {
		in   int64
		want bool
	}{
		{
			in:   0,
			want: false,
		},
		{
			in:   1,
			want: true,
		},
	}

	for _, test := range tests {

		ctrl := gomock.NewController(t)
		asset := mock_controllers.NewMockAssetController(ctrl)
		asset.EXPECT().GetAsset().Return(
			&entities.Asset{
				UpdateFeature: &entities.UpdateFeature{
					UpdaterInterval:   test.in,
					UpdaterVolumeSize: uint64(test.in),
				},
			},
		).AnyTimes()
		c := controller{
			assetController: asset,
		}
		res := c.isUnitUpdate()

		assert.Equal(t, test.want, res)
	}

}
