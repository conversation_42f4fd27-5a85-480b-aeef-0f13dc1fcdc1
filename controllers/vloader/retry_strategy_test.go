package vloader

import (
	"errors"
	"testing"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// 测试错误分类函数 - 测试实际的 controller 方法
func TestController_IsRetryableError(t *testing.T) {
	// 创建一个真实的 controller 实例用于测试
	controller := &controller{}

	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{
			name:     "nil error",
			err:      nil,
			expected: false,
		},
		{
			name:     "gRPC timeout error",
			err:      status.<PERSON><PERSON><PERSON>(codes.DeadlineExceeded, "timeout"),
			expected: true,
		},
		{
			name:     "gRPC unavailable error",
			err:      status.<PERSON><PERSON><PERSON>(codes.Unavailable, "service unavailable"),
			expected: true,
		},
		{
			name:     "gRPC resource exhausted error",
			err:      status.<PERSON><PERSON><PERSON>(codes.ResourceExhausted, "resource exhausted"),
			expected: true,
		},
		{
			name:     "gRPC internal error",
			err:      status.<PERSON><PERSON><PERSON>(codes.Internal, "internal error"),
			expected: true,
		},
		{
			name:     "gRPC authentication error",
			err:      status.<PERSON><PERSON><PERSON>(codes.Unauthenticated, "authentication failed"),
			expected: false,
		},
		{
			name:     "gRPC permission denied error",
			err:      status.<PERSON><PERSON><PERSON>(codes.PermissionDenied, "permission denied"),
			expected: false,
		},
		{
			name:     "gRPC invalid argument error",
			err:      status.Errorf(codes.InvalidArgument, "invalid argument"),
			expected: false,
		},
		{
			name:     "gRPC not found error",
			err:      status.Errorf(codes.NotFound, "not found"),
			expected: false,
		},
		{
			name:     "network connection refused error",
			err:      errors.New("connection refused"),
			expected: true,
		},
		{
			name:     "network timeout error",
			err:      errors.New("i/o timeout"),
			expected: true,
		},
		{
			name:     "network unreachable error",
			err:      errors.New("network is unreachable"),
			expected: true,
		},
		{
			name:     "unknown error",
			err:      errors.New("unknown error"),
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := controller.isRetryableError(tt.err)
			if result != tt.expected {
				t.Errorf("isRetryableError() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

// 测试重试策略选择函数 - 测试实际的 controller 方法
func TestController_SelectRetryStrategy(t *testing.T) {
	controller := &controller{}

	tests := []struct {
		name       string
		retryCount int
		err        error
		expected   RetryStrategy
	}{
		{
			name:       "normal retry - retryable error",
			retryCount: 5,
			err:        status.Errorf(codes.DeadlineExceeded, "timeout"),
			expected:   RetryNormal,
		},
		{
			name:       "degraded retry - retryable error",
			retryCount: 15,
			err:        status.Errorf(codes.DeadlineExceeded, "timeout"),
			expected:   RetryDegraded,
		},
		{
			name:       "persistent retry - retryable error",
			retryCount: 35,
			err:        status.Errorf(codes.DeadlineExceeded, "timeout"),
			expected:   RetryPersistent,
		},
		{
			name:       "degraded retry - non-retryable error",
			retryCount: 5,
			err:        status.Errorf(codes.Unauthenticated, "auth failed"),
			expected:   RetryDegraded,
		},
		{
			name:       "normal retry - first attempt",
			retryCount: 0,
			err:        status.Errorf(codes.Unavailable, "service unavailable"),
			expected:   RetryNormal,
		},
		{
			name:       "normal retry - last normal attempt",
			retryCount: 9,
			err:        status.Errorf(codes.Unavailable, "service unavailable"),
			expected:   RetryNormal,
		},
		{
			name:       "degraded retry - first degraded attempt",
			retryCount: 10,
			err:        status.Errorf(codes.Unavailable, "service unavailable"),
			expected:   RetryDegraded,
		},
		{
			name:       "degraded retry - last degraded attempt",
			retryCount: 29,
			err:        status.Errorf(codes.Unavailable, "service unavailable"),
			expected:   RetryDegraded,
		},
		{
			name:       "persistent retry - first persistent attempt",
			retryCount: 30,
			err:        status.Errorf(codes.Unavailable, "service unavailable"),
			expected:   RetryPersistent,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := controller.selectRetryStrategy(tt.retryCount, tt.err)
			if result != tt.expected {
				t.Errorf("selectRetryStrategy() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

// 测试等待时间计算函数 - 测试实际的 controller 方法
func TestController_CalculateWaitTime(t *testing.T) {
	controller := &controller{}

	tests := []struct {
		name        string
		retryCount  int
		strategy    RetryStrategy
		expectedMin time.Duration
		expectedMax time.Duration
	}{
		{
			name:        "normal retry - first attempt",
			retryCount:  0,
			strategy:    RetryNormal,
			expectedMin: 750 * time.Millisecond,  // 1s - 25% jitter
			expectedMax: 1250 * time.Millisecond, // 1s + 25% jitter
		},
		{
			name:        "normal retry - second attempt",
			retryCount:  1,
			strategy:    RetryNormal,
			expectedMin: 1500 * time.Millisecond, // 2s - 25% jitter
			expectedMax: 2500 * time.Millisecond, // 2s + 25% jitter
		},
		{
			name:        "normal retry - third attempt",
			retryCount:  2,
			strategy:    RetryNormal,
			expectedMin: 3000 * time.Millisecond, // 4s - 25% jitter
			expectedMax: 5000 * time.Millisecond, // 4s + 25% jitter
		},
		{
			name:        "normal retry - max interval",
			retryCount:  10,
			strategy:    RetryNormal,
			expectedMin: 22500 * time.Millisecond, // 30s - 25% jitter
			expectedMax: 37500 * time.Millisecond, // 30s + 25% jitter
		},
		{
			name:        "degraded retry",
			retryCount:  15,
			strategy:    RetryDegraded,
			expectedMin: 15000 * time.Millisecond, // 30s - 50% jitter
			expectedMax: 45000 * time.Millisecond, // 30s + 50% jitter
		},
		{
			name:        "persistent retry",
			retryCount:  35,
			strategy:    RetryPersistent,
			expectedMin: 0 * time.Millisecond,      // 60s - 100% jitter (最小0)
			expectedMax: 120000 * time.Millisecond, // 60s + 100% jitter
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 运行多次测试以验证随机抖动
			for i := 0; i < 20; i++ {
				result := controller.calculateWaitTime(tt.retryCount, tt.strategy)
				if result < tt.expectedMin || result > tt.expectedMax {
					t.Errorf("calculateWaitTime() = %v, expected between %v and %v (attempt %d)",
						result, tt.expectedMin, tt.expectedMax, i+1)
					break
				}
			}
		})
	}
}

// 测试重试配置常量
func TestRetryConstants(t *testing.T) {
	if maxNormalRetries != 10 {
		t.Errorf("maxNormalRetries = %v, expected 10", maxNormalRetries)
	}

	if maxDegradedRetries != 20 {
		t.Errorf("maxDegradedRetries = %v, expected 20", maxDegradedRetries)
	}

	if baseRetryInterval != 1*time.Second {
		t.Errorf("baseRetryInterval = %v, expected 1s", baseRetryInterval)
	}

	if maxRetryInterval != 30*time.Second {
		t.Errorf("maxRetryInterval = %v, expected 30s", maxRetryInterval)
	}

	if degradedInterval != 30*time.Second {
		t.Errorf("degradedInterval = %v, expected 30s", degradedInterval)
	}

	if persistentInterval != 60*time.Second {
		t.Errorf("persistentInterval = %v, expected 60s", persistentInterval)
	}

	if normalJitterPercent != 25 {
		t.Errorf("normalJitterPercent = %v, expected 25", normalJitterPercent)
	}

	if degradedJitterPercent != 50 {
		t.Errorf("degradedJitterPercent = %v, expected 50", degradedJitterPercent)
	}

	if persistentJitterPercent != 100 {
		t.Errorf("persistentJitterPercent = %v, expected 100", persistentJitterPercent)
	}
}

// 测试重试策略枚举
func TestRetryStrategyEnum(t *testing.T) {
	if RetryNormal != 0 {
		t.Errorf("RetryNormal = %v, expected 0", RetryNormal)
	}

	if RetryDegraded != 1 {
		t.Errorf("RetryDegraded = %v, expected 1", RetryDegraded)
	}

	if RetryPersistent != 2 {
		t.Errorf("RetryPersistent = %v, expected 2", RetryPersistent)
	}

	if NoRetry != 3 {
		t.Errorf("NoRetry = %v, expected 3", NoRetry)
	}
}

// 基准测试：测试等待时间计算的性能
func BenchmarkCalculateWaitTime(b *testing.B) {
	controller := &controller{}

	b.Run("NormalRetry", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			controller.calculateWaitTime(5, RetryNormal)
		}
	})

	b.Run("DegradedRetry", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			controller.calculateWaitTime(15, RetryDegraded)
		}
	})

	b.Run("PersistentRetry", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			controller.calculateWaitTime(35, RetryPersistent)
		}
	})
}

// 基准测试：测试错误分类的性能
func BenchmarkIsRetryableError(b *testing.B) {
	controller := &controller{}
	err := status.Errorf(codes.DeadlineExceeded, "timeout")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		controller.isRetryableError(err)
	}
}

// 基准测试：测试重试策略选择的性能
func BenchmarkSelectRetryStrategy(b *testing.B) {
	controller := &controller{}
	err := status.Errorf(codes.DeadlineExceeded, "timeout")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		controller.selectRetryStrategy(10, err)
	}
}
