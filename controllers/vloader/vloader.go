package vloader

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"math"
	"math/rand"
	"os"
	"path"
	"strconv"
	"strings"
	"time"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/helper"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"

	kamala_api_vloader "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/vloader.git/api"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type controller struct {
	scannerController controllers.ScannerController
	assetController   controllers.AssetController
	helper            helper.UpdateHelper
	dialer            util.Dialer
}

// 重试策略相关常量
const (
	// 重试阶段定义
	maxNormalRetries   = 10 // 正常重试最大次数
	maxDegradedRetries = 20 // 降级重试最大次数

	// 重试间隔配置
	baseRetryInterval  = 1 * time.Second  // 基础重试间隔
	maxRetryInterval   = 30 * time.Second // 最大重试间隔
	degradedInterval   = 30 * time.Second // 降级重试间隔
	persistentInterval = 60 * time.Second // 持久重试间隔

	// 随机抖动配置
	normalJitterPercent     = 25  // 正常重试抖动百分比
	degradedJitterPercent   = 50  // 降级重试抖动百分比
	persistentJitterPercent = 100 // 持久重试抖动百分比
)

// 重试策略枚举
type RetryStrategy int

const (
	RetryNormal     RetryStrategy = iota // 正常重试
	RetryDegraded                        // 降级重试
	RetryPersistent                      // 持久重试
	NoRetry                              // 不重试（但业务要求继续重试）
)

// 重试配置结构体
type RetryConfig struct {
	MaxNormalRetries        int
	MaxDegradedRetries      int
	BaseRetryInterval       time.Duration
	MaxRetryInterval        time.Duration
	DegradedInterval        time.Duration
	PersistentInterval      time.Duration
	NormalJitterPercent     int
	DegradedJitterPercent   int
	PersistentJitterPercent int
}

// type version struct {
// 	engineVersion  string
// 	patternVersion string
// }

func NewController(scannerController controllers.ScannerController, assetController controllers.AssetController, helper helper.UpdateHelper, dialer util.Dialer) controllers.VloaderController {
	c := &controller{
		scannerController: scannerController,
		assetController:   assetController,
		helper:            helper,
		dialer:            dialer,
	}
	return c
}

// 获取病毒库所在的目录
// engineVersion, patternVersion都为空获取最新的，否则获取指定的
func (s *controller) DownloadVirusLibrary(engineVersion, patternVersion string) (dir, engine, pattern string, err error) {
	assetName := s.scannerController.GetScanner().GetAssetName()
	channelId := s.assetController.GetAsset().UpdateFeature.GetVersionDepot().GetChannelId()
	virusLibraryInformation := fmt.Sprintf("AssetName:%v, ChannelId:%v, ", assetName, channelId)

	// 如果需要获取最新版本，先通过智能重试策略获取版本号
	if engineVersion == "" && patternVersion == "" {
		isAllowTolerance := s.isAllowTolerance()

		// 使用智能重试策略获取最新版本
		engineVersion, patternVersion, err = s.retryGetNewestVirusLibraryVersion(isAllowTolerance, virusLibraryInformation)

		// 注意：retryGetNewestVirusLibraryVersion 会阻塞直到成功，所以这里 err 应该为 nil
		// 但为了代码的健壮性，仍然检查错误
		if err != nil {
			log.Errorf("Unexpected error after retry: %v, %v, isAllowTolerance: %v, err: %v",
				virusLibraryInformation, isAllowTolerance, err.Error())
			return "", "", "", err
		}
	}

	// 统一处理：获取指定版本的病毒库目录（无论是最新版本还是指定版本）
	dir, err = s.getAssignVersionLibrary(engineVersion, patternVersion)

	if err != nil {
		if code := status.Code(err); code == codes.NotFound {
			log.Infof("get assign version virus library not found, %v, engineVersion:%v, patternVersion%v", virusLibraryInformation, engineVersion, patternVersion)
			return "", "", "", status.Errorf(code, "collector can't found assign version virus library")
		}
		log.Errorf("get assign version virus library failed, %v, engineVersion:%v, patternVersion%v, err:%v", virusLibraryInformation, engineVersion, patternVersion, err.Error())
		return "", "", "", status.Errorf(codes.Unknown, "collector get assign version library failed, err:%v", err.Error())
	}

	log.Debugf("get assign version virus library success, %v, engineVersion:%v, patternVersion%v, dir:%v", virusLibraryInformation, engineVersion, patternVersion, dir)
	return dir, engineVersion, patternVersion, nil

}

// 获取指定版本的病毒库所在的目录
func (s *controller) getAssignVersionLibrary(engineVersion, patternVersion string) (dir string, err error) {
	assetName := s.scannerController.GetScanner().GetAssetName()
	channelId := s.assetController.GetAsset().UpdateFeature.GetVersionDepot().GetChannelId()
	virusLibraryInformation := fmt.Sprintf("AssetName:%v, ChannelId:%v, engineVersion:%v, patternVersion %v", assetName, channelId, engineVersion, patternVersion)

	//判断本地有没有指定版本的病毒库
	dir, err = s.checkVirusLibraryVersionExisiInLocal(engineVersion, patternVersion)
	if err == nil {
		// log.Infof("get virus library from loacl success, %v", virusLibraryInformation)
		return
	}
	// 判断vloader有没有指定版本的病毒库
	err = s.checkVirusLibraryVersionExisiInVloader(engineVersion, patternVersion)

	if err != nil {
		return
	}

	log.Infof("check virus library from vloader success, %v", virusLibraryInformation)

	// 从vloader下载病毒库
	h := s.helper
	begin := time.Now()

	h.CleanUpdateDir()
	err = s.downloadVirusLibraryFromVloader(engineVersion, patternVersion)
	defer h.CleanUpdateDir()
	if err != nil {
		return
	}

	log.Infof("download virus library from vloader success, %v", virusLibraryInformation)
	//为下载好的病毒库创建时间戳目录
	dir, err = s.RestoreVirusLibary(engineVersion, patternVersion, begin)

	if err != nil {
		return
	}
	log.Infof("restore virus library success, %v", virusLibraryInformation)

	dir, err = s.checkVirusLibraryVersionExisiInLocal(engineVersion, patternVersion)

	return
}

// 判断一个版本在vloader是否存在，存在返回空，不存在返回错误
func (s *controller) checkVirusLibraryVersionExisiInVloader(engineVersion, patternVersion string) error {

	assetName := s.scannerController.GetScanner().GetAssetName()
	channelId := s.assetController.GetAsset().UpdateFeature.GetVersionDepot().GetChannelId()
	virusLibraryInformation := fmt.Sprintf("AssetName:%v, ChannelId:%v, engineVersion:%v, patternVersion %v", assetName, channelId, engineVersion, patternVersion)

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()
	virusLibraryClient, err := s.dialer.FetchVloaderClient(ctx, assetName)

	if err != nil {
		log.Errorf("get vloader client err,%v err:%v", virusLibraryInformation, err.Error())
		return err
	}

	getVersionRequest := &kamala_api_vloader.GetVersion_Request{
		AssetName:      assetName,
		ChannelId:      channelId,
		PatternVersion: patternVersion,
		EngineVersion:  engineVersion,
		AllowSecondNew: false,
	}

	_, err = virusLibraryClient.GetVersion(ctx, getVersionRequest)

	if err != nil {
		if code := status.Code(err); code == codes.NotFound {
			return err
		}
		log.Errorf("get version from vloader err, %v, err: %v", virusLibraryInformation, err.Error())
		return err
	}

	return nil

}

// 判断指定版本的病毒库在本地是否存在
func (s *controller) checkVirusLibraryVersionExisiInLocal(engineVersion, patternVersion string) (string, error) {
	h := s.helper
	//获取病毒库前，先对index.json和实际每个时间戳目录里的info.json中的信息进行核对
	err := h.RebuildIndex()

	if err != nil {
		return "", err
	}

	dir, err := h.GetDir(patternVersion, engineVersion)

	if err != nil {
		return "", err
	}

	return dir, err
}

// 获取最新版本的版本号
func (s *controller) getNewestVirusLibraryVersion(isAllowTolerance bool) (engineVersion, patternVersion string, err error) {
	assetName := s.scannerController.GetScanner().GetAssetName()
	channelId := s.assetController.GetAsset().UpdateFeature.GetVersionDepot().GetChannelId()
	virusLibraryInformation := fmt.Sprintf("AssetName:%v, ChannelId:%v", assetName, channelId)

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()
	virusLibraryClient, err := s.dialer.FetchVloaderClient(ctx, assetName)

	if err != nil {
		log.Errorf("get vloader client err,%v err:%v", virusLibraryInformation, err.Error())
		return
	}

	getVersionRequest := &kamala_api_vloader.GetVersion_Request{
		AssetName:      assetName,
		ChannelId:      channelId,
		PatternVersion: "",
		EngineVersion:  "",
		AllowSecondNew: isAllowTolerance,
	}

	getVersionResponse, err := virusLibraryClient.GetVersion(ctx, getVersionRequest)

	if err != nil {
		log.Errorf("get newest virus library version fail, %v, response:%v,  err: %v", virusLibraryInformation, getVersionResponse, err.Error())
		return
	}

	if getVersionResponse == nil {
		err = status.Errorf(codes.NotFound, "not found newest version %v", virusLibraryInformation)
		return
	}

	engineVersion = getVersionResponse.GetEngineVersion()
	patternVersion = getVersionResponse.GetPatternVersion()

	if engineVersion == "" && patternVersion == "" {
		err = status.Errorf(codes.NotFound, "not found newest version %v", virusLibraryInformation)
		return
	}
	return
}

// 判断是否允许使用次新的版本进行扫描
func (s *controller) isAllowTolerance() bool {
	engineScanTolerance := s.assetController.GetAsset().GetUpdateFeature().GetVersionDepot().GetEngineDefine().GetScanTolerance()
	patternScanTolerance := s.assetController.GetAsset().GetUpdateFeature().GetVersionDepot().GetPatternDefine().GetScanTolerance()

	if engineScanTolerance == 0 || patternScanTolerance == 0 {
		return false
	}

	return true

}

// 根据Asset的UpdateFeature的VersionDepot的 EngineDefine和PatternDefine的ScanTolerance来计算扫描可以容忍版本差的版本
// func (s *controller) getVersionByScanTolerance() (virusVersions []version, err error) {
// 	assetName := s.scannerController.GetScanner().GetAssetName()
// 	channelId := s.assetController.GetAsset().UpdateFeature.GetVersionDepot().GetChannelId()
// 	virusLibraryInformation := fmt.Sprintf("AssetName:%v, ChannelId:%v", assetName, channelId)

// 	engineScanTolerance := s.assetController.GetAsset().GetUpdateFeature().GetVersionDepot().GetEngineDefine().GetScanTolerance()
// 	patternScanTolerance := s.assetController.GetAsset().GetUpdateFeature().GetVersionDepot().GetPatternDefine().GetScanTolerance()

// 	engineVersionKind := helper.VersionKind(s.assetController.GetAsset().GetUpdateFeature().GetVersionDepot().GetEngineDefine().GetKind())
// 	patternVersionKind := helper.VersionKind(s.assetController.GetAsset().GetUpdateFeature().GetVersionDepot().GetPatternDefine().GetKind())

// 	if engineScanTolerance == -1 {
// 		engineScanTolerance = 10
// 	}

// 	if patternScanTolerance == -1 {
// 		engineScanTolerance = 10
// 	}

// 	tolerance := engineScanTolerance

// 	if patternScanTolerance < tolerance {
// 		tolerance = patternScanTolerance
// 	}

// 	//如果不容忍版本差，就直接返回空
// 	if tolerance == 0 {
// 		return []version{}, nil
// 	}

// 	//去version获取鉴定器的历史版本
// 	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
// 	defer cancel()
// 	versionClient, err := s.dialer.FetchVersionClient(ctx)
// 	if err != nil {
// 		log.Errorf("get version client failed,%v err:%v", virusLibraryInformation, err.Error())
// 		return
// 	}

// 	getVersionRequest := kamala_api_model.ListVersions_Request{
// 		PageSize: 10,
// 		Query: &kamala_entities.Value{
// 			MapValue: map[string]*kamala_entities.Value{
// 				"asset_name": {Type_Union: &kamala_entities.Value_StringValue{StringValue: assetName}},
// 				"status":     {Type_Union: &kamala_entities.Value_Int32Value{Int32Value: int32(kamala_entities.VersionStatus_UPGRADE_SUCCESS)}},
// 			},
// 		},
// 		OrderBy: []string{"-version"},
// 	}

// 	getVersionResponse, err := versionClient.ListVersions(ctx, &getVersionRequest)

// 	if err != nil {
// 		return []version{}, err
// 	}
// 	virusAllVersions := []*kamala_entities.VersionPacket{}
// 	virusAllVersionsTemp := getVersionResponse.GetVersion()

// 	//对从version表获取的版本进行核对
// 	for _, version := range virusAllVersionsTemp {
// 		if version.AssetName == assetName && version.ChannelId == channelId {
// 			virusAllVersions = append(virusAllVersions, version)
// 		}
// 	}

// 	// 对获取到的版本进行排序
// 	sort.Slice(virusAllVersions, func(i, j int) bool {
// 		aEngineVersion := virusAllVersions[i].Engine
// 		bEngineVersion := virusAllVersions[j].Engine
// 		aPatternVersion := virusAllVersions[i].Pattern
// 		bPatternVersion := virusAllVersions[i].Pattern

// 		var EVUpdate, PVUpdate int
// 		var err error
// 		EVUpdate, err = helper.CompareVersionCore(aEngineVersion, bEngineVersion, engineVersionKind)
// 		if err != nil {
// 			return false
// 		}
// 		PVUpdate, err = helper.CompareVersionCore(aPatternVersion, bPatternVersion, patternVersionKind)
// 		if err != nil {
// 			return false
// 		}

// 		switch {
// 		case EVUpdate == 1 && PVUpdate == 1:
// 			return true
// 		case EVUpdate == 0 && PVUpdate == 1:
// 			return true
// 		case EVUpdate == 1 && PVUpdate == 0:
// 			return true
// 		default:
// 			return false
// 		}

// 	})

// 	//获取前tolerance个版本号
// 	for i := 0; i < int(tolerance) && i < len(virusAllVersions); i++ {
// 		virusVersions = append(virusVersions, version{
// 			engineVersion:  virusAllVersions[i].Engine,
// 			patternVersion: virusAllVersions[i].Pattern,
// 		})
// 	}

// 	return
// }

// 从vloader那下载病毒库,下载失败返回错误
func (s *controller) downloadVirusLibraryFromVloader(engineVersion, patternVersion string) error {
	assetName := s.scannerController.GetScanner().GetAssetName()
	channelId := s.assetController.GetAsset().UpdateFeature.GetVersionDepot().GetChannelId()
	virusLibraryInformation := fmt.Sprintf("AssetName:%v, ChannelId:%v, engineVersion:%v, patternVersion %v", assetName, channelId, engineVersion, patternVersion)

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*100)
	defer cancel()
	virusLibraryClient, err := s.dialer.FetchVloaderClient(ctx, assetName)

	if err != nil {
		log.Errorf("get vloader client err,%v err:%v", virusLibraryInformation, err.Error())
		return err
	}

	getVersionRequest := &kamala_api_vloader.GetVersion_Request{
		AssetName:      assetName,
		ChannelId:      channelId,
		PatternVersion: patternVersion,
		EngineVersion:  engineVersion,
		AllowSecondNew: false,
	}
	//判断vloader是否在下载最新版的病毒库
	response, err := virusLibraryClient.GetVersion(ctx, getVersionRequest)

	if err != nil {
		log.Errorf("get max version, %v,err: %v", virusLibraryInformation, err.Error())
		return fmt.Errorf("get max version, %v,err: %v", virusLibraryInformation, err.Error())
	}

	is_download := response.GetIfDownload()
	//等待vloader那边50s来下载完毕
	waitVloaderDownloadTime := 50
	for i := 0; !is_download; i++ {
		response, err := virusLibraryClient.GetVersion(ctx, getVersionRequest)

		if err != nil {
			log.Errorf("get max version, %v,err: %v", virusLibraryInformation, err.Error())
			return fmt.Errorf("get max version, %v,err: %v", virusLibraryInformation, err.Error())
		}

		//等待vloader下载超时
		if i >= waitVloaderDownloadTime {
			log.Errorf("get max version wait vloader download ok time out: %vs,%v", waitVloaderDownloadTime, virusLibraryInformation)
			return fmt.Errorf("get max version wait vloader download ok time out: %vs, %v", waitVloaderDownloadTime, virusLibraryInformation)
		}

		// log.Infof("wati vloader download, %v", virusLibraryInformation)
		is_download = response.GetIfDownload()
		time.Sleep(1 * time.Second)
	}

	downloadVirusRequest := &kamala_api_vloader.DownloadVirusLibrary_Request{
		AssetName:      assetName,
		ChannelId:      channelId,
		PatternVersion: patternVersion,
		EngineVersion:  engineVersion,
	}

	downloadResponse, err := virusLibraryClient.DownloadVirusLibrary(ctx, downloadVirusRequest)

	if err != nil {
		log.Errorf("get download response, %v, err :%v", virusLibraryInformation, err.Error())
		return fmt.Errorf("get download response, %v, err :%v", virusLibraryInformation, err.Error())
	}

	h := s.helper
	updateDir := h.GetUpdateDir()
	err = checkUpdateDir(updateDir)
	if err != nil {
		return fmt.Errorf("%v:%v", virusLibraryInformation, err.Error())
	}

	VirusLibSuffix := "_backup.zip"
	virusFileName := fmt.Sprintf("%v%v", generateLibName(s.scannerController.GetScanner().GetAssetName()), VirusLibSuffix)
	virusFilePath := path.Join(updateDir, virusFileName)

	virusFile, err := os.Create(virusFilePath)

	if err != nil {
		log.Errorf("create virus file %v, err :%v", virusLibraryInformation, err.Error())

		return fmt.Errorf("create virus %v, file err :%v", virusLibraryInformation, err.Error())
	}
	defer virusFile.Close()

	//将病毒库下载到本地
	for {
		downloadVirusLibraryResponse, err := downloadResponse.Recv()
		if err == io.EOF {
			break
		}

		if err != nil {
			log.Errorf("download virus file %v,err :%v", virusLibraryInformation, err.Error())
			os.RemoveAll(virusFilePath)
			return fmt.Errorf("download virus file %v, err :%v", virusLibraryInformation, err.Error())
		}
		if len(downloadVirusLibraryResponse.GetContent()) <= 0 {
			break
		}

		_, err = io.Copy(virusFile, bytes.NewReader(downloadVirusLibraryResponse.GetContent()))

		if err != nil {
			log.Errorf("download virus file %v,err :%v", virusLibraryInformation, err.Error())
			return fmt.Errorf("download virus file %v,err :%v", virusLibraryInformation, err.Error())
		}

	}
	return nil

}

// 将刚下载好的病毒库持久化储存，并保持缓存最近使用过的2个版本的病毒库, 返回持久化保存后的病毒库目录
func (s *controller) RestoreVirusLibary(engineVersion, patternVersion string, begin time.Time) (string, error) {
	h := s.helper

	updateDuration := time.Since(begin)

	folder := strconv.FormatInt(begin.Unix(), 10)
	info := &helper.Index{
		Folder:         folder,
		UpdateTime:     begin.Unix(),
		EngineVersion:  engineVersion,
		PatternVersion: patternVersion,
		UpdateDuration: updateDuration.Seconds(),
	}

	versionKind := &helper.VersionDefine{
		EngineVersionKind:  helper.VersionKind(s.assetController.GetAsset().GetUpdateFeature().GetVersionDepot().GetEngineDefine().GetKind()),
		PatternVersionKind: helper.VersionKind(s.assetController.GetAsset().GetUpdateFeature().GetVersionDepot().GetPatternDefine().GetKind()),
	}

	dir, err := h.RestoreUpdateDir(info, versionKind)

	return dir, err

}

func checkUpdateDir(updateDir string) error {
	if _, err := os.Stat(updateDir); os.IsNotExist(err) {
		if err := os.MkdirAll(updateDir, 0755); err != nil {
			log.Errorf("Mkdir Latest directory failed error : %v", err.Error())
			return fmt.Errorf("mkdir Latest directory failed error : %v", err.Error())
		}
	}
	return nil
}

func generateLibName(assetName string) string {
	ret := ""
	if assetName == "rowl" || assetName == "rthreat" || assetName == "rthreat-arm64" ||
		assetName == "ras-arm64" || assetName == "rowl-arm64" {
		return "ras"
	}
	if assetName == "mcafee" {
		return "avvdata"
	}

	/*if assetName == "qde2m" || assetName == "qde2-arm64" {
		return "qde2"
	}*/

	if assetName == "qowl-arm64" {
		return "qowl"
	}
	if strings.Contains(assetName, "-") {
		for _, v := range strings.Split(assetName, "-") {
			ret += v + "_"
		}
		ret = strings.TrimSuffix(ret, "_")
	} else {
		ret = assetName
	}
	return ret
}

// 错误分类函数 - 判断错误是否应该重试
// 返回 true 表示应该重试，false 表示不应该重试（但业务要求继续重试）
func (s *controller) isRetryableError(err error) bool {
	if err == nil {
		return false
	}

	// 检查是否是 gRPC 错误
	if st, ok := status.FromError(err); ok {
		code := st.Code()
		switch code {
		case codes.DeadlineExceeded, // 超时
			codes.Unavailable,       // 服务不可用
			codes.ResourceExhausted, // 资源耗尽
			codes.Internal:          // 内部错误
			return true
		case codes.Unauthenticated, // 认证失败
			codes.PermissionDenied, // 权限不足
			codes.InvalidArgument,  // 参数错误
			codes.NotFound:         // 资源不存在
			return false
		default:
			// 其他错误默认重试
			return true
		}
	}

	// 检查是否是网络相关错误
	errStr := strings.ToLower(err.Error())
	networkErrors := []string{
		"connection refused",
		"connection reset",
		"timeout",
		"network is unreachable",
		"no route to host",
		"temporary failure",
		"i/o timeout",
	}

	for _, networkErr := range networkErrors {
		if strings.Contains(errStr, networkErr) {
			return true
		}
	}

	// 默认情况下重试
	return true
}

// 根据重试次数和错误类型选择重试策略
func (s *controller) selectRetryStrategy(retryCount int, err error) RetryStrategy {
	// 如果错误不可重试，但业务要求继续重试，使用降级策略
	if !s.isRetryableError(err) {
		return RetryDegraded
	}

	// 根据重试次数选择策略
	if retryCount < maxNormalRetries {
		return RetryNormal
	} else if retryCount < maxNormalRetries+maxDegradedRetries {
		return RetryDegraded
	} else {
		return RetryPersistent
	}
}

// 计算等待时间，包含指数退避和随机抖动
func (s *controller) calculateWaitTime(retryCount int, strategy RetryStrategy) time.Duration {
	var baseInterval time.Duration
	var jitterPercent int

	switch strategy {
	case RetryNormal:
		// 指数退避：1s -> 2s -> 4s -> 8s -> 16s -> 30s
		baseInterval = baseRetryInterval * time.Duration(math.Pow(2, float64(retryCount)))
		if baseInterval > maxRetryInterval {
			baseInterval = maxRetryInterval
		}
		jitterPercent = normalJitterPercent

	case RetryDegraded:
		// 降级重试：固定30秒间隔
		baseInterval = degradedInterval
		jitterPercent = degradedJitterPercent

	case RetryPersistent:
		// 持久重试：固定60秒间隔
		baseInterval = persistentInterval
		jitterPercent = persistentJitterPercent

	default:
		// 默认使用降级策略
		baseInterval = degradedInterval
		jitterPercent = degradedJitterPercent
	}

	// 添加随机抖动
	if jitterPercent > 0 {
		jitterRange := float64(jitterPercent) / 100.0
		jitter := (rand.Float64() - 0.5) * 2 * jitterRange // -jitterRange 到 +jitterRange
		multiplier := 1.0 + jitter
		baseInterval = time.Duration(float64(baseInterval) * multiplier)
	}

	// 确保最小等待时间为100ms
	if baseInterval < 100*time.Millisecond {
		baseInterval = 100 * time.Millisecond
	}

	return baseInterval
}

// 记录重试尝试的日志
func (s *controller) logRetryAttempt(retryCount int, err error, waitTime time.Duration, strategy RetryStrategy, virusLibraryInformation string) {
	// 根据策略和重试次数决定日志级别和频率
	shouldLog := false
	logLevel := "ERROR"

	switch strategy {
	case RetryNormal:
		// 正常重试：每次都记录
		shouldLog = true
		logLevel = "ERROR"
	case RetryDegraded:
		// 降级重试：每5次记录一次
		shouldLog = (retryCount % 5) == 0
		logLevel = "WARN"
	case RetryPersistent:
		// 持久重试：每10次记录一次
		shouldLog = (retryCount % 10) == 0
		logLevel = "INFO"
	}

	if !shouldLog {
		return
	}

	// 根据日志级别输出
	switch logLevel {
	case "ERROR":
		log.Errorf("Retry attempt %d (strategy: %v) - %v, waiting %v before retry, err: %v",
			retryCount, strategy, virusLibraryInformation, waitTime, err.Error())
	case "WARN":
		log.Warnf("Retry attempt %d (strategy: %v) - %v, waiting %v before retry, err: %v",
			retryCount, strategy, virusLibraryInformation, waitTime, err.Error())
	case "INFO":
		log.Infof("Retry attempt %d (strategy: %v) - %v, waiting %v before retry, err: %v",
			retryCount, strategy, virusLibraryInformation, waitTime, err.Error())
	}
}

// 智能重试获取最新病毒库版本
// 该方法实现了指数退避、错误分类和分阶段重试策略
// 业务要求：任何错误都必须阻塞等待，不能继续往下走
func (s *controller) retryGetNewestVirusLibraryVersion(isAllowTolerance bool, virusLibraryInformation string) (engineVersion, patternVersion string, err error) {
	retryCount := 0

	log.Infof("Starting intelligent retry for getting newest virus library version, %v, isAllowTolerance: %v",
		virusLibraryInformation, isAllowTolerance)

	for {
		// 尝试获取最新版本
		engineVersion, patternVersion, err = s.getNewestVirusLibraryVersion(isAllowTolerance)

		// 成功获取版本，退出重试循环
		if err == nil {
			log.Infof("Successfully got newest virus library version after %d retries, %v, engineVersion: %v, patternVersion: %v",
				retryCount, virusLibraryInformation, engineVersion, patternVersion)
			return engineVersion, patternVersion, nil
		}

		// 选择重试策略
		strategy := s.selectRetryStrategy(retryCount, err)

		// 计算等待时间
		waitTime := s.calculateWaitTime(retryCount, strategy)

		// 记录重试尝试
		s.logRetryAttempt(retryCount, err, waitTime, strategy, virusLibraryInformation)

		// 等待后继续重试
		time.Sleep(waitTime)

		retryCount++

		// 记录重试统计信息（每100次记录一次）
		if retryCount%100 == 0 {
			log.Warnf("Retry count reached %d for getting newest virus library version, %v, current strategy: %v",
				retryCount, virusLibraryInformation, strategy)
		}
	}
}
