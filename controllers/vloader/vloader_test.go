package vloader

import (
	"errors"
	"io"
	"os"
	"path"
	"reflect"
	"testing"
	"time"

	entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	kamala_entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	kamala_api_vloader "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/vloader.git/api"
	mock_controllers "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers/mock"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/helper"
	mock_client "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/mock"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func Test_checkVirusLibraryVersionExisiInVloader(t *testing.T) {

	tests := []struct {
		asset              string
		wantErr            error
		wantPatternVersion string
		wantEngineVersion  string
	}{
		{
			asset:              "ok",
			wantErr:            nil,
			wantPatternVersion: "111",
			wantEngineVersion:  "222",
		},
		{
			asset:              "err",
			wantErr:            errors.New("err"),
			wantPatternVersion: "111",
			wantEngineVersion:  "222",
		},
		{
			asset:              "NotFound",
			wantErr:            status.Error(codes.NotFound, "not found"),
			wantPatternVersion: "111",
			wantEngineVersion:  "222",
		},
	}

	for _, test := range tests {

		ctrl := gomock.NewController(t)
		scannerMock := mock_controllers.NewMockScannerController(ctrl)
		assetMock := mock_controllers.NewMockAssetController(ctrl)
		vloaderMock := mock_client.NewMockVirusLibraryClient(ctrl)
		dialer := mock_client.NewMockDialer(ctrl)
		assetMock.EXPECT().GetAsset().Return(&entities.Asset{
			Name: "test_scanner",
			UpdateFeature: &entities.UpdateFeature{
				VersionDepot: &entities.VersionDepot{
					ChannelId: "1",
				},
			},
		}).AnyTimes()

		dialer.EXPECT().FetchVloaderClient(gomock.Any(), gomock.Any()).Return(
			vloaderMock, nil,
		).AnyTimes()

		scannerMock.EXPECT().GetScanner().DoAndReturn(
			func() *entities.Scanner {
				return &entities.Scanner{
					AssetName: test.asset,
				}
			},
		).AnyTimes()

		vloaderMock.EXPECT().GetVersion(gomock.Any(), gomock.Any()).Return(
			&kamala_api_vloader.GetVersion_Response{}, test.wantErr,
		).AnyTimes()

		s := &controller{
			scannerController: scannerMock,
			assetController:   assetMock,
			dialer:            dialer,
		}

		err := s.checkVirusLibraryVersionExisiInVloader(test.wantEngineVersion, test.wantPatternVersion)
		if test.asset == "err" {
			assert.NotNil(t, err)
		} else if test.asset == "ok" {
			if err != nil {
				t.Error("检查病毒库在vloader出现普通错误，出错")
			}
		} else if test.asset == "NotFound" {
			if code := status.Code(err); code != codes.NotFound {
				t.Error("检查病毒库在vloader病毒库不存在，出错")
			}
		}
	}

}

func Test_downloadVirusLibraryFromVloader(t *testing.T) {

	tests := []struct {
		engineVersion  string
		patternVersion string
		RecErr         error
	}{
		// {
		// 	engineVersion:  "1",
		// 	patternVersion: "1",
		// 	err:            nil,
		// 	wantErr:        nil,
		// },
		{
			engineVersion:  "1",
			patternVersion: "1",
			RecErr:         io.EOF,
		},
		{
			engineVersion:  "1",
			patternVersion: "1",
			RecErr:         errors.New("test"),
		},
	}

	for _, test := range tests {
		ctrl := gomock.NewController(t)
		scannerMock := mock_controllers.NewMockScannerController(ctrl)
		assetMock := mock_controllers.NewMockAssetController(ctrl)
		helperMock := mock_client.NewMockUpdateHelper(ctrl)
		virusLibraryClientMock := mock_client.NewMockVirusLibraryClient(ctrl)
		DownloadVirusLibraryClientMock := mock_client.NewMockVirusLibrary_DownloadVirusLibraryClient(ctrl)
		dialer := mock_client.NewMockDialer(ctrl)

		testUpdateDir := "testUpdate/latest"

		s := &controller{
			scannerController: scannerMock,
			assetController:   assetMock,
			helper:            helperMock,
			dialer:            dialer,
		}

		dialer.EXPECT().FetchVloaderClient(gomock.Any(), gomock.Any()).Return(
			virusLibraryClientMock, nil,
		).AnyTimes()
		scannerMock.EXPECT().GetScanner().Return(
			&entities.Scanner{
				AssetName: "test",
			},
		).AnyTimes()

		assetMock.EXPECT().GetAsset().Return(&entities.Asset{
			Name: "test_scanner",
			UpdateFeature: &entities.UpdateFeature{
				VersionDepot: &entities.VersionDepot{
					ChannelId: "1",
				},
			},
		}).AnyTimes()

		helperMock.EXPECT().GetUpdateDir().Return(
			testUpdateDir,
		).AnyTimes()

		virusLibraryClientMock.EXPECT().GetVersion(gomock.Any(), gomock.Any()).Return(
			&kamala_api_vloader.GetVersion_Response{
				EngineVersion:  test.engineVersion,
				PatternVersion: test.patternVersion,
				IfDownload:     true,
			}, nil,
		).AnyTimes()
		virusLibraryClientMock.EXPECT().DownloadVirusLibrary(gomock.Any(), gomock.Any()).Return(
			DownloadVirusLibraryClientMock, nil,
		).AnyTimes()

		DownloadVirusLibraryClientMock.EXPECT().Recv().Return(
			&kamala_api_vloader.DownloadVirusLibrary_Response{
				Content: []byte(""),
			}, test.RecErr,
		).AnyTimes()

		err := s.downloadVirusLibraryFromVloader(test.engineVersion, test.patternVersion)

		// if _, err := os.Stat(testUpdateDir); os.IsNotExist(err) {
		// 	t.Errorf("create update dir fail %v", err.Error())
		// }
		// if _, err := os.Stat(path.Join(testUpdateDir, "virusLibrary")); os.IsNotExist(err) {
		// 	t.Errorf("download virusLibrary fail %v", err.Error())
		// }

		if test.RecErr != nil && test.RecErr != io.EOF {
			assert.NotNil(t, err)
		} else {
			if err != nil {
				t.Errorf("download err %v", err.Error())
			}
		}
		os.RemoveAll(testUpdateDir)

	}

}

func Test_RestoreVirusLibary(t *testing.T) {

	ctrl := gomock.NewController(t)
	scannerMock := mock_controllers.NewMockScannerController(ctrl)
	assetMock := mock_controllers.NewMockAssetController(ctrl)
	helperMock := mock_client.NewMockUpdateHelper(ctrl)
	s := &controller{
		scannerController: scannerMock,
		assetController:   assetMock,
		helper:            helperMock,
	}
	scannerMock.EXPECT().GetScanner().Return(
		&entities.Scanner{
			AssetName: "test",
		},
	).AnyTimes()

	assetMock.EXPECT().GetAsset().Return(&entities.Asset{
		Name: "test_scanner",
		UpdateFeature: &entities.UpdateFeature{
			VersionDepot: &entities.VersionDepot{
				EngineDefine:  &entities.VersionDefine{Kind: entities.VersionKind_STRING},
				PatternDefine: &entities.VersionDefine{Kind: entities.VersionKind_STRING},
				ChannelId:     "1",
			},
		},
	}).AnyTimes()

	helperMock.EXPECT().RestoreUpdateDir(gomock.Any(), gomock.Any()).Return(
		"1", nil,
	).AnyTimes()
	s.RestoreVirusLibary("1", "1", time.Now())

}
func TestGenerateLibName(t *testing.T) {

	tests := []struct {
		assetNmae string
		want      string
	}{
		{
			assetNmae: "qowl-win",
			want:      "qowl_win",
		},
		{
			assetNmae: "qowl-apk",
			want:      "qowl_apk",
		},
		{
			assetNmae: "qowl-win-apk",
			want:      "qowl_win_apk",
		},
		{
			assetNmae: "qowl-win-apk-beta",
			want:      "qowl_win_apk_beta",
		},
		{
			assetNmae: "qowl",
			want:      "qowl",
		},
		{
			assetNmae: "qowl-beta",
			want:      "qowl_beta",
		},
		{
			assetNmae: "rowl-arm64",
			want:      "ras",
		},
		{
			assetNmae: "mcafee",
			want:      "avvdata",
		},
		{
			assetNmae: "qde2",
			want:      "qde2",
		},
		{
			assetNmae: "qde2m",
			want:      "qde2m",
		},
		{
			assetNmae: "qde2l",
			want:      "qde2l",
		},
		{
			assetNmae: "qowl-arm64",
			want:      "qowl",
		},
	}

	for _, test := range tests {
		res := generateLibName(test.assetNmae)
		assert.Equal(t, test.want, res)
	}

}

func Test_DownloadVirusLibrary(t *testing.T) {
	ctrl := gomock.NewController(t)

	tests := []struct {
		patternVersion string
		engineVersion  string
		dir            string
		getNewestErr   error
		getScanTolErr  error
		getAssignErr   string
		wantErr        error
	}{
		{
			patternVersion: "",
			engineVersion:  "",
			dir:            "testNewestNormal",
		},
		{
			patternVersion: "",
			engineVersion:  "",
			dir:            "testNewestgetNewestErr",
			getNewestErr:   status.Error(codes.NotFound, "not found"),
		},
		{
			patternVersion: "assgin",
			engineVersion:  "assgin",
			dir:            "testAssignNormal",
		},
		{
			patternVersion: "assgin",
			engineVersion:  "assgin",
			dir:            "testAssignErr",
			getAssignErr:   "not found",
			wantErr:        status.Errorf(codes.NotFound, "collector can't found assign version virus library"),
		},

		{
			patternVersion: "assgin",
			engineVersion:  "assgin",
			dir:            "testAssignErr",
			getAssignErr:   "other err",
			wantErr:        status.Errorf(codes.Unknown, "collector get assign version library failed, err:%v", status.Error(codes.Unknown, "other err").Error()),
		},
	}

	for _, test := range tests {
		scannerMock := mock_controllers.NewMockScannerController(ctrl)
		assetMock := mock_controllers.NewMockAssetController(ctrl)
		virusLibraryClientMock := mock_client.NewMockVirusLibraryClient(ctrl)
		dialerMock := mock_client.NewMockDialer(ctrl)
		helperMock := mock_client.NewMockUpdateHelper(ctrl)

		scannerMock.EXPECT().GetScanner().Return(
			&entities.Scanner{
				AssetName: test.dir,
			},
		).AnyTimes()
		assetMock.EXPECT().GetAsset().Return(&entities.Asset{
			Name: test.dir,
			UpdateFeature: &entities.UpdateFeature{
				VersionDepot: &entities.VersionDepot{
					ChannelId: "1",
				},
			},
		}).AnyTimes()

		dialerMock.EXPECT().FetchVloaderClient(gomock.Any(), gomock.Any()).Return(
			virusLibraryClientMock, nil,
		).AnyTimes()

		helperMock.EXPECT().GetDir(test.patternVersion, test.engineVersion).Return(
			test.dir, nil,
		).AnyTimes()
		helperMock.EXPECT().CleanUpdateDir().Return(
			nil,
		).AnyTimes()

		s := &controller{
			scannerController: scannerMock,
			assetController:   assetMock,
			helper:            helperMock,
			dialer:            dialerMock,
		}

		guard := gomonkey.ApplyPrivateMethod(reflect.TypeOf(s), "getNewestVirusLibraryVersion",
			func(s *controller, isallow bool) (engineVersion string, patternVersion string, err error) {
				if isallow == true {
					return "tollerance", "tollerance", nil
				}
				if test.getNewestErr != nil {
					return "", "", status.Error(codes.NotFound, "not found")
				}
				return "", "", nil
			},
		)
		defer guard.Reset()

		guard = gomonkey.ApplyPrivateMethod(reflect.TypeOf(s), "isAllowTolerance",
			func(s *controller) bool {
				return true
			},
		)
		defer guard.Reset()

		guard = gomonkey.ApplyPrivateMethod(reflect.TypeOf(s), "getAssignVersionLibrary",
			func(s *controller, engineVersion, patternVersion string) (string, error) {
				if test.getAssignErr == "not found" {
					return "", status.Error(codes.NotFound, "not found")
				}

				if test.getAssignErr == "other err" {
					return "", status.Error(codes.Unknown, "other err")
				}
				return test.dir, nil
			},
		)
		defer guard.Reset()

		res, _, _, err := s.DownloadVirusLibrary(test.engineVersion, test.patternVersion)

		if test.wantErr == nil {
			assert.Equal(t, test.dir, res)
		} else {
			assert.EqualError(t, err, test.wantErr.Error())
		}
	}
}

func Test_DownloadVirusLibrary_v(t *testing.T) {
	ctrl := gomock.NewController(t)

	tests := []struct {
		patternVersion string
		engineVersion  string
		dir            string
		getNewestErr   error
		getScanTolErr  error
		getAssignErr   string
		wantErr        error
	}{
		{
			patternVersion: "",
			engineVersion:  "",
			dir:            "testNewestNormal",
		},
	}

	for _, test := range tests {
		scannerMock := mock_controllers.NewMockScannerController(ctrl)
		assetMock := mock_controllers.NewMockAssetController(ctrl)
		virusLibraryClientMock := mock_client.NewMockVirusLibraryClient(ctrl)
		dialerMock := mock_client.NewMockDialer(ctrl)
		helperMock := mock_client.NewMockUpdateHelper(ctrl)

		scannerMock.EXPECT().GetScanner().Return(
			&entities.Scanner{
				AssetName: test.dir,
			},
		).AnyTimes()
		assetMock.EXPECT().GetAsset().Return(&entities.Asset{
			Name: test.dir,
			UpdateFeature: &entities.UpdateFeature{
				VersionDepot: &entities.VersionDepot{
					ChannelId: "1",
				},
			},
		}).AnyTimes()

		dialerMock.EXPECT().FetchVloaderClient(gomock.Any(), gomock.Any()).Return(
			virusLibraryClientMock, nil,
		).AnyTimes()

		helperMock.EXPECT().GetDir(test.patternVersion, test.engineVersion).Return(
			test.dir, nil,
		).AnyTimes()
		helperMock.EXPECT().CleanUpdateDir().Return(
			nil,
		).AnyTimes()

		s := &controller{
			scannerController: scannerMock,
			assetController:   assetMock,
			helper:            helperMock,
			dialer:            dialerMock,
		}

		guard := gomonkey.ApplyPrivateMethod(reflect.TypeOf(s), "getNewestVirusLibraryVersion",
			func(s *controller, isallow bool) (engineVersion string, patternVersion string, err error) {
				if isallow == true {
					return "tollerance", "tollerance", nil
				}
				if test.getNewestErr != nil {
					return "", "", status.Error(codes.NotFound, "not found")
				}
				return "test1", "test2", nil
			},
		)
		defer guard.Reset()

		guard = gomonkey.ApplyPrivateMethod(reflect.TypeOf(s), "isAllowTolerance",
			func(s *controller) bool {
				return false
			},
		)
		defer guard.Reset()

		guard = gomonkey.ApplyPrivateMethod(reflect.TypeOf(s), "getAssignVersionLibrary",
			func(s *controller, engineVersion, patternVersion string) (string, error) {
				if test.getAssignErr == "not found" {
					return "", status.Error(codes.NotFound, "not found")
				}

				if test.getAssignErr == "other err" {
					return "", status.Error(codes.Unknown, "other err")
				}
				return test.dir, nil
			},
		)
		defer guard.Reset()

		res, engine, pattern, err := s.DownloadVirusLibrary(test.engineVersion, test.patternVersion)

		if test.wantErr == nil {
			assert.Equal(t, test.dir, res)
			assert.Equal(t, "test1", engine)
			assert.Equal(t, "test2", pattern)
		} else {
			assert.EqualError(t, err, test.wantErr.Error())
		}
	}
}

func Test_getAssignVersionLibrary(t *testing.T) {

	tests := []struct {
		dir             string
		checkLocalErr   error
		checkVloaderErr error
		downloadErr     error
		restoreErr      error
		wantErr         error
	}{
		{
			dir: "test",
		},
		{
			dir:           "test",
			checkLocalErr: errors.New("checkLocalErr"),
		},

		{
			dir:             "test",
			checkLocalErr:   errors.New("checkLocalErr"),
			checkVloaderErr: errors.New("checkVloaderErr"),
			wantErr:         errors.New("checkVloaderErr"),
		},
		{
			dir:           "test",
			checkLocalErr: errors.New("checkLocalErr"),
			downloadErr:   errors.New("downloadErr"),
			wantErr:       errors.New("downloadErr"),
		},
		{
			dir:           "test",
			checkLocalErr: errors.New("checkLocalErr"),
			restoreErr:    errors.New("restoreErr"),
			wantErr:       errors.New("restoreErr"),
		},
	}

	for _, test := range tests {
		ctrl := gomock.NewController(t)

		scannerMock := mock_controllers.NewMockScannerController(ctrl)
		assetMock := mock_controllers.NewMockAssetController(ctrl)

		dialerMock := mock_client.NewMockDialer(ctrl)
		helperMock := mock_client.NewMockUpdateHelper(ctrl)

		scannerMock.EXPECT().GetScanner().Return(
			&entities.Scanner{
				AssetName: test.dir,
			},
		).AnyTimes()
		assetMock.EXPECT().GetAsset().Return(&entities.Asset{
			Name: test.dir,
			UpdateFeature: &entities.UpdateFeature{
				VersionDepot: &entities.VersionDepot{
					ChannelId: "1",
				},
			},
		}).AnyTimes()
		helperMock.EXPECT().CleanUpdateDir().Return(
			nil,
		).AnyTimes()

		s := &controller{
			scannerController: scannerMock,
			assetController:   assetMock,
			helper:            helperMock,
			dialer:            dialerMock,
		}

		guard := gomonkey.ApplyPrivateMethod(reflect.TypeOf(s), "checkVirusLibraryVersionExisiInLocal",
			func(s *controller, engineVersion string, patternVersion string) (string, error) {
				if test.checkLocalErr != nil {
					return test.dir, errors.New("checkLocalErr")
				}
				return test.dir, nil
			},
		)
		defer guard.Reset()

		guard = gomonkey.ApplyPrivateMethod(reflect.TypeOf(s), "checkVirusLibraryVersionExisiInVloader",
			func(s *controller, engineVersion string, patternVersion string) error {
				if test.checkVloaderErr != nil {
					return errors.New("checkVloaderErr")
				}
				return nil
			},
		)
		defer guard.Reset()

		guard = gomonkey.ApplyPrivateMethod(reflect.TypeOf(s), "downloadVirusLibraryFromVloader",
			func(s *controller, engineVersion string, patternVersion string) error {
				if test.downloadErr != nil {
					return errors.New("downloadErr")
				}
				return nil
			},
		)
		defer guard.Reset()

		guard = gomonkey.ApplyMethod(reflect.TypeOf(s), "RestoreVirusLibary",
			func(s *controller, engineVersion string, patternVersion string, begin time.Time) (string, error) {
				if test.restoreErr != nil {
					return "", errors.New("restoreErr")
				}
				return test.dir, nil
			},
		)
		defer guard.Reset()

		res, err := s.getAssignVersionLibrary("test", "test")
		if test.wantErr == nil {
			assert.Equal(t, test.dir, res)

		} else {
			assert.EqualError(t, err, test.wantErr.Error())
		}

	}

}

func Test_getNewestVirusLibraryVersion(t *testing.T) {

	tests := []struct {
		assetName       string
		versionResponse *kamala_api_vloader.GetVersion_Response
		engineVersion   string
		patternVerson   string
		fetchClientErr  error
		getVerErr       error
		wantErr         error
	}{
		//正常
		{
			assetName: "normal",
			versionResponse: &kamala_api_vloader.GetVersion_Response{
				EngineVersion:  "normal",
				PatternVersion: "normal",
			},
			engineVersion: "normal",
			patternVerson: "normal",
		},
		{
			assetName:      "fetchClientErr",
			fetchClientErr: errors.New("fetchClientErr"),
			wantErr:        errors.New("fetchClientErr"),
		},
		{
			assetName: "getVerErr",

			getVerErr: errors.New("getVerErr"),
			wantErr:   errors.New("getVerErr"),
		},

		{
			assetName: "responseNil",
			wantErr:   status.Error(codes.NotFound, "not found newest version AssetName:responseNil, ChannelId:test"),
		},
		{
			assetName: "versionNil",
			versionResponse: &kamala_api_vloader.GetVersion_Response{
				EngineVersion:  "",
				PatternVersion: "",
			},
			engineVersion: "",
			patternVerson: "",
			wantErr:       status.Error(codes.NotFound, "not found newest version AssetName:versionNil, ChannelId:test"),
		},
	}

	for _, test := range tests {
		ctrl := gomock.NewController(t)

		scannerMock := mock_controllers.NewMockScannerController(ctrl)
		assetMock := mock_controllers.NewMockAssetController(ctrl)
		virusLibraryClientMock := mock_client.NewMockVirusLibraryClient(ctrl)
		dialerMock := mock_client.NewMockDialer(ctrl)
		helperMock := mock_client.NewMockUpdateHelper(ctrl)

		scannerMock.EXPECT().GetScanner().Return(
			&entities.Scanner{
				AssetName: test.assetName,
			},
		).AnyTimes()
		assetMock.EXPECT().GetAsset().Return(&entities.Asset{
			Name: test.assetName,
			UpdateFeature: &entities.UpdateFeature{
				VersionDepot: &entities.VersionDepot{
					ChannelId: "test",
				},
			},
		}).AnyTimes()

		dialerMock.EXPECT().FetchVloaderClient(gomock.Any(), gomock.Any()).Return(
			virusLibraryClientMock, test.fetchClientErr,
		).AnyTimes()

		helperMock.EXPECT().CleanUpdateDir().Return(
			nil,
		).AnyTimes()

		virusLibraryClientMock.EXPECT().GetVersion(gomock.Any(), gomock.Any()).Return(
			test.versionResponse, test.getVerErr,
		).AnyTimes()

		s := &controller{
			scannerController: scannerMock,
			assetController:   assetMock,
			helper:            helperMock,
			dialer:            dialerMock,
		}

		resEngine, resPattern, err := s.getNewestVirusLibraryVersion(false)

		if test.wantErr == nil {
			assert.Equal(t, test.engineVersion, resEngine)
			assert.Equal(t, test.patternVerson, resPattern)
			if err != nil {
				t.Errorf("获取最新版失败")
			}
		} else {
			assert.EqualError(t, err, test.wantErr.Error())
		}
	}

}

// func Test_getVersionByScanTolerance(t *testing.T) {
// 	tests := []struct {
// 		engineScanTolerance  int32
// 		patternScanTolerance int32
// 		versions             []version
// 		err                  error
// 	}{
// 		{
// 			engineScanTolerance:  3,
// 			patternScanTolerance: 2,
// 			versions: []version{
// 				{
// 					engineVersion:  "0.0.3",
// 					patternVersion: "0.0.3",
// 				},
// 				{
// 					engineVersion:  "0.0.2",
// 					patternVersion: "0.0.2",
// 				},
// 			},
// 		},
// 	}

// 	for _, test := range tests {
// 		ctrl := gomock.NewController(t)

// 		scannerMock := mock_controllers.NewMockScannerController(ctrl)
// 		assetMock := mock_controllers.NewMockAssetController(ctrl)
// 		versionMock := mock_client.NewMockVersionClient(ctrl)
// 		dialerMock := mock_client.NewMockDialer(ctrl)
// 		helperMock := mock_client.NewMockUpdateHelper(ctrl)

// 		scannerMock.EXPECT().GetScanner().Return(
// 			&entities.Scanner{
// 				AssetName: "test",
// 			},
// 		).AnyTimes()
// 		assetMock.EXPECT().GetAsset().Return(&entities.Asset{
// 			Name: "test",
// 			UpdateFeature: &entities.UpdateFeature{
// 				VersionDepot: &entities.VersionDepot{
// 					EngineDefine: &kamala_entities.VersionDefine{
// 						Kind:          kamala_entities.VersionKind_SEMVER,
// 						ScanTolerance: test.engineScanTolerance,
// 					},

// 					PatternDefine: &kamala_entities.VersionDefine{
// 						Kind:          kamala_entities.VersionKind_SEMVER,
// 						ScanTolerance: test.patternScanTolerance,
// 					},

// 					ChannelId: "test",
// 				},
// 			},
// 		}).AnyTimes()

// 		dialerMock.EXPECT().FetchVersionClient(gomock.Any()).Return(
// 			versionMock, nil,
// 		).AnyTimes()

// 		helperMock.EXPECT().CleanUpdateDir().Return(
// 			nil,
// 		).AnyTimes()

// 		versionMock.EXPECT().ListVersions(gomock.Any(), gomock.Any()).Return(
// 			&kamala_api_model.ListVersions_Response{
// 				Version: []*kamala_entities.VersionPacket{
// 					{
// 						AssetName: "test",
// 						ChannelId: "test",
// 						Pattern:   "0.0.1",
// 						Engine:    "0.0.1",
// 					},
// 					{
// 						AssetName: "test",
// 						ChannelId: "test",
// 						Pattern:   "0.0.3",
// 						Engine:    "0.0.3",
// 					},
// 					{
// 						AssetName: "test",
// 						ChannelId: "test",
// 						Pattern:   "0.0.2",
// 						Engine:    "0.0.2",
// 					},
// 				},
// 			}, test.err,
// 		).AnyTimes()

// 		s := &controller{
// 			scannerController: scannerMock,
// 			assetController:   assetMock,
// 			helper:            helperMock,
// 			dialer:            dialerMock,
// 		}

// 		res, err := s.getVersionByScanTolerance()

// 		if test.err != nil {
// 			assert.EqualError(t, err, test.err.Error())
// 		} else {
// 			assert.Equal(t, res, test.versions)
// 		}

// 	}
// }

func Test_isAllowTolerance(t *testing.T) {
	tests := []struct {
		eTolerance int32
		pTolerance int32
		want       bool
	}{
		{
			eTolerance: 0,
			pTolerance: 1,
			want:       false,
		},
		{
			eTolerance: 1,
			pTolerance: 0,
			want:       false,
		},
		{
			eTolerance: 1,
			pTolerance: 1,
			want:       true,
		},
	}

	for _, test := range tests {

		ctrl := gomock.NewController(t)

		assetMock := mock_controllers.NewMockAssetController(ctrl)
		assetMock.EXPECT().GetAsset().Return(&entities.Asset{
			Name: "test",
			UpdateFeature: &entities.UpdateFeature{
				VersionDepot: &entities.VersionDepot{
					EngineDefine: &kamala_entities.VersionDefine{
						ScanTolerance: test.eTolerance,
					},

					PatternDefine: &kamala_entities.VersionDefine{
						ScanTolerance: test.pTolerance,
					},
				},
			},
		}).AnyTimes()

		s := &controller{
			assetController: assetMock,
		}

		res := s.isAllowTolerance()

		assert.Equal(t, test.want, res)
	}
}

func Test_checkVirusLibraryVersionExisiInLocal(t *testing.T) {
	tem := "tem"
	os.MkdirAll(path.Join(tem, helper.LatestPath), os.ModePerm)
	defer os.RemoveAll(tem)
	h := helper.NewUpdateHelper(tem)

	infos := []helper.Index{

		{
			Folder:         "1612701322",
			UpdateTime:     1612701322,
			EngineVersion:  "20210207203116",
			PatternVersion: "20210207203116",
			UpdateDuration: 20.495990722,
		},
		{
			Folder:         "1622701322",
			UpdateTime:     1622701322,
			EngineVersion:  "20210507203116",
			PatternVersion: "20210507203116",
			UpdateDuration: 20.495990722,
		},
	}

	for _, info := range infos {
		h.RestoreUpdateDir(&info, &helper.VersionDefine{
			EngineVersionKind:  helper.VersionKindSTRING,
			PatternVersionKind: helper.VersionKindSTRING,
		})
	}

	c := controller{
		helper: h,
	}

	tests := []struct {
		EngineVersion  string
		PatternVersion string
		want           string
	}{
		{
			EngineVersion:  "",
			PatternVersion: "",
			want:           "",
		},
		{
			EngineVersion:  "20210507203116",
			PatternVersion: "20210507203116",
			want:           "1622701322",
		},
	}

	for _, test := range tests {

		res, _ := c.checkVirusLibraryVersionExisiInLocal(test.EngineVersion, test.PatternVersion)

		assert.Equal(t, test.want, res)

	}

}

//测试其他服务接口用
// func Test_test(t *testing.T) {
// 	defer framework.Init()()

// 	name := "10.252.12.197:31056"
// 	_ = dialer.RegisterDialOption("DirectFetchVersion", dialer.DialWithRegistry(nil))

// 	conn := dialer.CreateClientConnection(dialer.WithTarget(name))

// 	versionClient := kamala_api_model.NewVersionClient(conn)

// 	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
// 	defer cancel()

// 	getVersionRequest := kamala_api_model.ListVersions_Request{
// 		PageSize: 10,
// 		Query: &kamala_entities.Value{
// 			MapValue: map[string]*kamala_entities.Value{
// 				"asset_name": {Type_Union: &kamala_entities.Value_StringValue{StringValue: "bd"}},
// 				"status":     {Type_Union: &kamala_entities.Value_Int32Value{Int32Value: int32(kamala_entities.VersionStatus_UPGRADE_SUCCESS)}},
// 			},
// 		},
// 		OrderBy: []string{"-version"},
// 	}

// 	getVersionResponse, err := versionClient.ListVersions(ctx, &getVersionRequest)

// 	if err != nil {
// 		fmt.Println(err.Error())
// 	}

// 	virusAllVersionsTemp := getVersionResponse.GetVersion()

// 	fmt.Println(virusAllVersionsTemp)

// }

// func Test_test(t *testing.T) {
// 	defer framework.Init()()

// 	addr := "k8s01.ops.zzyc.xxxes.cn:30800"

// 	_ = dialer.RegisterDialOption("DirectFetchVloader", dialer.DialWithRegistry(nil))
// 	conn := dialer.CreateClientConnection(dialer.WithTarget(addr), dialer.WithDialOptTag("DirectFetchVloader"))

// 	cli := kamala_api_vloader.NewVirusLibraryClient(conn)

// 	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
// 	defer cancel()

// 	getVersionRequest := &kamala_api_vloader.GetVersion_Request{
// 		AssetName:      "bd",
// 		ChannelId:      "kamala_bd",
// 		PatternVersion: "",
// 		EngineVersion:  "",
// 		AllowSecondNew: false,
// 	}

// 	getVersionResponse, err := cli.GetVersion(ctx, getVersionRequest)

// 	fmt.Println(getVersionResponse)
// 	fmt.Println(err.Error())

// }
