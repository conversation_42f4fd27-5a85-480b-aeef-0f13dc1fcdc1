package scanner

import (
	"context"
	"fmt"
	"testing"
	"time"

	entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	api_scan "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git"
	scan "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git"
	api "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git/api"
	scan_api "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git/api"
	scanadapter "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/adapters/scan"
	mock_controllers "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers/mock"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/mock"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"
	sm "github.com/cch123/supermonkey"
	"github.com/golang/protobuf/ptypes/empty"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func Test_controller_Ping(t *testing.T) {
	tests := []struct {
		name                      string
		isFetchScannerClientError bool
		isScannerClientPingError  bool
		wantName                  string
		wantErr                   bool
	}{
		{
			name:                      "fetchScannerClient error",
			isFetchScannerClientError: true,
			isScannerClientPingError:  false,
			wantErr:                   true,
		},
		{
			name:                      "scannerClientPing error",
			isFetchScannerClientError: false,
			isScannerClientPingError:  true,
			wantErr:                   true,
		},
		{
			name:                      "succeed",
			isFetchScannerClientError: false,
			isScannerClientPingError:  false,
			wantName:                  "bd",
			wantErr:                   false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			dialer := mock.NewMockDialer(ctrl)
			scannerClient := mock.NewMockScanApiClient(ctrl)
			c := &controller{
				dialer: dialer,
			}
			if tt.isFetchScannerClientError {
				dialer.EXPECT().FetchScannerClient(gomock.Any()).Return(nil, fmt.Errorf("error")).Times(1)
			} else {
				dialer.EXPECT().FetchScannerClient(gomock.Any()).
					DoAndReturn(func(ctx context.Context) (scan_api.ScanApiClient, error) {
						return scannerClient, nil
					}).Times(1)
				if tt.isScannerClientPingError {
					scannerClient.EXPECT().Ping(gomock.Any(), &empty.Empty{}).Return(&scan_api.Ping_Response{}, fmt.Errorf("error")).Times(1)
				} else {
					scannerClient.EXPECT().Ping(gomock.Any(), &empty.Empty{}).Return(&scan_api.Ping_Response{
						Name:    "bd",
						Version: &scan.Version{},
					}, nil).Times(1)
				}
			}
			got, err := c.Ping()
			if tt.wantErr {
				assert.Error(t, err)
				return
			}
			assert.Equal(t, tt.wantName, got.Name)
		})
	}
}

func Test_controller_ping(t *testing.T) {
	t.Setenv("HOSTNAME", "docker_aa")
	tests := []struct {
		name                      string
		isFetchScannerClientError bool
		isScannerClientPingError  bool
		wantName                  string
		wantAssetName             string
	}{
		// TODO: Add test cases.
		{
			name:                      "fetchScannerClient error",
			isFetchScannerClientError: true,
			isScannerClientPingError:  false,
			wantName:                  "",
			wantAssetName:             "",
		},
		{
			name:                      "scannerClientPing error",
			isFetchScannerClientError: false,
			isScannerClientPingError:  true,
			wantName:                  "",
			wantAssetName:             "",
		},
		{
			name:                      "succeed",
			isFetchScannerClientError: false,
			isScannerClientPingError:  false,
			wantName:                  "docker_aa",
			wantAssetName:             "bd",
		},
	}
	for i, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			dialer := mock.NewMockDialer(ctrl)
			scannerClient := mock.NewMockScanApiClient(ctrl)
			c := &controller{
				dialer:  dialer,
				scanner: &entities.Scanner{},
			}
			if tt.isFetchScannerClientError {
				dialer.EXPECT().FetchScannerClient(gomock.Any()).Return(nil, fmt.Errorf("error")).Times(1)
			} else {
				dialer.EXPECT().FetchScannerClient(gomock.Any()).
					DoAndReturn(func(ctx context.Context) (scan_api.ScanApiClient, error) {
						return scannerClient, nil
					}).Times(1)
				if tt.isScannerClientPingError {
					scannerClient.EXPECT().Ping(gomock.Any(), &empty.Empty{}).Return(&scan_api.Ping_Response{}, fmt.Errorf("error")).Times(1)
				} else {
					scannerClient.EXPECT().Ping(gomock.Any(), &empty.Empty{}).Return(&scan_api.Ping_Response{
						AssetName: "bd",
						Version:   &scan.Version{},
					}, nil).Times(1)
				}
			}
			c.ping()
			t.Logf("offset: %d", i)
			assert.Equal(t, tt.wantName, c.scanner.Name)
			assert.Equal(t, tt.wantAssetName, c.scanner.AssetName)
		})
	}
}

func Test_controller_loopPing(t *testing.T) {
	c := &controller{}
	pg := sm.Patch((*controller).ping, func(c *controller) {
		t.Log("ping scanner")
		c.Stop = true
	})
	defer pg.Unpatch()

	c.loopPing()
}

func Test_controller_Exit(t *testing.T) {
	type fields struct{}
	tests := []struct {
		name                      string
		isFetchScannerClientError bool
		isScannerClientExistError bool
		wantError                 bool
	}{
		// TODO: Add test cases.
		{
			name:                      "fetchScannerClient error",
			isFetchScannerClientError: true,
			isScannerClientExistError: false,
			wantError:                 true,
		},
		{
			name:                      "scannerClientPing error",
			isFetchScannerClientError: false,
			isScannerClientExistError: true,
			wantError:                 true,
		},
		{
			name:                      "succeed",
			isFetchScannerClientError: false,
			isScannerClientExistError: false,
			wantError:                 false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			dialer := mock.NewMockDialer(ctrl)
			scannerClient := mock.NewMockScanApiClient(ctrl)
			c := &controller{
				dialer: dialer,
			}
			if tt.isFetchScannerClientError {
				dialer.EXPECT().FetchScannerClient(gomock.Any()).Return(nil, fmt.Errorf("error")).Times(1)
			} else {
				dialer.EXPECT().FetchScannerClient(gomock.Any()).
					DoAndReturn(func(ctx context.Context) (scan_api.ScanApiClient, error) {
						return scannerClient, nil
					}).Times(1)
				if tt.isScannerClientExistError {
					scannerClient.EXPECT().Exit(gomock.Any(), &empty.Empty{}).Return(&empty.Empty{}, fmt.Errorf("error")).Times(1)
				} else {
					scannerClient.EXPECT().Exit(gomock.Any(), &empty.Empty{}).Return(&empty.Empty{}, nil).Times(1)
				}
			}
			err := c.Exit()
			if tt.wantError {
				assert.Error(t, err)
				return
			}
		})
	}
}

func Test_controller_Reload(t *testing.T) {
	type fields struct{}
	tests := []struct {
		name                      string
		isFetchScannerClientError bool
		isScannerClientExistError bool
		wantError                 bool
	}{
		// TODO: Add test cases.
		{
			name:                      "fetchScannerClient error",
			isFetchScannerClientError: true,
			isScannerClientExistError: false,
			wantError:                 true,
		},
		{
			name:                      "scannerClientReload error",
			isFetchScannerClientError: false,
			isScannerClientExistError: true,
			wantError:                 true,
		},
		{
			name:                      "succeed",
			isFetchScannerClientError: false,
			isScannerClientExistError: false,
			wantError:                 false,
		},
	}
	for _, tt := range tests {
		pg := sm.Patch((*controller).ping, func(c *controller) {
			t.Log("ping scanner")
		})
		defer pg.Unpatch()

		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			dialer := mock.NewMockDialer(ctrl)
			scannerClient := mock.NewMockScanApiClient(ctrl)
			c := &controller{
				dialer: dialer,
			}
			if tt.isFetchScannerClientError {
				dialer.EXPECT().FetchScannerClient(gomock.Any()).Return(nil, fmt.Errorf("error")).Times(1)
			} else {
				dialer.EXPECT().FetchScannerClient(gomock.Any()).
					DoAndReturn(func(ctx context.Context) (scan_api.ScanApiClient, error) {
						return scannerClient, nil
					}).AnyTimes()
			}
			if tt.isScannerClientExistError {
				scannerClient.EXPECT().Reload(gomock.Any(), gomock.Any()).Return(&scan.Version{}, fmt.Errorf("error")).Times(1)
			} else {
				scannerClient.EXPECT().Reload(gomock.Any(), gomock.Any()).Return(&scan.Version{
					EngineVersion:  "0.0.1",
					PatternVersion: "0.0.1",
				}, nil).AnyTimes()
			}
			version, err := c.Reload("")
			if tt.wantError {
				assert.Error(t, err)
				return
			}
			t.Log(version)
		})
	}
}

func Test_controller_Update(t *testing.T) {
	type fields struct{}
	tests := []struct {
		name                      string
		isFetchScannerClientError bool
		isScannerClientExistError bool
		wantError                 bool
	}{
		// TODO: Add test cases.
		{
			name:                      "fetchScannerClient error",
			isFetchScannerClientError: true,
			isScannerClientExistError: false,
			wantError:                 true,
		},
		{
			name:                      "scannerClientReload error",
			isFetchScannerClientError: false,
			isScannerClientExistError: true,
			wantError:                 true,
		},
		{
			name:                      "succeed",
			isFetchScannerClientError: false,
			isScannerClientExistError: false,
			wantError:                 false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			dialer := mock.NewMockDialer(ctrl)
			scannerClient := mock.NewMockScanApiClient(ctrl)
			c := &controller{
				dialer: dialer,
				scanner: &entities.Scanner{
					Version: &entities.Version{},
				},
			}
			if tt.isFetchScannerClientError {
				dialer.EXPECT().FetchScannerClient(gomock.Any()).Return(nil, fmt.Errorf("error")).Times(1)
			} else {
				dialer.EXPECT().FetchScannerClient(gomock.Any()).
					DoAndReturn(func(ctx context.Context) (scan_api.ScanApiClient, error) {
						return scannerClient, nil
					}).Times(1)
				if tt.isScannerClientExistError {
					scannerClient.EXPECT().Update(gomock.Any(), gomock.Any()).Return(&scan.Version{}, fmt.Errorf("error")).Times(1)
				} else {
					scannerClient.EXPECT().Update(gomock.Any(), gomock.Any()).Return(&scan.Version{
						EngineVersion:  "0.0.1",
						PatternVersion: "0.0.1",
					}, nil).Times(1)
				}
			}
			version, err := c.Update()
			if tt.wantError {
				assert.Error(t, err)
				return
			}
			t.Log(version)
		})
	}
}

func Test_controller_Scan(t *testing.T) {
	type args struct {
		ctx           context.Context
		scanOperation []scanadapter.ScanOperation
	}
	tests := []struct {
		name                      string
		args                      args
		isFetchScannerClientError bool
		isScannerClientScanError  bool
		wantErr                   bool
		want                      *util.ScanResult
	}{
		{
			name:                      "",
			args:                      args{},
			isFetchScannerClientError: true,
			isScannerClientScanError:  false,
			wantErr:                   true,
			want:                      &util.ScanResult{},
		},
		{
			name:                      "",
			args:                      args{},
			isFetchScannerClientError: false,
			isScannerClientScanError:  true,
			wantErr:                   true,
			want:                      &util.ScanResult{},
		},
		{
			name:                      "",
			args:                      args{},
			isFetchScannerClientError: false,
			isScannerClientScanError:  false,
			wantErr:                   false,
			want:                      &util.ScanResult{},
		},
	}

	for _, tt := range tests {
		guard := sm.Patch(CheckScanCode, func(scanResult map[string]*scan.ScanResult) bool {
			return false
		})
		defer guard.Unpatch()

		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			dialer := mock.NewMockDialer(ctrl)
			scannerClient := mock.NewMockScanApiClient(ctrl)
			assetController := mock_controllers.NewMockAssetController(ctrl)
			c := &controller{
				dialer: dialer,
				scanner: &entities.Scanner{
					Version: &entities.Version{},
				},
				assetController: assetController,
			}
			assetController.EXPECT().GetAsset().Return(&entities.Asset{
				Name: "bd",
				ScanFeature: &entities.ScanFeature{
					MaxScanTimeout: int64(200 * time.Second),
				},
			}).AnyTimes()
			if tt.isFetchScannerClientError {
				dialer.EXPECT().FetchScannerClient(gomock.Any()).Return(nil, fmt.Errorf("error")).Times(1)
			} else {
				dialer.EXPECT().FetchScannerClient(gomock.Any()).
					DoAndReturn(func(ctx context.Context) (scan_api.ScanApiClient, error) {
						return scannerClient, nil
					}).AnyTimes()
			}
			if tt.isScannerClientScanError {
				scannerClient.EXPECT().Scan(gomock.Any(), gomock.Any()).Return(&scan_api.Scan_Response{}, fmt.Errorf("error")).AnyTimes()
			} else {
				scannerClient.EXPECT().Scan(gomock.Any(), gomock.Any()).Return(&scan_api.Scan_Response{
					Results: make(map[string]*scan.ScanResult),
				}, nil).AnyTimes()
			}
			r, err := c.Scan(tt.args.ctx, tt.args.scanOperation)
			if tt.wantErr {
				assert.Error(t, err)
			}
			t.Log(r)
		})
	}
}

func Test_SetState(t *testing.T) {
	ctrl := gomock.NewController(t)
	mock_scannerClient := mock.NewMockScanApiClient(ctrl)
	mock_scannerClient.EXPECT().Update(gomock.Any(), gomock.Any()).Return(
		&api_scan.Version{
			EngineVersion: "engine",
		}, nil,
	).AnyTimes()
	mock_dialer := mock.NewMockDialer(ctrl)
	mock_dialer.EXPECT().FetchScannerClient(gomock.Any()).Return(
		mock_scannerClient, nil,
	).AnyTimes()
	mock_scannerClient.EXPECT().Ping(gomock.Any(), gomock.Any()).Return(
		&api.Ping_Response{
			State: api_scan.State_IDLE,
		}, fmt.Errorf("test"),
	).AnyTimes()

	c := NewController(mock_dialer)

	c.SetState(int32(api_scan.State_RELOADING))
	res := c.GetState()

	assert.Equal(t, int32(api_scan.State_RELOADING), res)
}

func Test_CheckScanCode(t *testing.T) {
	t.Run("empty", func(t *testing.T) {
		scanResult := make(map[string]*api_scan.ScanResult)
		res := CheckScanCode(scanResult)
		assert.Equal(t, true, res)
	})

	t.Run("all feature", func(t *testing.T) {
		scanResult := make(map[string]*api_scan.ScanResult)
		scanResult["test"] = &api_scan.ScanResult{
			Code: scan.ScanCode(entities.ScanCode_SCAN_FAILURE),
		}
		res := CheckScanCode(scanResult)
		assert.Equal(t, true, res)
	})

	t.Run("success", func(t *testing.T) {
		scanResult := make(map[string]*api_scan.ScanResult)
		scanResult["test"] = &api_scan.ScanResult{
			Code: api_scan.ScanCode_OK,
		}
		res := CheckScanCode(scanResult)
		assert.Equal(t, false, res)
	})
}
