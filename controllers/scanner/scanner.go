package scanner

import (
	"context"
	"fmt"
	"os"
	"sync"
	"sync/atomic"
	"time"

	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers"
	"github.com/Rican7/retry"
	"github.com/Rican7/retry/strategy"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	api_scan "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git"
	scanadapter "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/adapters/scan"

	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"
)

type controller struct {
	util.LoopController
	scanner      *entities.Scanner
	scannerMutex sync.RWMutex
	lastPing     time.Time
	lastUpdate   time.Time
	updateState  int32
	scanService  scanadapter.ScanService

	assetController controllers.AssetController
	dialer          util.Dialer
}

type ScannTask struct {
}

func NewController(dialer util.Dialer) controllers.ScannerController {
	c := &controller{dialer: dialer}

	c.Run(c.loopPing)
	c.Run(c.InitUpdate)

	return c
}

func (c *controller) InitUpdate() {
	for c.assetController == nil || c.assetController.GetAsset() == nil {
		time.Sleep(time.Millisecond)
	}
	if util.IsSelfUpdate(c.assetController.GetAsset()) {
		c.Run(c.loopUpdate)
	}
}

func (c *controller) SetAssetController(ac controllers.AssetController) {
	c.assetController = ac
}

func (c *controller) GetScanner() *entities.Scanner {
	return c.scanner
}

func (c *controller) GetState() int32 {
	return c.updateState
}

func (c *controller) Ping() (*scanadapter.PingResponse, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()
	logger := log.WithContext(ctx)
	scanClient, err := c.dialer.FetchScannerClient(ctx)
	if err != nil {
		log.Errorf("failed to get scanner client")
		return nil, status.Errorf(codes.Internal, "failed to get scanner client error: %v", err)
	}
	if c.scanService == nil {
		c.scanService = NewScanService(scanClient)
	}

	begin := time.Now()
	response, err := c.scanService.Ping(ctx)
	if err != nil {
		logger.Errorf("[duration: %v] failed to ping scanner, err: %v", time.Since(begin), err)
		return nil, status.Errorf(codes.Internal, "failed to ping scanner error: %v", err)
	}
	logger.Tracef("ping response: %v", response)
	return response, nil
}

func (c *controller) SetState(state int32) {
	atomic.StoreInt32(&c.updateState, state)
}

func (c *controller) ping() {
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()
	logger := log.WithContext(ctx)
	scanClient, err := c.dialer.FetchScannerClient(ctx)
	if err != nil {
		log.Debug("failed to get scan client")
		return
	}

	if c.scanService == nil {
		c.scanService = NewScanService(scanClient)
	}

	begin := time.Now()
	response, err := c.scanService.Ping(ctx)
	if err != nil {
		logger.Errorf("[%v] failed to ping scanner, err: %v", time.Since(begin), err)
		c.SetState(util.TERMINATING)
		return
	}
	logger.Tracef("ping response: %v", response)
	if c.scanner == nil {
		c.scanner = &entities.Scanner{}
	}

	c.scanner.Name = os.Getenv("HOSTNAME")
	c.scanner.AssetName = response.AssetName
	c.scanner.StartupSeconds = response.StartupSeconds
	if c.scanner.Version == nil {
		c.scanner.Version = &entities.Version{}
	}
	// map unified version
	c.scanner.Version.Engine = response.Version.EngineVersion
	c.scanner.Version.Pattern = response.Version.PatternVersion
	c.lastPing = time.Now()
	c.SetState(util.IDLE)
}

func (c *controller) Scan(ctx context.Context, scanOperation []scanadapter.ScanOperation) (*util.ScanResult, error) {
	timeout := time.Duration(c.assetController.GetAsset().GetScanFeature().GetMaxScanTimeout())
	if timeout <= 0 {
		log.Warnf("failed to get scan timeout from asset, timeout : %v, is <= 0", timeout)
		timeout = 500 * time.Second
	}
	// 设置扫描的重试次数，默认为：60次，若是Asset中设置了FailTolerance字段，则使用该字段值为扫描重试次数上限
	retryCount := uint(3)
	if c.assetController.GetAsset().GetScanFeature().GetFailTolerance() != 0 {
		retryCount = uint(c.assetController.GetAsset().GetScanFeature().GetFailTolerance())
	}
	logger := log.WithContext(ctx)

	scanClient, err := c.dialer.FetchScannerClient(ctx)
	if err != nil {
		return nil, err
	}
	if c.scanService == nil {
		c.scanService = NewScanService(scanClient)
	}

	// 统一请求结构
	unifiedReq := &scanadapter.ScanRequest{Operations: scanOperation}
	log.Infof("scan tasks, request.operations: %+v, env: %s,%s", unifiedReq.Operations, os.Getenv("KAMALA_SCANNER_FILE_PLUS"), os.Getenv("KAMALA_RELAY_DIR"))

	// 鉴定的同时不进行鉴定器升级
	c.scannerMutex.Lock()
	defer c.scannerMutex.Unlock()
	var resp *scanadapter.ScanResponse
	res := &util.ScanResult{}
	begin := time.Now()
	// 扫描错误的重试逻辑，重试条件：无法连接和网络层错误
	// TODO 未来会将重试之后依旧错误的task回退
	err = retry.Retry(func(attempt uint) error {
		var retryErr error
		beginScan := time.Now()
		resp, retryErr = c.scanService.Scan(ctx, unifiedReq)
		// 若果是扫描超时即：Context DeadlineExceeded或者个是GRPC错误无法调通Scanner服务，则将错误向上层返回即可，
		// 若是鉴定器返回的是nil但是Code都是SCAN_FAILURE，那么将这一批task全部拿去重扫一遍，若是重扫达到了设置的重试上限，
		// 那么认为此时的鉴定器已经失去了扫描能力了，上层检测是否这批任务的Code全是SCAN_FAILURE，若是则触发鉴定器的重启操作
		if retryErr != nil {
			log.Warnf("[%v] failed to scan, err: %v, retry: %v", time.Since(beginScan), retryErr, attempt)
			res.RetryCount = int(attempt)
			return retryErr
		}
		kamalaResults := scanadapter.ConvertUnifiedResultsToKamala(resp.Results)
		log.Infof("request: %v, result count: %v, check code: %v, retry count: %v",
			unifiedReq, len(resp.Results), CheckScanCode(kamalaResults), attempt)
		if CheckScanCode(kamalaResults) {
			// codes.Code(api_scan.ScanCode_SCAN_FAILURE) 就表示这批样本扫描后，鉴定器返回的错误信息是nil
			// 但是Code都是 ScanCode_SCAN_FAILURE 的情况，那么显示将retryErr做设置，这样是为了直接复用retry包
			// 因为retry包是检测到了nil就返回不做重试操作了
			retryErr = status.Errorf(codes.Code(entities.ScanCode_SCAN_FAILURE),
				"all task scan code: SCAN_FAILURE, task list: %v", resp)
			log.Warnf("scan failed: %v", retryErr)
		}
		res.RetryCount = int(attempt)
		return retryErr
	}, strategy.Limit(retryCount), strategy.Wait(time.Second))

	if status.Code(err) == codes.Code(entities.ScanCode_SCAN_FAILURE) {
		err = nil
	}
	if err != nil {
		logger.Errorf("[%v] failed to scan, request: %v", time.Since(begin), unifiedReq)
		return res, err
	}
	res.Result = scanadapter.ConvertUnifiedResultsToKamala(resp.Results)
	return res, err
}

func (c *controller) Update() (*scanadapter.Version, error) {
	ctx := context.Background()
	logger := log.WithContext(ctx)
	scanClient, err := c.dialer.FetchScannerClient(ctx)
	if err != nil {
		return nil, err
	}
	if c.scanService == nil {
		c.scanService = NewScanService(scanClient)
	}

	// 升级的同时不进行样本鉴定
	// TODO 不再使用锁来控制互斥, 需要在流程上完成升级与扫描的互斥, 升级前应该确保本地没
	// 有缓存的任务, 否则升级时间过长, 会导致任务超时
	c.scannerMutex.Lock()
	defer c.scannerMutex.Unlock()

	begin := time.Now()
	logger.Debugf("start to update")
	unifiedVersion, err := c.scanService.Update(ctx, &scanadapter.UpdateRequest{})
	if err != nil {
		logger.Errorf("[%v] failed to update, err: %v", time.Since(begin), err)
		return unifiedVersion, err
	}
	c.scanner.Version.UpdateDuration = fmt.Sprintf("%vs", time.Since(begin).Seconds())
	c.lastUpdate = time.Now()
	logger.Infof("[%v] succeed to update, version: %v", time.Since(begin), unifiedVersion)
	return unifiedVersion, err
}

func (c *controller) Reload(assignVersionDir string) (*scanadapter.Version, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 600*time.Second)
	defer cancel()
	logger := log.WithContext(ctx)

	scanClient, err := c.dialer.FetchScannerClient(ctx)
	if err != nil {
		return nil, err
	}
	if c.scanService == nil {
		c.scanService = NewScanService(scanClient)
	}
	logger.Debugf("start to lock reload")
	c.scannerMutex.Lock()
	defer c.scannerMutex.Unlock()
	begin := time.Now()
	logger.Debugf("start to reload")
	//TODO find the latest version to reload
	unifiedVersion, err := c.scanService.Reload(ctx, &scanadapter.ReloadRequest{Name: assignVersionDir})
	if err != nil {
		logger.Errorf("[%v] failed to reload, err: %v", time.Since(begin), err)
		return unifiedVersion, err
	}
	logger.Infof("[%v] succeed to reload, version: %v", time.Since(begin), unifiedVersion)

	c.ping()
	return unifiedVersion, err
}

func (c *controller) Exit() error {
	ctx, cancel := context.WithTimeout(context.Background(), 600*time.Second)
	defer cancel()
	logger := log.WithContext(ctx)

	scanClient, err := c.dialer.FetchScannerClient(ctx)
	if err != nil {
		return err
	}

	begin := time.Now()
	logger.Debugf("start to lock exit")
	c.scannerMutex.Lock()
	defer c.scannerMutex.Unlock()
	if c.scanService == nil {
		c.scanService = NewScanService(scanClient)
	}
	err = c.scanService.Exit(ctx)
	if err != nil {
		logger.Errorf("[%v] failed to exit, err: %v", time.Since(begin), err)
		return err
	}
	logger.Infof("[%v] succeed to exit", time.Since(begin))
	return nil
}

func (c *controller) loopPing() {
	tick := time.Tick(100 * time.Millisecond) //tick to check the stop
	interval := time.Tick(3 * time.Second)

	for !c.Stop {
		select {
		case <-tick:
			continue
		case <-interval:
			c.ping()
		}
	}
}

func (c *controller) loopUpdate() {
	tick := time.Tick(100 * time.Millisecond) //tick to check the stop
	interval := time.Tick(3 * time.Second)

	for !c.Stop {
		select {
		case <-tick:
			continue
		case <-interval:
			curAsset := c.assetController.GetAsset()
			if curAsset == nil {
				continue
			}

			if curAsset.GetUpdateFeature().GetSelfConfig().GetInterval() == 0 {
				continue
			}

			updateInterval := time.Duration(curAsset.GetUpdateFeature().GetSelfConfig().GetInterval())

			if updateInterval <= 0 {
				log.Debugf("no need to update, interval: %v",
					curAsset.GetUpdateFeature().GetSelfConfig().GetInterval())
				continue
			}

			if c.GetState() == util.FIRST_UPDATE {
				log.Debugf("in first update, state: %v", c.GetState())
				continue
			}

			if c.GetState() == util.TERMINATING || c.GetState() == util.Startup {
				log.Debugf("currently at terminating or startup state: %v", c.GetState())
				continue
			}

			if time.Since(c.lastUpdate) < updateInterval {
				//log.Debugf("not time to update, curDuration: %v, interval: %v", time.Since(c.lastUpdate), updateInterval)
				continue
			}
			c.SetState(util.UPDATE_READY)
			log.Debugf("time to update set state：UPDATE_READY, state: %v, curDuration: %v, updateInterval: %v",
				c.GetState(), time.Since(c.lastUpdate), updateInterval)
			c.waitStateSignal()
		}
	}
}

// 等待状态信号，若此时状态变成了Updating说明Scan已经将taskChan中的任务处理干净了，可以进行升级操作了
// 若此时的状态变成了RestartUpdate说明在升级时间到的同时，调用了Exit，变成这个状态则是由于重启后显示升级
// 触发的，那么接下来就不需要在做升级操作了，continue即可；
func (c *controller) waitStateSignal() {
	// 触发统计现在已经领取的任务量并做扫描处理，之后将状态改变为Updating
	// 监听一个特定的信号
	for {
		if c.GetState() == util.UPDATING {
			log.Debugf("local tasks already fetched scan complete, receive update signal:%v", c.GetState())
			c.toUpdate()
			break
		}
		// 获取状态并判断当前状态是否为：Terminating或Startup， 是因为：当正在扫描的过程中遇上升级时间到了，
		// 会触发loopUpdate更新状态为：UpdateReady，但它同时也会触发loopUpdate循环阻塞等待目前现有任务全部扫描完
		// 在这个扫描过程中可能触发鉴定器的重启，一旦鉴定器调用重启接口，那么状态将置为：Terminating去重启鉴定器
		// 此时loopUpdate想要去做的Update其实在鉴定器重启后就做了，就不需要一直阻塞在这里等Updating状态的到来了，
		if c.GetState() == util.TERMINATING || c.GetState() == util.Startup {
			log.Debugf("ready to update meet restart, state：%v", c.GetState())
			c.lastUpdate = time.Now()
			break
		}
		log.Debugf("deal tasks of already fetched, state：%v ....", c.GetState())
		time.Sleep(time.Millisecond * 100)
	}
}

func (c *controller) toUpdate() {
	log.Debugf("time to update, lastUpdate: %v", c.lastUpdate)
	start := time.Now()
	version, err := c.Update()
	if err != nil {
		log.Warnf("failed to update, err: %v", err)
	} else {
		// 统一版本转实体结构
		c.scanner.Version.Engine = version.EngineVersion
		c.scanner.Version.Pattern = version.PatternVersion
		c.scanner.Version.UpdateDuration = fmt.Sprintf("%vs", time.Since(start).Seconds())
		c.scanner.Version.UpdateTime = start.Unix()
		log.Infof("succeed to update, version: %+v", version)
	}
	// 升级完成后，将状态更新为：IDLE(原子操作)
	c.SetState(util.IDLE)
}

// 检查扫描结果列表中的ScanCode，全部Code为：ScanCode_SCAN_FAILURE，返回true，
// 有一个不为：ScanCode_SCAN_FAILURE返回false
func CheckScanCode(scanResult map[string]*api_scan.ScanResult) bool {
	for _, v := range scanResult {
		if entities.ScanCode(v.GetCode()) != entities.ScanCode_SCAN_FAILURE {
			return false
		}
	}
	return true
}
