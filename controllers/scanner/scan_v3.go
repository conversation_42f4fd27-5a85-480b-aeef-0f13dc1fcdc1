//go:build scan_v3
// +build scan_v3

package scanner

import (
	"context"
	"encoding/json"
	"fmt"
	"path"
	"strings"
	"time"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	scan "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git"
	api "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git/api"
	scanadapter "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/adapters/scan"
	google_empty "github.com/golang/protobuf/ptypes/empty"
)

type v3Adapter struct {
	client api.ScanApiClient
}

func NewScanService(client api.ScanApiClient) scanadapter.ScanService {
	return &v3Adapter{client: client}
}

func (a *v3Adapter) Scan(ctx context.Context, req *scanadapter.ScanRequest) (*scanadapter.ScanResponse, error) {
	// v3 使用 map<string, string> parameters
	logger := log.WithContext(ctx)
	logger.Debugf("v3 scan: begin convert, operations: %d", len(req.Operations))
	params := make(map[string]string)
	for _, op := range req.Operations {
		// 构造 v3 的 key（样本路径）
		pathKey := op.Path
		usedPrefix := false
		if op.PathPrefixVar != "" && !path.IsAbs(op.Path) {
			// 让 v3 通过环境变量展开前缀：${VAR}/relative/path
			pathKey = fmt.Sprintf("${%s}/%s", op.PathPrefixVar, strings.TrimPrefix(op.Path, "/"))
			usedPrefix = true
		}

		// 构造 v3 的 value（参数串）。需要把 OutputDir 注入参数
		param := strings.TrimSpace(op.Parameter)
		mergedJSON := false
		if op.OutputDir != "" {
			// 如果参数是 JSON，合并 outdir 字段；否则追加 --outdir 标志
			if strings.HasPrefix(param, "{") {
				var m map[string]interface{}
				if err := json.Unmarshal([]byte(param), &m); err == nil {
					m["outdir"] = op.OutputDir
					if merged, err := json.Marshal(m); err == nil {
						param = string(merged)
						mergedJSON = true
					} else {
						logger.Warnf("v3 scan: marshal merged json failed: %v", err)
					}
				} else {
					logger.Warnf("v3 scan: parse json parameter failed: %v", err)
				}
			} else {
				if param == "" {
					param = fmt.Sprintf("--outdir %s", op.OutputDir)
				} else {
					param = fmt.Sprintf("%s --outdir %s", param, op.OutputDir)
				}
			}
		}

		logger.Debugf("v3 scan: map op -> key: %q (usedPrefix:%v), outdir:%v, mergedJSON:%v", pathKey, usedPrefix, op.OutputDir != "", mergedJSON)
		params[pathKey] = param
	}

	begin := time.Now()
	resp, err := a.client.Scan(ctx, &api.Scan_Request{
		Parameters: params,
	})
	if err != nil {
		logger.Errorf("[duration:%v] v3 scan RPC failed, err: %v, params_count:%d", time.Since(begin), err, len(params))
		return nil, err
	}
	logger.Infof("[duration:%v] v3 scan RPC succeed, results:%d", time.Since(begin), len(resp.Results))

	// 转换 ScanCode（v3 的值需要映射到统一值）
	results := make(map[string]*scanadapter.ScanResult)
	for k, v := range resp.Results {
		var version *scanadapter.Version
		if v.Version != nil {
			version = &scanadapter.Version{
				EngineVersion:  v.Version.EngineVersion,
				PatternVersion: v.Version.PatternVersion,
			}
		}
		results[k] = &scanadapter.ScanResult{
			Code:    convertV3ScanCodeToUnified(v.Code),
			Name:    v.Name,
			Message: v.Message,
			Version: version,
		}
	}

	return &scanadapter.ScanResponse{Results: results}, nil
}

func (a *v3Adapter) Reload(ctx context.Context, req *scanadapter.ReloadRequest) (*scanadapter.Version, error) {
	// v3 的 Reload 只有 name 字段
	resp, err := a.client.Reload(ctx, &api.Reload_Request{
		Name: req.Name,
	})
	if err != nil {
		return nil, err
	}
	return &scanadapter.Version{
		EngineVersion:  resp.EngineVersion,
		PatternVersion: resp.PatternVersion,
	}, nil
}

func (a *v3Adapter) Ping(ctx context.Context) (*scanadapter.PingResponse, error) {
	resp, err := a.client.Ping(ctx, &google_empty.Empty{})
	if err != nil {
		return nil, err
	}

	return &scanadapter.PingResponse{
		Name:           resp.Name,
		AssetName:      resp.AssetName,
		StartupSeconds: resp.StartupSeconds,
		State:          scanadapter.State(resp.State),
		Version: &scanadapter.Version{
			EngineVersion:  resp.Version.EngineVersion,
			PatternVersion: resp.Version.PatternVersion,
		},
	}, nil
}

func (a *v3Adapter) Update(ctx context.Context, req *scanadapter.UpdateRequest) (*scanadapter.Version, error) {
	resp, err := a.client.Update(ctx, &api.Update_Request{
		Name: req.Name,
	})
	if err != nil {
		return nil, err
	}

	return &scanadapter.Version{
		EngineVersion:  resp.EngineVersion,
		PatternVersion: resp.PatternVersion,
	}, nil
}

func (a *v3Adapter) Exit(ctx context.Context) error {
	_, err := a.client.Exit(ctx, &google_empty.Empty{})
	return err
}

func convertV3ScanCodeToUnified(v3Code scan.ScanCode) scanadapter.ScanCode {
	switch v3Code {
	case scan.ScanCode_NOTSCAN:
		return scanadapter.ScanCode_NOTSCAN
	case scan.ScanCode_OK:
		return scanadapter.ScanCode_OK
	case scan.ScanCode_INVALID_PARAM:
		return scanadapter.ScanCode_INVALID_PARAM
	case scan.ScanCode_DOWNLOAD_FAILURE:
		return scanadapter.ScanCode_DOWNLOAD_FAILURE
	case scan.ScanCode_SHA1_FAILURE:
		return scanadapter.ScanCode_SHA1_FAILURE
	case scan.ScanCode_SIZE_FAILURE:
		return scanadapter.ScanCode_SIZE_FAILURE
	case scan.ScanCode_SCAN_FAILURE:
		return scanadapter.ScanCode_SCAN_FAILURE
	case scan.ScanCode_FILE_UNSUPPORT:
		return scanadapter.ScanCode_FILE_UNSUPPORT
	case scan.ScanCode_UPLOAD_FAILURE:
		return scanadapter.ScanCode_UPLOAD_FAILURE
	case scan.ScanCode_OTHER:
		return scanadapter.ScanCode_OTHER
	default:
		return scanadapter.ScanCode_OTHER
	}
}
