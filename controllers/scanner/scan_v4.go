//go:build !scan_v3
// +build !scan_v3

package scanner

import (
	"context"

	scan "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git"
	api "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git/api"
	scanadapter "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/adapters/scan"
	google_empty "github.com/golang/protobuf/ptypes/empty"
)

type v4Adapter struct {
	client api.ScanApiClient
}

func NewScanService(client api.ScanApiClient) scanadapter.ScanService {
	return &v4Adapter{client: client}
}

func (a *v4Adapter) Scan(ctx context.Context, req *scanadapter.ScanRequest) (*scanadapter.ScanResponse, error) {
	// v4 使用 repeated ScanOperation
	operations := make([]*scan.ScanOperation, len(req.Operations))
	for i, op := range req.Operations {
		operations[i] = &scan.ScanOperation{
			Path:          op.Path,
			PathPrefixVar: op.PathPrefixVar,
			Parameter:     op.Parameter,
			OutputDir:     op.OutputDir,
		}
	}

	resp, err := a.client.Scan(ctx, &api.Scan_Request{
		Operations: operations,
	})
	if err != nil {
		return nil, err
	}

	// v4 的 ScanCode 已经是统一的值，直接转换
	results := make(map[string]*scanadapter.ScanResult)
	for k, v := range resp.Results {
		var version *scanadapter.Version
		if v.Version != nil {
			version = &scanadapter.Version{
				EngineVersion:  v.Version.EngineVersion,
				PatternVersion: v.Version.PatternVersion,
			}
		}
		results[k] = &scanadapter.ScanResult{
			Code:    scanadapter.ScanCode(v.Code), // 直接类型转换
			Name:    v.Name,
			Message: v.Message,
			Version: version,
		}
	}

	return &scanadapter.ScanResponse{Results: results}, nil
}

func (a *v4Adapter) Reload(ctx context.Context, req *scanadapter.ReloadRequest) (*scanadapter.Version, error) {
	resp, err := a.client.Reload(ctx, &api.Reload_Request{
		Name: req.Name,
	})
	if err != nil {
		return nil, err
	}
	return &scanadapter.Version{
		EngineVersion:  resp.EngineVersion,
		PatternVersion: resp.PatternVersion,
	}, nil
}

func (a *v4Adapter) Ping(ctx context.Context) (*scanadapter.PingResponse, error) {
	resp, err := a.client.Ping(ctx, &google_empty.Empty{})
	if err != nil {
		return nil, err
	}

	var version *scanadapter.Version
	if resp.Version != nil {
		version = &scanadapter.Version{
			EngineVersion:  resp.Version.EngineVersion,
			PatternVersion: resp.Version.PatternVersion,
		}
	}

	return &scanadapter.PingResponse{
		Name:           resp.Name,
		AssetName:      resp.AssetName,
		StartupSeconds: resp.StartupSeconds,
		State:          scanadapter.State(resp.State),
		Version:        version,
	}, nil
}

func (a *v4Adapter) Update(ctx context.Context, req *scanadapter.UpdateRequest) (*scanadapter.Version, error) {
	resp, err := a.client.Update(ctx, &api.Update_Request{
		Name: req.Name,
	})
	if err != nil {
		return nil, err
	}

	return &scanadapter.Version{
		EngineVersion:  resp.EngineVersion,
		PatternVersion: resp.PatternVersion,
	}, nil
}

func (a *v4Adapter) Exit(ctx context.Context) error {
	_, err := a.client.Exit(ctx, &google_empty.Empty{})
	return err
}
