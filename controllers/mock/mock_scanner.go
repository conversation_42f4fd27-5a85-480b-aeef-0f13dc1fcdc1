// Code generated by MockGen. DO NOT EDIT.
// Source: scanner.go
//
// Generated by this command:
//
//	mockgen -source=scanner.go -destination=mock/mock_scanner.go -package=mock_controllers
//

// Package mock_controllers is a generated GoMock package.
package mock_controllers

import (
	context "context"
	reflect "reflect"

	kamala_entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	scanadapter "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/adapters/scan"
	controllers "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers"
	util "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"
	gomock "go.uber.org/mock/gomock"
)

// MockScannerController is a mock of ScannerController interface.
type MockScannerController struct {
	ctrl     *gomock.Controller
	recorder *MockScannerControllerMockRecorder
	isgomock struct{}
}

// MockScannerControllerMockRecorder is the mock recorder for MockScannerController.
type MockScannerControllerMockRecorder struct {
	mock *MockScannerController
}

// NewMockScannerController creates a new mock instance.
func NewMockScannerController(ctrl *gomock.Controller) *MockScannerController {
	mock := &MockScannerController{ctrl: ctrl}
	mock.recorder = &MockScannerControllerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockScannerController) EXPECT() *MockScannerControllerMockRecorder {
	return m.recorder
}

// Exit mocks base method.
func (m *MockScannerController) Exit() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Exit")
	ret0, _ := ret[0].(error)
	return ret0
}

// Exit indicates an expected call of Exit.
func (mr *MockScannerControllerMockRecorder) Exit() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Exit", reflect.TypeOf((*MockScannerController)(nil).Exit))
}

// Finalize mocks base method.
func (m *MockScannerController) Finalize() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Finalize")
}

// Finalize indicates an expected call of Finalize.
func (mr *MockScannerControllerMockRecorder) Finalize() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Finalize", reflect.TypeOf((*MockScannerController)(nil).Finalize))
}

// GetScanner mocks base method.
func (m *MockScannerController) GetScanner() *kamala_entities.Scanner {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScanner")
	ret0, _ := ret[0].(*kamala_entities.Scanner)
	return ret0
}

// GetScanner indicates an expected call of GetScanner.
func (mr *MockScannerControllerMockRecorder) GetScanner() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScanner", reflect.TypeOf((*MockScannerController)(nil).GetScanner))
}

// GetState mocks base method.
func (m *MockScannerController) GetState() int32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetState")
	ret0, _ := ret[0].(int32)
	return ret0
}

// GetState indicates an expected call of GetState.
func (mr *MockScannerControllerMockRecorder) GetState() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetState", reflect.TypeOf((*MockScannerController)(nil).GetState))
}

// Ping mocks base method.
func (m *MockScannerController) Ping() (*scanadapter.PingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Ping")
	ret0, _ := ret[0].(*scanadapter.PingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Ping indicates an expected call of Ping.
func (mr *MockScannerControllerMockRecorder) Ping() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Ping", reflect.TypeOf((*MockScannerController)(nil).Ping))
}

// Reload mocks base method.
func (m *MockScannerController) Reload(assignVersionDir string) (*scanadapter.Version, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Reload", assignVersionDir)
	ret0, _ := ret[0].(*scanadapter.Version)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Reload indicates an expected call of Reload.
func (mr *MockScannerControllerMockRecorder) Reload(assignVersionDir any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reload", reflect.TypeOf((*MockScannerController)(nil).Reload), assignVersionDir)
}

// Scan mocks base method.
func (m *MockScannerController) Scan(ctx context.Context, scanOperation []scanadapter.ScanOperation) (*util.ScanResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Scan", ctx, scanOperation)
	ret0, _ := ret[0].(*util.ScanResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Scan indicates an expected call of Scan.
func (mr *MockScannerControllerMockRecorder) Scan(ctx, scanOperation any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Scan", reflect.TypeOf((*MockScannerController)(nil).Scan), ctx, scanOperation)
}

// SetAssetController mocks base method.
func (m *MockScannerController) SetAssetController(arg0 controllers.AssetController) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetAssetController", arg0)
}

// SetAssetController indicates an expected call of SetAssetController.
func (mr *MockScannerControllerMockRecorder) SetAssetController(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAssetController", reflect.TypeOf((*MockScannerController)(nil).SetAssetController), arg0)
}

// SetState mocks base method.
func (m *MockScannerController) SetState(state int32) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetState", state)
}

// SetState indicates an expected call of SetState.
func (mr *MockScannerControllerMockRecorder) SetState(state any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetState", reflect.TypeOf((*MockScannerController)(nil).SetState), state)
}

// Update mocks base method.
func (m *MockScannerController) Update() (*scanadapter.Version, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update")
	ret0, _ := ret[0].(*scanadapter.Version)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockScannerControllerMockRecorder) Update() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockScannerController)(nil).Update))
}
