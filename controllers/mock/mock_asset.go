// Code generated by MockGen. DO NOT EDIT.
// Source: asset.go
//
// Generated by this command:
//
//	mockgen -source=asset.go -destination=mock/mock_asset.go -package=mock_controllers
//

// Package mock_controllers is a generated GoMock package.
package mock_controllers

import (
	reflect "reflect"

	kamala_entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	controllers "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers"
	gomock "go.uber.org/mock/gomock"
)

// MockAssetController is a mock of AssetController interface.
type MockAssetController struct {
	ctrl     *gomock.Controller
	recorder *MockAssetControllerMockRecorder
	isgomock struct{}
}

// MockAssetControllerMockRecorder is the mock recorder for MockAssetController.
type MockAssetControllerMockRecorder struct {
	mock *MockAssetController
}

// NewMockAssetController creates a new mock instance.
func NewMockAssetController(ctrl *gomock.Controller) *MockAssetController {
	mock := &MockAssetController{ctrl: ctrl}
	mock.recorder = &MockAssetControllerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAssetController) EXPECT() *MockAssetControllerMockRecorder {
	return m.recorder
}

// Finalize mocks base method.
func (m *MockAssetController) Finalize() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Finalize")
}

// Finalize indicates an expected call of Finalize.
func (mr *MockAssetControllerMockRecorder) Finalize() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Finalize", reflect.TypeOf((*MockAssetController)(nil).Finalize))
}

// GetAsset mocks base method.
func (m *MockAssetController) GetAsset() *kamala_entities.Asset {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAsset")
	ret0, _ := ret[0].(*kamala_entities.Asset)
	return ret0
}

// GetAsset indicates an expected call of GetAsset.
func (mr *MockAssetControllerMockRecorder) GetAsset() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAsset", reflect.TypeOf((*MockAssetController)(nil).GetAsset))
}

// SetScannerController mocks base method.
func (m *MockAssetController) SetScannerController(controller controllers.ScannerController) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetScannerController", controller)
}

// SetScannerController indicates an expected call of SetScannerController.
func (mr *MockAssetControllerMockRecorder) SetScannerController(controller any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetScannerController", reflect.TypeOf((*MockAssetController)(nil).SetScannerController), controller)
}
