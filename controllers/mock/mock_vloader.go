// Code generated by MockGen. DO NOT EDIT.
// Source: vloader.go
//
// Generated by this command:
//
//	mockgen -source=vloader.go -destination=mock/mock_vloader.go -package=mock_controllers
//

// Package mock_controllers is a generated GoMock package.
package mock_controllers

import (
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockVloaderController is a mock of VloaderController interface.
type MockVloaderController struct {
	ctrl     *gomock.Controller
	recorder *MockVloaderControllerMockRecorder
	isgomock struct{}
}

// MockVloaderControllerMockRecorder is the mock recorder for MockVloaderController.
type MockVloaderControllerMockRecorder struct {
	mock *MockVloaderController
}

// NewMockVloaderController creates a new mock instance.
func NewMockVloaderController(ctrl *gomock.Controller) *MockVloaderController {
	mock := &MockVloaderController{ctrl: ctrl}
	mock.recorder = &MockVloaderControllerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockVloaderController) EXPECT() *MockVloaderControllerMockRecorder {
	return m.recorder
}

// DownloadVirusLibrary mocks base method.
func (m *MockVloaderController) DownloadVirusLibrary(engineVersion, patternVersion string) (string, string, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DownloadVirusLibrary", engineVersion, patternVersion)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(string)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// DownloadVirusLibrary indicates an expected call of DownloadVirusLibrary.
func (mr *MockVloaderControllerMockRecorder) DownloadVirusLibrary(engineVersion, patternVersion any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadVirusLibrary", reflect.TypeOf((*MockVloaderController)(nil).DownloadVirusLibrary), engineVersion, patternVersion)
}
