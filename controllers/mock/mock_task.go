// Code generated by MockGen. DO NOT EDIT.
// Source: git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers (interfaces: TaskController)
//
// Generated by this command:
//
//	mockgen -destination mock/mock_task.go git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers TaskController
//

// Package mock_controllers is a generated GoMock package.
package mock_controllers

import (
	reflect "reflect"

	kamala_entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	gomock "go.uber.org/mock/gomock"
)

// MockTaskController is a mock of TaskController interface.
type MockTaskController struct {
	ctrl     *gomock.Controller
	recorder *MockTaskControllerMockRecorder
	isgomock struct{}
}

// MockTaskControllerMockRecorder is the mock recorder for MockTaskController.
type MockTaskControllerMockRecorder struct {
	mock *MockTaskController
}

// NewMockTaskController creates a new mock instance.
func NewMockTaskController(ctrl *gomock.Controller) *MockTaskController {
	mock := &MockTaskController{ctrl: ctrl}
	mock.recorder = &MockTaskControllerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTaskController) EXPECT() *MockTaskControllerMockRecorder {
	return m.recorder
}

// DeliverTask mocks base method.
func (m *MockTaskController) DeliverTask(arg0 *kamala_entities.Task, arg1 map[string]string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeliverTask", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeliverTask indicates an expected call of DeliverTask.
func (mr *MockTaskControllerMockRecorder) DeliverTask(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeliverTask", reflect.TypeOf((*MockTaskController)(nil).DeliverTask), arg0, arg1)
}

// FetchTask mocks base method.
func (m *MockTaskController) FetchTask(arg0 string, arg1 int32) []*kamala_entities.Task {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchTask", arg0, arg1)
	ret0, _ := ret[0].([]*kamala_entities.Task)
	return ret0
}

// FetchTask indicates an expected call of FetchTask.
func (mr *MockTaskControllerMockRecorder) FetchTask(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchTask", reflect.TypeOf((*MockTaskController)(nil).FetchTask), arg0, arg1)
}

// Finalize mocks base method.
func (m *MockTaskController) Finalize() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Finalize")
}

// Finalize indicates an expected call of Finalize.
func (mr *MockTaskControllerMockRecorder) Finalize() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Finalize", reflect.TypeOf((*MockTaskController)(nil).Finalize))
}

// GetMode mocks base method.
func (m *MockTaskController) GetMode() int32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMode")
	ret0, _ := ret[0].(int32)
	return ret0
}

// GetMode indicates an expected call of GetMode.
func (mr *MockTaskControllerMockRecorder) GetMode() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMode", reflect.TypeOf((*MockTaskController)(nil).GetMode))
}
