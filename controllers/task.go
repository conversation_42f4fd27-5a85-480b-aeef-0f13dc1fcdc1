//go:generate mockgen -destination mock/mock_task.go git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers TaskController
package controllers

import (
	entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
)

type TaskController interface {
	FetchTask(string, int32) []*entities.Task
	DeliverTask(*entities.Task, map[string]string) error
	GetMode() int32
	Finalize()
}
