module git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git

go 1.22.0

require (
	git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework-plugins.git v1.0.170
	git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git v1.0.381
	git-biz.qianxin-inc.cn/zion-infra/data-foundation/apis/morpheus-api.git v1.5.4
	git-biz.qianxin-inc.cn/zion-infra/file_access-api v1.0.3
	git-biz.qianxin-inc.cn/zion-infra/kamala/api.git v3.0.5+incompatible
	git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git v0.7.24
	git-biz.qianxin-inc.cn/zion-infra/kamala/apis/model.git/v2 v2.7.14
	git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git v1.0.0
	git-biz.qianxin-inc.cn/zion-infra/kamala/apis/vloader.git v1.0.3
	git-biz.qianxin-inc.cn/zion-infra/kamala/apis/worker.git v2.1.0+incompatible
	git-open.qianxin-inc.cn/yuanruifeng/pqueue v0.0.10
	github.com/Rican7/retry v0.1.0
	github.com/agiledragon/gomonkey/v2 v2.7.0
	github.com/cch123/supermonkey v1.0.1
	github.com/golang/protobuf v1.5.4
	github.com/judwhite/go-svc v1.1.2
	github.com/magiconair/properties v1.8.0
	github.com/panjf2000/ants v1.2.1
	github.com/pborman/uuid v1.2.1
	github.com/stretchr/testify v1.8.4
	go.uber.org/mock v0.4.0
	golang.org/x/net v0.25.0
	google.golang.org/genproto v0.0.0-20240509183442-62759503f434
	google.golang.org/grpc v1.65.0
	google.golang.org/protobuf v1.34.1

// git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework-plugins.git v1.0.83
// git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git v1.0.195
// git-biz.qianxin-inc.cn/zion-infra/file_access-api v1.0.3
// git-biz.qianxin-inc.cn/zion-infra/kamala/api.git v3.0.5+incompatible
// git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git v0.7.19
// git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git v1.0.0
// git-biz.qianxin-inc.cn/zion-infra/kamala/apis/worker.git v2.1.0+incompatible
// git-biz.qianxin-inc.cn/zion-infra/morpheus-api v0.6.5
// git-open.qianxin-inc.cn/yuanruifeng/pqueue v0.0.10
// github.com/Rican7/retry v0.1.0
// github.com/cch123/supermonkey v1.0.1
// github.com/golang/mock v1.4.4
// github.com/golang/protobuf v1.3.2
// github.com/judwhite/go-svc v1.1.2
// github.com/magiconair/properties v1.8.0
// github.com/panjf2000/ants v1.2.0
// github.com/satori/go.uuid v1.2.0
// github.com/stretchr/testify v1.6.1
// golang.org/x/net v0.0.0-20200226121028-0de0cce0169b
// google.golang.org/genproto v0.0.0-20191216164720-4f79533eabd1
// google.golang.org/grpc v1.26.0
// git-biz.qianxin-inc.cn/zion-infra/kamala/apis/vloader.git v0.0.4

)

require (
	git-biz.qianxin-inc.cn/infra-components/data-access-framework/golang/data-access-protocol-go.git v1.0.7 // indirect
	git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/config-encryptor.git v0.0.4 // indirect
	git-biz.qianxin-inc.cn/infra-components/sdk/retry.git v1.0.4 // indirect
	git-open.qianxin-inc.cn/free/memberlist v0.0.3 // indirect
	git-open.qianxin-inc.cn/free/pkix.git v0.0.3 // indirect
	github.com/NYTimes/gziphandler v1.1.1 // indirect
	github.com/ahmetb/go-linq/v3 v3.2.0 // indirect
	github.com/armon/go-metrics v0.4.1 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cenkalti/backoff/v4 v4.3.0 // indirect
	github.com/cep21/circuit/v3 v3.2.2 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/coreos/go-semver v0.3.1 // indirect
	github.com/coreos/go-systemd/v22 v22.5.0 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/desertbit/timer v0.0.0-20180107155436-c41aec40b27f // indirect
	github.com/dgryski/go-metro v0.0.0-20211217172704-adc40b04c140 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/fatih/color v1.15.0 // indirect
	github.com/fatih/pool v3.0.0+incompatible // indirect
	github.com/fsnotify/fsnotify v1.7.0 // indirect
	github.com/go-ini/ini v1.67.0 // indirect
	github.com/go-redis/redis/v8 v8.11.5 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/google/btree v1.1.2 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/gorilla/mux v1.8.1 // indirect
	github.com/grpc-ecosystem/go-grpc-prometheus v1.2.0 // indirect
	github.com/grpc-ecosystem/grpc-gateway v1.16.0 // indirect
	github.com/hashicorp/consul/api v1.26.1 // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-cleanhttp v0.5.2 // indirect
	github.com/hashicorp/go-hclog v1.5.0 // indirect
	github.com/hashicorp/go-immutable-radix v1.3.1 // indirect
	github.com/hashicorp/go-metrics v0.5.1 // indirect
	github.com/hashicorp/go-msgpack v0.5.5 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/go-retryablehttp v0.7.4 // indirect
	github.com/hashicorp/go-rootcerts v1.0.2 // indirect
	github.com/hashicorp/go-sockaddr v1.0.5 // indirect
	github.com/hashicorp/golang-lru v1.0.2 // indirect
	github.com/hashicorp/serf v0.10.1 // indirect
	github.com/iancoleman/strcase v0.3.0 // indirect
	github.com/improbable-eng/grpc-web v0.15.0 // indirect
	github.com/klauspost/compress v1.17.2 // indirect
	github.com/lestrrat-go/strftime v1.0.6 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/miekg/dns v1.1.56 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/natefinch/npipe v0.0.0-20160621034901-c1b8fa8bdcce // indirect
	github.com/opentracing/opentracing-go v1.2.0 // indirect
	github.com/orcaman/concurrent-map v1.0.0 // indirect
	github.com/panmari/cuckoofilter v1.0.6 // indirect
	github.com/pelletier/go-toml v1.9.5 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/prometheus/client_golang v1.19.1 // indirect
	github.com/prometheus/client_model v0.6.1 // indirect
	github.com/prometheus/common v0.53.0 // indirect
	github.com/prometheus/procfs v0.14.0 // indirect
	github.com/rs/cors v1.11.0 // indirect
	github.com/sean-/seed v0.0.0-20170313163322-e2103e2c3529 // indirect
	github.com/soheilhy/cmux v0.1.5 // indirect
	github.com/uber/jaeger-client-go v2.30.0+incompatible // indirect
	github.com/uber/jaeger-lib v2.4.1+incompatible // indirect
	github.com/xhit/go-str2duration/v2 v2.1.0 // indirect
	go.etcd.io/etcd/api/v3 v3.5.10 // indirect
	go.etcd.io/etcd/client/pkg/v3 v3.5.10 // indirect
	go.etcd.io/etcd/client/v3 v3.5.10 // indirect
	go.uber.org/atomic v1.11.0 // indirect
	go.uber.org/automaxprocs v1.5.3 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	go.uber.org/zap v1.26.0 // indirect
	golang.org/x/arch v0.0.0-20200826200359-b19915210f00 // indirect
	golang.org/x/exp v0.0.0-20231006140011-7918f672742d // indirect
	golang.org/x/mod v0.13.0 // indirect
	golang.org/x/sync v0.7.0 // indirect
	golang.org/x/sys v0.20.0 // indirect
	golang.org/x/text v0.15.0 // indirect
	golang.org/x/time v0.5.0 // indirect
	golang.org/x/tools v0.14.0 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20240528184218-531527333157 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240528184218-531527333157 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	nhooyr.io/websocket v1.8.11 // indirect
)
