#!/bin/bash

set -e
set -o pipefail

# 新框架占用了 LOGGER 环境变量, 我们用新框架的时候改了个名
# 为了兼容老框架的collector参数, kamala-collector这个configmap里会存在 LOGGER 和 LOGTYPE 两个环境变量, 值都是qlog
# unset LOGGER 不要删, 新框架检测到 LOGGER 环境变量存在, 且内容不符合规范会报错, collector启动失败, 我们只好手动 unset

unset LOGGER

env

COMMAND="/opt/kamala/kamala-collector \
    -logger.type=$LOGTYPE \
    -logger.qlog.addr=$HOST_ADDR:$QLOG_AGENT_PORT \
    -logger.qlog.category=$QLOG_CATEGORY \
    -logger.min_level=$LOGGER_MIN_LEVEL \
    -logger.rpc_tracing=false \
    -dialer.timeout=2000ms"

if [ $# -eq 0 ]; then
    set -- $COMMAND
elif [ "${1:0:1}" = '-' ]; then
    set -- $COMMAND "$@"
fi

exec "$@"
