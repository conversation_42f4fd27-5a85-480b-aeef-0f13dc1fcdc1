package main

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io/ioutil"
	"math"
	"os"
	"path"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git"
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	kamala "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git"
	scanadapter "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/adapters/scan"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers"
	mock_controllers "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/controllers/mock"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/helper"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk"
	mock_storage "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/mock"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/proxy"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"
	"git-open.qianxin-inc.cn/yuanruifeng/pqueue"
	sm "github.com/cch123/supermonkey"
	"github.com/panjf2000/ants"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
)

func TestInitDirList(t *testing.T) {
	t.Setenv("KAMALA_SCANNER_FILE_PLUS", "./plusfile")
	s := &scan{
		plusFileDir: os.Getenv("KAMALA_SCANNER_FILE_PLUS"),
	}
	s.initDirList()
	dirs, err := ioutil.ReadDir("./plusfile")
	if err != nil {
		t.Fatalf("read dir failed: %v", err)
	}
	assert.Equal(t, 15, len(dirs))
	for _, dir := range dirs {
		exist := util.CheckFileIsExist(path.Join("./plusfile", dir.Name(), LockFile))
		assert.Equal(t, true, exist)
	}
	err = os.RemoveAll("./plusfile")
	if err != nil {
		t.Fatalf("clean test file failed: %v", err)
	}
}

func TestIsUseDisk(t *testing.T) {
	mockAsset := mock_controllers.NewMockAssetController(gomock.NewController(t))
	mockAsset.EXPECT().GetAsset().Return(&entities.Asset{
		Name: "test_scanner",
		ScanFeature: &entities.ScanFeature{
			MaxFetchSize: 400 * MB,
		},
	}).AnyTimes()

	s := &scan{
		emptyDirSize:    800 * MB,
		fileDir:         "./testfile",
		logDir:          "./testfile",
		assetController: mockAsset,
	}
	tests := []struct {
		size           uint64
		usedDisk       bool
		expectEstimate uint64
	}{
		{
			size:           100 * MB,
			usedDisk:       false,
			expectEstimate: 100 * MB,
		},
		{
			size:           900 * MB,
			usedDisk:       true,
			expectEstimate: 0,
		},
		{
			size:           1,
			usedDisk:       true,
			expectEstimate: 0,
		},
		{
			size:           0,
			usedDisk:       true,
			expectEstimate: 0,
		},
	}

	for _, tt := range tests {
		actual := s.isUseDisk(tt.size)
		assert.Equal(t, tt.usedDisk, actual)
		assert.Equal(t, tt.expectEstimate, s.estimateSize)
		s.estimateSize = 0
	}
}

func TestChooseDirList(t *testing.T) {
	tests := []struct {
		size   uint64
		expect []DirInfo
	}{
		{
			size: MB,
			expect: []DirInfo{
				{Name: "file1", Size: 5 * GB},
				{Name: "file2", Size: 5 * GB},
				{Name: "file3", Size: 5 * GB},
				{Name: "file4", Size: 5 * GB},
				{Name: "file5", Size: 5 * GB},
				{Name: "file6", Size: 5 * GB},
				{Name: "file7", Size: 5 * GB},
				{Name: "file8", Size: 5 * GB},
				{Name: "file9", Size: 5 * GB},
				{Name: "file10", Size: 5 * GB},
				{Name: "file11", Size: 5 * GB},
				{Name: "file12", Size: 5 * GB},
				{Name: "file13", Size: 10 * GB},
				{Name: "file14", Size: 10 * GB},
				{Name: "file15", Size: 20 * GB},
			},
		},
		{
			size: 0,
			expect: []DirInfo{
				{Name: "file1", Size: 5 * GB},
				{Name: "file2", Size: 5 * GB},
				{Name: "file3", Size: 5 * GB},
				{Name: "file4", Size: 5 * GB},
				{Name: "file5", Size: 5 * GB},
				{Name: "file6", Size: 5 * GB},
				{Name: "file7", Size: 5 * GB},
				{Name: "file8", Size: 5 * GB},
				{Name: "file9", Size: 5 * GB},
				{Name: "file10", Size: 5 * GB},
				{Name: "file11", Size: 5 * GB},
				{Name: "file12", Size: 5 * GB},
				{Name: "file13", Size: 10 * GB},
				{Name: "file14", Size: 10 * GB},
				{Name: "file15", Size: 20 * GB},
			},
		},
		{
			size: 1,
			expect: []DirInfo{
				{Name: "file1", Size: 5 * GB},
				{Name: "file2", Size: 5 * GB},
				{Name: "file3", Size: 5 * GB},
				{Name: "file4", Size: 5 * GB},
				{Name: "file5", Size: 5 * GB},
				{Name: "file6", Size: 5 * GB},
				{Name: "file7", Size: 5 * GB},
				{Name: "file8", Size: 5 * GB},
				{Name: "file9", Size: 5 * GB},
				{Name: "file10", Size: 5 * GB},
				{Name: "file11", Size: 5 * GB},
				{Name: "file12", Size: 5 * GB},
				{Name: "file13", Size: 10 * GB},
				{Name: "file14", Size: 10 * GB},
				{Name: "file15", Size: 20 * GB},
			},
		},
		{
			size: 8 * GB,
			expect: []DirInfo{
				{Name: "file13", Size: 10 * GB},
				{Name: "file14", Size: 10 * GB},
				{Name: "file15", Size: 20 * GB},
			},
		},
		{
			size: 10 * GB,
			expect: []DirInfo{
				{Name: "file13", Size: 10 * GB},
				{Name: "file14", Size: 10 * GB},
				{Name: "file15", Size: 20 * GB},
			},
		},
		{
			size: 11 * GB,
			expect: []DirInfo{
				{Name: "file15", Size: 20 * GB},
			},
		},
	}
	for _, tt := range tests {
		actual := chooseDirList(tt.size)
		assert.Equal(t, tt.expect, actual)
	}
}

type myLocalTask struct {
	task             *entities.Task
	filePath         string
	logPath          string
	fetchTime        time.Time
	fetchDuration    time.Duration
	downloadDuration time.Duration
	scanDuration     time.Duration
	uploadDuration   time.Duration
	updateDuration   time.Duration
	lifeDuration     time.Duration

	downloadInfo *sdk.DownloadInfo
	uploadInfo   *sdk.UploadInfo

	fetchBatchCount int
	scanBatchCount  int

	downloadOk time.Time
	scanOk     time.Time
	uploadOk   time.Time

	waitScanTime     time.Duration
	waitDownloadTime time.Duration
	waitUploadTime   time.Duration
	waitUpdateTime   time.Duration

	totalTime time.Duration
	lastTime  time.Duration
}

type localTask []*myLocalTask

// Len 为集合内元素的总数
func (this localTask) Len() int {
	return len(this)
}

// Less 返回索引为 i 的元素是否应排在索引为 j 的元素之前。
func (this localTask) Less(i, j int) bool {
	return this[i].task.Size < this[j].task.Size
}

// Swap 交换索引为 i 和 j 的元素
func (this localTask) Swap(i, j int) {
	this[i], this[j] = this[j], this[i]
}

func NewScanOperation(path, parameter string) scanadapter.ScanOperation {
	return scanadapter.ScanOperation{Path: path, Parameter: parameter}
}
func SortScanOperationByPath(operations []scanadapter.ScanOperation) {
	sort.Slice(operations, func(i, j int) bool { return operations[i].Path < operations[j].Path })
}

// 测试不是最高优先级的tasks（有指定版本的也有没有指定版本的）
func TestGetNoPriority(t *testing.T) {
	h := helper.NewUpdateHelper("testfile")
	s := new(scan)
	input := make(map[string]*LocalTask)
	cases := []struct {
		name      string
		priority  entities.Priority
		parameter string
	}{
		{name: "task1-path", parameter: "parameter1", priority: entities.Priority_VERY_HIGH},
		{name: "task2-path", parameter: "parameter2", priority: entities.Priority_VERY_HIGH},
		{name: "task3-path", parameter: "parameter3", priority: entities.Priority_VERY_HIGH},
		{name: "task4-path", parameter: "parameter4", priority: entities.Priority_VERY_HIGH},
		{name: "task5-path", parameter: "parameter5", priority: entities.Priority_VERY_HIGH},
		{name: "task6-path", parameter: "parameter6", priority: entities.Priority_VERY_HIGH},
		{name: "task7-path", parameter: "parameter7", priority: entities.Priority_VERY_HIGH},
	}
	input["task1-path"] = &LocalTask{
		task: &entities.Task{
			Priority:       cases[0].priority,
			Parameter:      cases[0].parameter,
			PatternVersion: "20210203200916",
			EngineVersion:  "20210203200916",
		},
	}

	input["task3-path"] = &LocalTask{
		task: &entities.Task{
			Priority:       cases[2].priority,
			Parameter:      cases[2].parameter,
			PatternVersion: "20210204130741",
			EngineVersion:  "20210204130741",
		},
	}

	input["task7-path"] = &LocalTask{
		task: &entities.Task{
			Priority:       cases[6].priority,
			Parameter:      cases[6].parameter,
			PatternVersion: "20210205190005",
			EngineVersion:  "20210205190005",
		},
	}

	// 索引为奇数不需要特定版本，否则需要特定版本
	for _, v := range cases {
		if v.name != "task1-path" && v.name != "task3-path" && v.name != "task7-path" {
			input[v.name] = &LocalTask{
				task: &entities.Task{
					Priority:       v.priority,
					Parameter:      v.parameter,
					PatternVersion: "",
					EngineVersion:  "",
				},
			}
		}
	}
	expect := make(map[string][]scanadapter.ScanOperation)
	expect[""] = []scanadapter.ScanOperation{
		NewScanOperation("task2-path", "parameter2"),
		NewScanOperation("task4-path", "parameter4"),
		NewScanOperation("task5-path", "parameter5"),
		NewScanOperation("task6-path", "parameter6"),
	}
	expect["20210203200916+20210203200916"] = []scanadapter.ScanOperation{
		NewScanOperation("task1-path", "parameter1"),
	}
	expect["20210204130741+20210204130741"] = []scanadapter.ScanOperation{
		NewScanOperation("task3-path", "parameter3"),
	}
	expect["20210205190005+20210205190005"] = []scanadapter.ScanOperation{
		NewScanOperation("task7-path", "parameter7"),
	}

	for len(input) > 0 {
		speVersion, err := s.getUnitUpdateScanMap(input, h)
		if err != nil {
			t.Errorf("get scan scan map error: %v", err)
		}
		log.Debugf("speVersion: %+v", speVersion)
		for version, operations := range speVersion {
			SortScanOperationByPath(operations)
			assert.Equal(t, expect[version], operations)

			DeleteTaskFromScanOperations(input, operations)
		}
	}
}

// 测试非统一升级鉴定器，获取默认scanMap是否正常,高优先级和Other优先级同时存在的时候，应该先返回高优先级的后返回Other优先级的
func TestGetHighPriorityByDefaultScanMap(t *testing.T) {
	input := make(map[string]*LocalTask)
	cases := []struct {
		name      string
		priority  entities.Priority
		parameter string
	}{
		{name: "task1-path", parameter: "parameter1", priority: entities.Priority_VERY_HIGH},
		{name: "task2-path", parameter: "parameter2", priority: entities.Priority_VERY_HIGH},
		{name: "task3-path", parameter: "parameter3", priority: entities.Priority_VERY_HIGH},
		{name: "task4-path", parameter: "parameter4", priority: entities.Priority_VERY_HIGH},
		{name: "task5-path", parameter: "parameter5", priority: entities.Priority_LOW},
		{name: "task6-path", parameter: "parameter6", priority: entities.Priority_HIGH},
		{name: "task7-path", parameter: "parameter7", priority: entities.Priority_NORMAL},
	}
	input["task1-path"] = &LocalTask{
		task: &entities.Task{
			Priority:       cases[0].priority,
			Parameter:      cases[0].parameter,
			PatternVersion: "20210203200916",
			EngineVersion:  "20210203200916",
		},
	}

	input["task3-path"] = &LocalTask{
		task: &entities.Task{
			Priority:       cases[2].priority,
			Parameter:      cases[2].parameter,
			PatternVersion: "20210204130741",
			EngineVersion:  "20210204130741",
		},
	}

	input["task7-path"] = &LocalTask{
		task: &entities.Task{
			Priority:       cases[6].priority,
			Parameter:      cases[6].parameter,
			PatternVersion: "20210205190005",
			EngineVersion:  "20210205190005",
		},
	}

	// 索引为奇数不需要特定版本，否则需要特定版本
	for _, v := range cases {
		if v.name != "task1-path" && v.name != "task3-path" && v.name != "task7-path" {
			input[v.name] = &LocalTask{
				task: &entities.Task{
					Priority:       v.priority,
					Parameter:      v.parameter,
					PatternVersion: "",
					EngineVersion:  "",
				},
			}
		}
	}
	expect1 := []scanadapter.ScanOperation{
		NewScanOperation("task1-path", "parameter1"),
		NewScanOperation("task2-path", "parameter2"),
		NewScanOperation("task3-path", "parameter3"),
		NewScanOperation("task4-path", "parameter4"),
	}
	expect2 := []scanadapter.ScanOperation{
		NewScanOperation("task5-path", "parameter5"),
		NewScanOperation("task6-path", "parameter6"),
		NewScanOperation("task7-path", "parameter7"),
	}

	// 优先返回高优先级任务的输入列表：scanMap
	scanMap := defaultScanOperations(input)
	SortScanOperationByPath(scanMap)
	assert.Equal(t, expect1, scanMap)
	DeleteTaskFromScanOperations(input, scanMap)
	//for _, v := range scanMap {
	//	delete(input, v.Path)
	//}
	// 返回高其他优先级任务的输入列表：scanMap
	scanMap = defaultScanOperations(input)
	SortScanOperationByPath(scanMap)
	assert.Equal(t, expect2, scanMap)
}

// 测试非统一升级鉴定器，获取默认scanMap是否正常,并且任务有高优先级的，也有低优先级的
func TestGetOtherPriorityByDefaultScanMap(t *testing.T) {
	input := make(map[string]*LocalTask)
	cases := []struct {
		name      string
		priority  entities.Priority
		parameter string
	}{
		{name: "task1-path", parameter: "parameter1", priority: entities.Priority_VERY_HIGH},
		{name: "task2-path", parameter: "parameter2", priority: entities.Priority_VERY_HIGH},
		{name: "task3-path", parameter: "parameter3", priority: entities.Priority_VERY_HIGH},
		{name: "task4-path", parameter: "parameter4", priority: entities.Priority_HIGH},
		{name: "task5-path", parameter: "parameter5", priority: entities.Priority_LOW},
		{name: "task6-path", parameter: "parameter6", priority: entities.Priority_HIGH},
		{name: "task7-path", parameter: "parameter7", priority: entities.Priority_LOW},
	}
	input["task1-path"] = &LocalTask{
		task: &entities.Task{
			Priority:       cases[0].priority,
			Parameter:      cases[0].parameter,
			PatternVersion: "20210203200916",
			EngineVersion:  "20210203200916",
		},
	}

	input["task3-path"] = &LocalTask{
		task: &entities.Task{
			Priority:       cases[2].priority,
			Parameter:      cases[2].parameter,
			PatternVersion: "20210204130741",
			EngineVersion:  "20210204130741",
		},
	}

	input["task7-path"] = &LocalTask{
		task: &entities.Task{
			Priority:       cases[6].priority,
			Parameter:      cases[6].parameter,
			PatternVersion: "20210205190005",
			EngineVersion:  "20210205190005",
		},
	}

	// 索引为奇数不需要特定版本，否则需要特定版本
	for _, v := range cases {
		if v.name != "task1-path" && v.name != "task3-path" && v.name != "task7-path" {
			input[v.name] = &LocalTask{
				task: &entities.Task{
					Priority:       v.priority,
					Parameter:      v.parameter,
					PatternVersion: "",
					EngineVersion:  "",
				},
			}
		}
	}
	expect1 := []scanadapter.ScanOperation{
		NewScanOperation("task1-path", "parameter1"),
		NewScanOperation("task2-path", "parameter2"),
		NewScanOperation("task3-path", "parameter3"),
	}

	expect2 := []scanadapter.ScanOperation{
		NewScanOperation("task4-path", "parameter4"),
		NewScanOperation("task5-path", "parameter5"),
		NewScanOperation("task6-path", "parameter6"),
		NewScanOperation("task7-path", "parameter7"),
	}

	// 只会返回高优先级scanMap
	scanMap := defaultScanOperations(input)
	SortScanOperationByPath(scanMap)
	t.Logf("high priority scan map: %v", scanMap)
	assert.Equal(t, expect1, scanMap)
	//for _, v := range scanMap {
	//	delete(input, v.Path)
	//}
	DeleteTaskFromScanOperations(input, scanMap)
	// 第二次调用才会返回其他优先级的scanMap
	scanMap = defaultScanOperations(input)
	SortScanOperationByPath(scanMap)
	t.Logf("other priority scan map: %v", scanMap)
	assert.Equal(t, expect2, scanMap)
	for _, v := range scanMap {
		delete(input, v.Path)
	}
}
func DeleteTaskFromScanOperations(srcTasks map[string]*LocalTask, operations []scanadapter.ScanOperation) {
	for _, v := range operations {
		delete(srcTasks, v.Path)
	}
}

func Test_scan_getScanTimeout(t *testing.T) {
	mockAsset := mock_controllers.NewMockAssetController(gomock.NewController(t))
	type fields struct {
		assetController controllers.AssetController
	}
	type args struct {
		scanMap        []scanadapter.ScanOperation
		scanLocalTasks map[string]*LocalTask
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   time.Duration
		want1  time.Duration
	}{
		{
			name: "test1",
			fields: fields{
				assetController: mockAsset,
			},
			args: args{
				scanMap: []scanadapter.ScanOperation{
					{
						Path:      "file1",
						Parameter: "task1",
					},
					{
						Path:      "file2",
						Parameter: "task2",
					},
					{
						Path:      "file3",
						Parameter: "task3",
					},
				},
				// scanMap: map[string]string{"file1": "task1", "file2": "task2", "file3": "task3"},
				scanLocalTasks: map[string]*LocalTask{
					"file1": {
						task: &entities.Task{
							Name: "test_task1",
						},
						lastTime: time.Duration(30) * time.Second,
					},
					"file2": {
						task: &entities.Task{
							Name: "test_task2",
						},
						lastTime: time.Duration(60) * time.Second,
					},
					"file3": {
						task: &entities.Task{
							Name: "test_task3",
						},
						lastTime: time.Duration(90) * time.Second,
					},
				},
			},
			want:  time.Duration(float64(90)*0.5) * time.Second,
			want1: time.Duration(90) * time.Second,
		},
		{
			name: "test2",
			fields: fields{
				assetController: mockAsset,
			},
			args: args{
				scanMap: []scanadapter.ScanOperation{
					{
						Path:      "file1",
						Parameter: "task1",
					},
					{
						Path:      "file2",
						Parameter: "task2",
					},
					{
						Path:      "file3",
						Parameter: "task3",
					},
				},
				// scanMap: map[string]string{"file1": "task1", "file2": "task2", "file3": "task3"},
				scanLocalTasks: map[string]*LocalTask{
					"file1": {
						task: &entities.Task{
							Name: "test_task1",
						},
						lastTime: time.Duration(30) * time.Second,
					},
					"file2": {
						task: &entities.Task{
							Name: "test_task2",
						},
						lastTime: time.Duration(60) * time.Second,
					},
					"file3": {
						task: &entities.Task{
							Name: "test_task3",
						},
						lastTime: time.Duration(200) * time.Second,
					},
				},
			},
			want:  time.Duration(90) * time.Second,
			want1: time.Duration(200) * time.Second,
		},
	}
	mockAsset.EXPECT().GetAsset().Return(&entities.Asset{ScanFeature: &entities.ScanFeature{MaxScanTimeout: int64(math.Pow10(9) * 90)}}).Times(3)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &scan{
				assetController: tt.fields.assetController,
			}
			got, got1 := s.getScanTimeout(tt.args.scanMap, tt.args.scanLocalTasks)
			fmt.Printf("gotScanTimeout = %v, gotMaxLastTime = %v\n", got, got1)
			if got != tt.want {
				t.Errorf("getScanTimeout() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("getScanTimeout() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func pushToTaskChan(ch chan *LocalTask) {
	for i := 1; i <= 10; i++ {
		task := &LocalTask{
			task: &entities.Task{
				Name:        "task" + fmt.Sprintf("%03d", i),
				Size:        8 * uint64(i) * MB,
				ScanTimeout: int64(math.Pow10(9) * 60),
			},
		}
		fmt.Printf("push number %v task into taskChan, size:%vMB\n", i, 5*uint64(i))
		ch <- task
		time.Sleep(100 * time.Millisecond)
	}
	//task.name:task1, task.size:8M, task.max_scan_timeout: 60, totalTime:3m0s, lastTime:2m57s
	//task.name:task10, task.size:80M, task.max_scan_timeout: 60, totalTime:6m20s, lastTime:6m17s
	//task.name:task9, task.size:72M, task.max_scan_timeout: 60, totalTime:5m48s, lastTime:5m45s
	//task.name:task8, task.size:64M, task.max_scan_timeout: 60, totalTime:5m16s, lastTime:5m13s
	//task.name:task7, task.size:56M, task.max_scan_timeout: 60, totalTime:4m44s, lastTime:4m41s
	//task.name:task6, task.size:48M, task.max_scan_timeout: 60, totalTime:4m12s, lastTime:4m9s
	//task.name:task5, task.size:40M, task.max_scan_timeout: 60, totalTime:3m40s, lastTime:3m37s
	//task.name:task4, task.size:32M, task.max_scan_timeout: 60, totalTime:3m8s, lastTime:3m5s
	//task.name:task3, task.size:24M, task.max_scan_timeout: 60, totalTime:3m0s, lastTime:2m57s
	//task.name:task2, task.size:16M, task.max_scan_timeout: 60, totalTime:3m0s, lastTime:2m57s
}

func listResult(ch chan *LocalTask) {
	for v := range ch {
		fmt.Printf("%+v, %+v, %+v\n", v.task, v.lastTime, v.totalTime)
		time.Sleep(100 * time.Millisecond)
	}
}

func Test_scan_loopDownload(t *testing.T) {
	a := assert.New(t)
	expects := []*myLocalTask{}
	var got localTask
	for i := 1; i <= 10; i++ {
		expects = append(expects, &myLocalTask{
			task: &entities.Task{
				Name:        "task" + fmt.Sprintf("%03d", i),
				Size:        uint64(i) * 8 * MB,
				ScanTimeout: int64(60),
			},
			totalTime: util.GetDownloadLadderTime(uint64(i)*8*MB)*time.Second*2 + time.Duration(math.Pow10(9)*60),
			lastTime:  util.GetDownloadLadderTime(uint64(i)*8*MB)*time.Second*2 + time.Duration(math.Pow10(9)*60) - 3*time.Second,
		})
	}

	pg := sm.Patch(util.GetStorageSize, func(uri string) (int64, error) {
		//return 11, nil
		return 0, fmt.Errorf("just return an error")
	})
	defer pg.Unpatch()
	mockAsset := mock_controllers.NewMockAssetController(gomock.NewController(t))
	mockStorage := mock_storage.NewMockStorage(gomock.NewController(t))
	mockAsset.EXPECT().GetAsset().Return(&entities.Asset{ScanFeature: &entities.ScanFeature{MaxScanTimeout: int64(math.Pow10(9) * 60)}}).AnyTimes()
	mockStorage.EXPECT().Download(gomock.Any(), gomock.Any()).DoAndReturn(func(a1 interface{},
		a2 interface{}) (*sdk.DownloadInfo, error) {
		fmt.Printf("mock Download")
		time.Sleep(2 * time.Second)
		return &sdk.DownloadInfo{Content: []byte("download_ok")}, nil
	}).AnyTimes()

	pool, err := ants.NewPool(5)
	if err != nil {
		log.Errorf("failed to get new pool for storage, err: %v", err)
		assert.Equal(t, err, nil)
	}
	pool.Running()
	defer pool.Release()
	s := &scan{
		LoopController:    util.LoopController{},
		assetController:   mockAsset,
		storageController: mockStorage,
		taskChan:          make(chan *LocalTask, 10),
		resultChan:        make(chan *LocalTask, 10),
		filePQueue:        pqueue.New(pqueue.Size(10)),
		fileDir:           "test_file_dir",
		logDir:            "test_log_dir",
		storagePool:       pool,
	}
	go pushToTaskChan(s.taskChan)
	go listResult(s.resultChan)
	go s.loopDownload()
	time.Sleep(5 * time.Second)
	for s.filePQueue.Len() != 0 {
		v := s.filePQueue.Pop().(*LocalTask)
		got = append(got, &myLocalTask{task: v.task, totalTime: v.totalTime, lastTime: v.lastTime})
		fmt.Printf("task.name:%v, task.size:%vM, task.max_scan_timeout: %v, totalTime:%v, lastTime:%v\n",
			v.task.Name, v.task.Size/MB, v.task.ScanTimeout, v.totalTime, v.lastTime/time.Second*time.Second)
	}
	fmt.Printf("expect:\n")
	for _, v := range expects {
		fmt.Printf("task.name:%v, task.size:%vM, task.max_scan_timeout: %v, totalTime:%v, lastTime:%v\n",
			v.task.Name, v.task.Size/MB, v.task.ScanTimeout, v.totalTime, v.lastTime/time.Second*time.Second)
	}
	fmt.Printf("actual:\n")
	sort.Slice(got, func(i, j int) bool { return got[i].task.Name < got[j].task.Name })
	for _, v := range got {
		fmt.Printf("task.name:%v, task.size:%vM, task.max_scan_timeout: %v, totalTime:%v, lastTime:%v\n",
			v.task.Name, v.task.Size/MB, v.task.ScanTimeout, v.totalTime, v.lastTime/time.Second*time.Second)
	}
	a.Equal(len(expects), len(got))
	// 只能确定比较出总时间肯定是准的，lastTime无法控制确定，因为lastTime是（总时间-下载时间），但是下载过程的时间统计是随着实际情况变化的；
	//fmt.Printf("got:%+v\n", got)
	//for i := 0; i < 10; i++ {
	//	a.Equal(got[i].totalTime, expects[i].totalTime)
	//}
}

func TestChooseStorage(t *testing.T) {
	defer framework.Init()()
	a := assert.New(t)
	var t1 entities.Task
	// 扫描结果文件size小于200的情况,采用message上传
	t1.StoreUri = "message||files/a806157e5f10ef20ce5b2f9995acaa54d75fc52f/logs/qowl-apk/storage"
	a.Equal(true, proxy.ChooseStorage(&t1, "./.gitignore"))
	a.Equal("message", t1.StoreUri)

	// 扫描结果文件size大于于200的情况，采用具体的uri上传
	t1.StoreUri = "message||files/a806157e5f10ef20ce5b2f9995acaa54d75fc52f/logs/qowl-apk/storage"
	a.Equal(false, proxy.ChooseStorage(&t1, "./coverage.out"))
	a.Equal("files/a806157e5f10ef20ce5b2f9995acaa54d75fc52f/logs/qowl-apk/storage", t1.StoreUri)

	// 扫描结果文件不存在的情况，直接用给定的uri
	t1.StoreUri = "message||files/a806157e5f10ef20ce5b2f9995acaa54d75fc52f/logs/qowl-apk/storage"
	a.Equal(false, proxy.ChooseStorage(&t1, "./notfound"))
	t.Logf("选择处理后的store_uri:%v\n", t1.StoreUri)
	a.Equal("files/a806157e5f10ef20ce5b2f9995acaa54d75fc52f/logs/qowl-apk/storage", t1.StoreUri)

	// 扫描结果文件size小于200的情况
	t1.StoreUri = "message:base64||files/a806157e5f10ef20ce5b2f9995acaa54d75fc52f/logs/qowl-apk/storage"
	a.Equal(true, proxy.ChooseStorage(&t1, "./.gitignore"))
	a.Equal("message:base64", t1.StoreUri)

	// 扫描结果文件size大于于200的情况,扫描结果文件的内容如果大于200则：ChooseStorage返回false
	t1.StoreUri = "message:base64||files/a806157e5f10ef20ce5b2f9995acaa54d75fc52f/logs/qowl-apk/storage"
	a.Equal(false, proxy.ChooseStorage(&t1, "./coverage.out"))
	a.Equal("files/a806157e5f10ef20ce5b2f9995acaa54d75fc52f/logs/qowl-apk/storage", t1.StoreUri)
}

func TestReload(t *testing.T) {
	h := helper.NewUpdateHelper("testfile")
	mockScanner := mock_controllers.NewMockScannerController(gomock.NewController(t))
	mockScanner.EXPECT().Reload(gomock.Any()).Return(&scanadapter.Version{EngineVersion: "version2021", PatternVersion: "version2021"}, nil).AnyTimes()
	var s = &scan{
		helper:            h,
		scannerController: mockScanner,
	}

	err := s.reload("", "version2021", "version2021")
	if err != nil {
		t.Errorf("reload() error = %v", err)
	} else {
		t.Log("reload success\n")
	}
}

// 测试获取升级平均时间是否正确
func TestGetAvgUpdateTime(t *testing.T) {
	mockAsset := mock_controllers.NewMockAssetController(gomock.NewController(t))
	mockAsset.EXPECT().GetAsset().Return(&entities.Asset{
		UpdateFeature: &entities.UpdateFeature{
			VersionDepot: &entities.VersionDepot{Versions: []*entities.Version{
				{UpdateDuration: "10.43447611s"},
				{UpdateDuration: "1111.446691518s"},
				{UpdateDuration: "11.505317922s"},
				{UpdateDuration: "65.506086934s"},
				{UpdateDuration: "7.383010141s"},
				{UpdateDuration: "12.205014231s"},
			},
			},
		},
	})
	expect := (10 + 1111 + 11 + 65 + 7 + 12) / 6
	s := &scan{assetController: mockAsset}
	avgTime := s.getAvgUpdateTime()
	t.Logf("avg time: %v", avgTime)
	assert.Equal(t, expect, avgTime)

}

func TestSelfUpdate(t *testing.T) {
	mockAsset := mock_controllers.NewMockAssetController(gomock.NewController(t))
	mockScanner := mock_controllers.NewMockScannerController(gomock.NewController(t))
	// 自升级鉴定器调用，正常升级返回升级后的版本信息，输出结果应为：
	// update succeed when scanner restart, version: engine_version:"2.4.2.39" pattern_version:"2021.02.02 1030"
	mockAsset.EXPECT().GetAsset().Return(&entities.Asset{
		UpdateFeature: &entities.UpdateFeature{SelfUpdateInterval: 1},
	}).AnyTimes()
	mockScanner.EXPECT().Update().Return(&scanadapter.Version{
		PatternVersion: "2021.02.02 1030",
		EngineVersion:  "2.4.2.39",
	}, nil).AnyTimes()
	s := &scan{
		assetController:   mockAsset,
		scannerController: mockScanner}
	s.selfUpdate()

	// 非自升级的情况则什么也不会输出
	mockAsset1 := mock_controllers.NewMockAssetController(gomock.NewController(t))
	mockAsset1.EXPECT().GetAsset().Return(&entities.Asset{
		UpdateFeature: &entities.UpdateFeature{SelfUpdateInterval: 0},
	}).AnyTimes()
	s1 := &scan{
		assetController:   mockAsset1,
		scannerController: mockScanner}
	s1.selfUpdate()
}

// 测试批量扫描使用统一升级类型的鉴定器
func TestBatchScanByUnitUpdateScanner(t *testing.T) {
	input := make(map[string]*LocalTask)
	cases := []struct {
		name      string
		priority  entities.Priority
		parameter string
	}{
		{name: "task1-path", parameter: "parameter1", priority: entities.Priority_VERY_HIGH},
		{name: "task2-path", parameter: "parameter2", priority: entities.Priority_VERY_HIGH},
		{name: "task3-path", parameter: "parameter3", priority: entities.Priority_VERY_HIGH},
		{name: "task4-path", parameter: "parameter4", priority: entities.Priority_HIGH},
		{name: "task5-path", parameter: "parameter5", priority: entities.Priority_HIGH},
	}
	input["task1-path"] = &LocalTask{
		task: &entities.Task{
			Name:           "task1",
			Priority:       cases[0].priority,
			Parameter:      cases[0].parameter,
			PatternVersion: "20210205190005",
			EngineVersion:  "20210205190005",
		},
		lastTime: time.Second * 100,
	}

	input["task2-path"] = &LocalTask{
		task: &entities.Task{
			Name:           "task2",
			Priority:       cases[1].priority,
			Parameter:      cases[1].parameter,
			PatternVersion: "20210205190005",
			EngineVersion:  "20210205190005",
		},
		lastTime: time.Second * 100,
	}

	input["task3-path"] = &LocalTask{
		task: &entities.Task{
			Name:           "task3",
			Priority:       cases[2].priority,
			Parameter:      cases[2].parameter,
			PatternVersion: "20210205190005",
			EngineVersion:  "20210205190005",
		},
		lastTime: time.Second * 100,
	}

	input["task4-path"] = &LocalTask{
		task: &entities.Task{
			Name:           "task4",
			Priority:       cases[3].priority,
			Parameter:      cases[3].parameter,
			PatternVersion: "",
			EngineVersion:  "",
		},
		lastTime: time.Second * 100,
	}
	input["task5-path"] = &LocalTask{
		task: &entities.Task{
			Name:           "task5",
			Priority:       cases[4].priority,
			Parameter:      cases[4].parameter,
			PatternVersion: "",
			EngineVersion:  "",
		},
		lastTime: time.Second * 100,
	}
	mockScanner := mock_controllers.NewMockScannerController(gomock.NewController(t))
	mockVloader := mock_controllers.NewMockVloaderController(gomock.NewController(t))
	mockAsset := mock_controllers.NewMockAssetController(gomock.NewController(t))
	mockAsset.EXPECT().GetAsset().Return(&entities.Asset{
		UpdateFeature: &entities.UpdateFeature{
			UpdaterInterval:   30 * time.Second.Nanoseconds() / 1e9,
			UpdaterVolumeSize: 500 * MB,
			Kind:              entities.UpgradeKind_UPDATER,
		},
		ScanFeature: &entities.ScanFeature{MaxScanCount: 5, MaxScanTimeout: 50 * time.Second.Nanoseconds()},
	}).AnyTimes()

	latestVersion := "20210206160005"
	mockVloader.EXPECT().DownloadVirusLibrary(gomock.Any(), gomock.Any()).DoAndReturn(
		func(engineVersion, patternVersion string) (dir, engine, pattern string, err error) {
			t.Logf("DownloadVirusLibrary %v %v", engineVersion, patternVersion)
			if len(engineVersion) == 0 && len(patternVersion) == 0 {
				return "virdb_latest", latestVersion, latestVersion, nil
			}
			return "virdb_" + patternVersion, engineVersion, patternVersion, nil
		}).Times(2)

	mockScanner.EXPECT().Scan(gomock.Any(), gomock.Any()).DoAndReturn(func(a1 context.Context,
		scanMap []scanadapter.ScanOperation) (*util.ScanResult, error) {
		time.Sleep(time.Second)
		t.Logf("参数scanMap: %+v", scanMap)
		if len(scanMap) == 3 {
			return &util.ScanResult{
				Result: map[string]*kamala.ScanResult{
					"task1-path": {Name: "task1", Code: kamala.ScanCode_OK, Version: &kamala.Version{EngineVersion: "20210205190005", PatternVersion: "20210205190005"}},
					"task2-path": {Name: "task2", Code: kamala.ScanCode_OK, Version: &kamala.Version{EngineVersion: "20210205190005", PatternVersion: "20210205190005"}},
					"task3-path": {Name: "task3", Code: kamala.ScanCode_OK, Version: &kamala.Version{EngineVersion: "20210205190005", PatternVersion: "20210205190005"}},
				},
			}, nil
		} else {
			return &util.ScanResult{
				Result: map[string]*kamala.ScanResult{
					"task4-path": {Name: "task4", Code: kamala.ScanCode_OK, Version: &kamala.Version{EngineVersion: latestVersion, PatternVersion: latestVersion}},
					"task5-path": {Name: "task5", Code: kamala.ScanCode_OK, Version: &kamala.Version{EngineVersion: latestVersion, PatternVersion: latestVersion}},
				},
			}, nil
		}
	}).AnyTimes()

	mockScanner.EXPECT().Reload(gomock.Any()).DoAndReturn(
		func(dir string) (*scanadapter.Version, error) {
			if dir == "virdb_latest" {
				return &scanadapter.Version{
					PatternVersion: latestVersion,
					EngineVersion:  latestVersion,
				}, nil
			}
			version := strings.TrimPrefix(dir, "virdb_")
			return &scanadapter.Version{
				PatternVersion: version,
				EngineVersion:  version,
			}, nil
		}).AnyTimes()

	mockScanner.EXPECT().GetScanner().Return(&entities.Scanner{Version: &entities.Version{Engine: "20210205190005", Pattern: "20210205190005"}}).AnyTimes()
	mockScanner.EXPECT().Ping().Return(&scanadapter.PingResponse{Version: &scanadapter.Version{PatternVersion: "20210205190005", EngineVersion: "20210205190005"}}, nil).AnyTimes()

	s := &scan{
		resultChan:        make(chan *LocalTask, 5),
		logChan:           make(chan *LocalTask, 5),
		LoopController:    util.LoopController{Stop: false},
		helper:            helper.NewUpdateHelper("testfile"),
		scannerController: mockScanner,
		assetController:   mockAsset,
		vloaderController: mockVloader,
	}
	// 第一次调用只会将最高优先级的任务扫掉
	s.batchScan(false, input)
	// 第二次调用只会将低优先级的任务取出来扫描
	s.batchScan(true, input)
}

// 测试批量扫描使用统一升级类型的鉴定器
func TestBatchScanByNotUnitUpdateScanner(t *testing.T) {
	input := make(map[string]*LocalTask)
	cases := []struct {
		name      string
		priority  entities.Priority
		parameter string
	}{
		{name: "task1-path", parameter: "parameter1", priority: entities.Priority_VERY_HIGH},
		{name: "task2-path", parameter: "parameter2", priority: entities.Priority_VERY_HIGH},
		{name: "task3-path", parameter: "parameter3", priority: entities.Priority_VERY_HIGH},
		{name: "task4-path", parameter: "parameter4", priority: entities.Priority_HIGH},
		{name: "task5-path", parameter: "parameter5", priority: entities.Priority_HIGH},
	}
	input["task1-path"] = &LocalTask{
		task: &entities.Task{
			Name:           "task1",
			Priority:       cases[0].priority,
			Parameter:      cases[0].parameter,
			PatternVersion: "20210205190005",
			EngineVersion:  "20210205190005",
		},
		lastTime: time.Second * 100,
	}

	input["task2-path"] = &LocalTask{
		task: &entities.Task{
			Name:           "task2",
			Priority:       cases[1].priority,
			Parameter:      cases[1].parameter,
			PatternVersion: "20210205190005",
			EngineVersion:  "20210205190005",
		},
		lastTime: time.Second * 100,
	}

	input["task3-path"] = &LocalTask{
		task: &entities.Task{
			Name:           "task3",
			Priority:       cases[2].priority,
			Parameter:      cases[2].parameter,
			PatternVersion: "20210205190005",
			EngineVersion:  "20210205190005",
		},
		lastTime: time.Second * 100,
	}

	input["task4-path"] = &LocalTask{
		task: &entities.Task{
			Name:           "task4",
			Priority:       cases[3].priority,
			Parameter:      cases[3].parameter,
			PatternVersion: "",
			EngineVersion:  "",
		},
		lastTime: time.Second * 100,
	}
	input["task5-path"] = &LocalTask{
		task: &entities.Task{
			Name:           "task5",
			Priority:       cases[4].priority,
			Parameter:      cases[4].parameter,
			PatternVersion: "",
			EngineVersion:  "",
		},
		lastTime: time.Second * 100,
	}
	mockScanner := mock_controllers.NewMockScannerController(gomock.NewController(t))
	mockAsset := mock_controllers.NewMockAssetController(gomock.NewController(t))
	mockAsset.EXPECT().GetAsset().Return(&entities.Asset{
		UpdateFeature: &entities.UpdateFeature{
			SelfUpdateInterval: 30 * time.Second.Nanoseconds() / 1e9,
			UpdaterVolumeSize:  500 * MB,
		},
		ScanFeature: &entities.ScanFeature{MaxScanCount: 5, MaxScanTimeout: 50 * time.Second.Nanoseconds()},
	}).AnyTimes()

	mockScanner.EXPECT().Scan(gomock.Any(), gomock.Any()).DoAndReturn(func(a1 context.Context,
		scanMap []scanadapter.ScanOperation) (*util.ScanResult, error) {
		time.Sleep(time.Second)
		t.Logf("参数scanMap: %+v", scanMap)
		if len(scanMap) == 3 {
			return &util.ScanResult{
				Result: map[string]*kamala.ScanResult{
					"task1-path": {Name: "task1", Code: kamala.ScanCode_OK, Version: &kamala.Version{EngineVersion: "20210205190005", PatternVersion: "20210205190005"}},
					"task2-path": {Name: "task2", Code: kamala.ScanCode_OK, Version: &kamala.Version{EngineVersion: "20210205190005", PatternVersion: "20210205190005"}},
					"task3-path": {Name: "task3", Code: kamala.ScanCode_OK, Version: &kamala.Version{EngineVersion: "20210205190005", PatternVersion: "20210205190005"}},
				},
			}, nil
		} else {
			return &util.ScanResult{
				Result: map[string]*kamala.ScanResult{
					"task4-path": {Name: "task4", Code: kamala.ScanCode_OK, Version: &kamala.Version{EngineVersion: "", PatternVersion: ""}},
					"task5-path": {Name: "task5", Code: kamala.ScanCode_OK, Version: &kamala.Version{EngineVersion: "", PatternVersion: ""}},
				},
			}, nil
		}
	}).AnyTimes()

	mockScanner.EXPECT().Reload(gomock.Any()).Return(&scanadapter.Version{
		PatternVersion: "20210205190005",
		EngineVersion:  "20210205190005",
	}, nil).AnyTimes()

	mockScanner.EXPECT().GetScanner().Return(&entities.Scanner{Version: &entities.Version{Engine: "20210205190005", Pattern: "20210205190005"}}).AnyTimes()
	mockScanner.EXPECT().Ping().Return(&scanadapter.PingResponse{Version: &scanadapter.Version{PatternVersion: "20210205190005", EngineVersion: "20210205190005"}}, nil).AnyTimes()

	s := &scan{
		resultChan:        make(chan *LocalTask, 5),
		logChan:           make(chan *LocalTask, 5),
		LoopController:    util.LoopController{Stop: false},
		helper:            helper.NewUpdateHelper("testfile"),
		scannerController: mockScanner,
		assetController:   mockAsset,
	}
	// 第一次调用只会将最高优先级的任务扫掉
	s.batchScan(false, input)
	// 第二次调用只会将低优先级的任务取出来扫描
	s.batchScan(true, input)
}

func TestRestartInit(t *testing.T) {
	mockScanner := mock_controllers.NewMockScannerController(gomock.NewController(t))
	mockAsset := mock_controllers.NewMockAssetController(gomock.NewController(t))
	mockAsset.EXPECT().GetAsset().Return(&entities.Asset{
		UpdateFeature: &entities.UpdateFeature{SelfUpdateInterval: 1},
	}).AnyTimes()
	mockScanner.EXPECT().Update().Return(&scanadapter.Version{
		PatternVersion: "2021.02.02 1030",
		EngineVersion:  "2.4.2.39",
	}, nil).AnyTimes()
	mockScanner.EXPECT().Ping().Return(&scanadapter.PingResponse{
		Name:           "owl",
		StartupSeconds: 1,
		Version:        &scanadapter.Version{EngineVersion: "", PatternVersion: ""},
	}, nil).AnyTimes()
	mockScanner.EXPECT().SetState(gomock.Any())
	mockScanner.EXPECT().GetState().Return(int32(util.IDLE))

	s := &scan{
		LoopController:    util.LoopController{Stop: false},
		helper:            helper.NewUpdateHelper("testfile"),
		scannerController: mockScanner,
		assetController:   mockAsset,
		preStartupTime:    time.Now().Unix(),
	}
	time.Sleep(time.Second * 3)
	s.restartInit()
}

// 测试一切正常的情况下的：FetchTask
func TestFetchTask(t *testing.T) {
	mockAsset := mock_controllers.NewMockAssetController(gomock.NewController(t))
	mockScanner := mock_controllers.NewMockScannerController(gomock.NewController(t))
	mockTask := mock_controllers.NewMockTaskController(gomock.NewController(t))

	mockTask.EXPECT().FetchTask(gomock.Any(), gomock.Any()).DoAndReturn(func(name string, count int32) []*entities.Task {
		ret := []*entities.Task{}
		switch count {
		case 1:
			ret = []*entities.Task{{Name: "test_task1", Priority: entities.Priority_VERY_HIGH}}
		case 2:
			ret = []*entities.Task{{Name: "test_task1", Priority: entities.Priority_VERY_HIGH},
				{Name: "test_task2", Priority: entities.Priority_HIGH}}
		case 3:
			ret = []*entities.Task{{Name: "test_task1", Priority: entities.Priority_VERY_HIGH},
				{Name: "test_task2", Priority: entities.Priority_HIGH},
				{Name: "test_task3", Priority: entities.Priority_NORMAL}}
		case 4:
			ret = []*entities.Task{
				{Name: "test_task1", Priority: entities.Priority_VERY_HIGH},
				{Name: "test_task2", Priority: entities.Priority_VERY_HIGH},
				{Name: "test_task3", Priority: entities.Priority_NORMAL},
				{Name: "test_task4", Priority: entities.Priority_NORMAL},
			}
		case 5:
			ret = []*entities.Task{
				{Name: "test_task1", Priority: entities.Priority_VERY_HIGH},
				{Name: "test_task2", Priority: entities.Priority_VERY_HIGH},
				{Name: "test_task3", Priority: entities.Priority_NORMAL},
				{Name: "test_task4", Priority: entities.Priority_NORMAL},
				{Name: "test_task5", Priority: entities.Priority_LOW},
			}
		}
		return ret
	}).AnyTimes()
	mockTask.EXPECT().GetMode().Return(int32(0)).AnyTimes()
	mockAsset.EXPECT().GetAsset().Return(&entities.Asset{
		Name:        "jingyun",
		ScanFeature: &entities.ScanFeature{MaxFetchCount: 5, MaxScanCount: 5, MaxScanTimeout: 202 * time.Second.Nanoseconds()},
		UpdateFeature: &entities.UpdateFeature{
			VersionDepot: &entities.VersionDepot{Versions: []*entities.Version{
				{UpdateDuration: "10.43447611s"},
				{UpdateDuration: "1111.446691518s"},
				{UpdateDuration: "11.505317922s"},
				{UpdateDuration: "65.506086934s"},
				{UpdateDuration: "7.383010141s"},
				{UpdateDuration: "12.205014231s"}},
			}},
	}).AnyTimes()
	mockScanner.EXPECT().GetState().Return(int32(util.IDLE)).AnyTimes()

	s := &scan{
		LoopController: util.LoopController{
			Stop: false,
		},
		assetController:   mockAsset,
		scannerController: mockScanner,
		taskController:    mockTask,
		taskCount:         0,
		taskChan:          make(chan *LocalTask, 5),
	}

	s.Run(s.loopFetch)
	tick := time.Tick(500 * time.Millisecond)
	stop := false
	for !stop {
		select {
		case <-tick:
			stop = true
		case v := <-s.taskChan:
			t.Logf("task: %+v, fetch time: %v, fetch duration: %v, fetch batch count: %v", v.task, v.fetchTime,
				v.fetchDuration, v.fetchBatchCount)
			atomic.AddInt32(&s.taskCount, -1)
		}
	}
	s.Finalize()
}

// 测试当状态处于Terminating或Startup状态时，不应该进行FetchTask操作而是做循环等待操作
func TestFetchTaskInTerminating(t *testing.T) {
	mockAsset := mock_controllers.NewMockAssetController(gomock.NewController(t))
	mockScanner := mock_controllers.NewMockScannerController(gomock.NewController(t))
	mockTask := mock_controllers.NewMockTaskController(gomock.NewController(t))

	mockTask.EXPECT().GetMode().Return(int32(0)).AnyTimes()
	mockAsset.EXPECT().GetAsset().Return(&entities.Asset{
		Name:        "jingyun",
		ScanFeature: &entities.ScanFeature{MaxFetchCount: 5, MaxScanCount: 5, MaxScanTimeout: 202 * time.Second.Nanoseconds()},
		UpdateFeature: &entities.UpdateFeature{
			VersionDepot: &entities.VersionDepot{Versions: []*entities.Version{
				{UpdateDuration: "10.43447611s"},
				{UpdateDuration: "1111.446691518s"},
				{UpdateDuration: "11.505317922s"},
				{UpdateDuration: "65.506086934s"},
				{UpdateDuration: "7.383010141s"},
				{UpdateDuration: "12.205014231s"}},
			}},
	}).AnyTimes()
	mockScanner.EXPECT().GetState().Return(int32(util.TERMINATING)).AnyTimes()
	mockScanner.EXPECT().GetScanner().Return(&entities.Scanner{Name: "jingyun"}).AnyTimes()

	s := &scan{
		LoopController: util.LoopController{
			Stop: false,
		},
		assetController:   mockAsset,
		scannerController: mockScanner,
		taskController:    mockTask,
		taskCount:         0,
		taskChan:          make(chan *LocalTask, 5),
	}

	s.Run(s.loopFetch)
	tick := time.Tick(350 * time.Millisecond)
	stop := false
	for !stop {
		select {
		case <-tick:
			stop = true
		case v := <-s.taskChan:
			t.Logf("task: %+v, fetch time: %v, fetch duration: %v, fetch batch count: %v", v.task, v.fetchTime,
				v.fetchDuration, v.fetchBatchCount)
			atomic.AddInt32(&s.taskCount, -1)
		}
	}
	s.Finalize()
}

// 测试当taskChan没有及时被消费，fetchTask(0)操作
func TestFetchTaskInTaskChanNotEmpty(t *testing.T) {
	mockAsset := mock_controllers.NewMockAssetController(gomock.NewController(t))
	mockScanner := mock_controllers.NewMockScannerController(gomock.NewController(t))
	mockTask := mock_controllers.NewMockTaskController(gomock.NewController(t))

	mockTask.EXPECT().FetchTask(gomock.Any(), gomock.Any()).DoAndReturn(func(name string, count int32) []*entities.Task {
		ret := []*entities.Task{}
		switch count {
		case 1:
			ret = []*entities.Task{{Name: "test_task1", Priority: entities.Priority_VERY_HIGH}}
		case 2:
			ret = []*entities.Task{{Name: "test_task1", Priority: entities.Priority_VERY_HIGH},
				{Name: "test_task2", Priority: entities.Priority_HIGH}}
		case 3:
			ret = []*entities.Task{{Name: "test_task1", Priority: entities.Priority_VERY_HIGH},
				{Name: "test_task2", Priority: entities.Priority_HIGH},
				{Name: "test_task3", Priority: entities.Priority_NORMAL}}
		case 4:
			ret = []*entities.Task{
				{Name: "test_task1", Priority: entities.Priority_VERY_HIGH},
				{Name: "test_task2", Priority: entities.Priority_VERY_HIGH},
				{Name: "test_task3", Priority: entities.Priority_NORMAL},
				{Name: "test_task4", Priority: entities.Priority_NORMAL},
			}
		case 5:
			ret = []*entities.Task{
				{Name: "test_task1", Priority: entities.Priority_VERY_HIGH},
				{Name: "test_task2", Priority: entities.Priority_VERY_HIGH},
				{Name: "test_task3", Priority: entities.Priority_NORMAL},
				{Name: "test_task4", Priority: entities.Priority_NORMAL},
				{Name: "test_task5", Priority: entities.Priority_LOW},
			}
		}
		return ret
	}).AnyTimes()
	mockTask.EXPECT().GetMode().Return(int32(0)).AnyTimes()
	mockAsset.EXPECT().GetAsset().Return(&entities.Asset{
		Name:        "jingyun",
		ScanFeature: &entities.ScanFeature{MaxFetchCount: 5, MaxScanCount: 5, MaxScanTimeout: 202 * time.Second.Nanoseconds()},
		UpdateFeature: &entities.UpdateFeature{
			VersionDepot: &entities.VersionDepot{Versions: []*entities.Version{
				{UpdateDuration: "10.43447611s"},
				{UpdateDuration: "1111.446691518s"},
				{UpdateDuration: "11.505317922s"},
				{UpdateDuration: "65.506086934s"},
				{UpdateDuration: "7.383010141s"},
				{UpdateDuration: "12.205014231s"}},
			}},
	}).AnyTimes()
	mockScanner.EXPECT().GetState().Return(int32(util.IDLE)).AnyTimes()

	s := &scan{
		LoopController: util.LoopController{
			Stop: false,
		},
		assetController:   mockAsset,
		scannerController: mockScanner,
		taskController:    mockTask,
		taskCount:         0,
		taskChan:          make(chan *LocalTask, 5),
	}

	s.Run(s.loopFetch)
	tick := time.Tick(500 * time.Millisecond)
	stop := false
	for !stop {
		select {
		case <-tick:
			stop = true
		case v := <-s.taskChan:
			t.Logf("task: %+v, fetch time: %v, fetch duration: %v, fetch batch count: %v", v.task, v.fetchTime,
				v.fetchDuration, v.fetchBatchCount)
		}
	}
	s.Finalize()
}

// 测试当状态处于Updating或UpdateReady时，并且此时鉴定器升级的平均时间还大于了MaxScanTimeout时，应该处于一个循环中不进行Fetch操作
func TestFetchTaskStateInUpdateReady(t *testing.T) {
	mockAsset := mock_controllers.NewMockAssetController(gomock.NewController(t))
	mockScanner := mock_controllers.NewMockScannerController(gomock.NewController(t))
	mockTask := mock_controllers.NewMockTaskController(gomock.NewController(t))

	mockTask.EXPECT().GetMode().Return(int32(0)).AnyTimes()
	mockAsset.EXPECT().GetAsset().Return(&entities.Asset{
		Name:        "jingyun",
		ScanFeature: &entities.ScanFeature{MaxFetchCount: 5, MaxScanCount: 5, MaxScanTimeout: 200 * time.Second.Nanoseconds()},
		UpdateFeature: &entities.UpdateFeature{
			VersionDepot: &entities.VersionDepot{Versions: []*entities.Version{
				{UpdateDuration: "10.43447611s"},
				{UpdateDuration: "1111.446691518s"},
				{UpdateDuration: "11.505317922s"},
				{UpdateDuration: "65.506086934s"},
				{UpdateDuration: "7.383010141s"},
				{UpdateDuration: "12.205014231s"}},
			}},
	}).AnyTimes()
	mockScanner.EXPECT().GetState().Return(int32(util.UPDATE_READY)).AnyTimes()
	mockScanner.EXPECT().GetScanner().Return(&entities.Scanner{Name: "jingyun"}).AnyTimes()

	s := &scan{
		LoopController: util.LoopController{
			Stop: false,
		},
		assetController:   mockAsset,
		scannerController: mockScanner,
		taskController:    mockTask,
		taskCount:         0,
		taskChan:          make(chan *LocalTask, 5),
	}

	s.Run(s.loopFetch)
	tick := time.Tick(360 * time.Millisecond)
	stop := false
	for !stop {
		select {
		case <-tick:
			stop = true
		case v := <-s.taskChan:
			t.Logf("task: %+v, fetch time: %v, fetch duration: %v, fetch batch count: %v", v.task, v.fetchTime,
				v.fetchDuration, v.fetchBatchCount)
			atomic.AddInt32(&s.taskCount, -1)
		}
	}
	s.Finalize()
}

// 测试当扫描失败并且重试次数达到了设置FailTolerance，触发鉴定器的重启流程，通过日志观察得到
func TestToScan(t *testing.T) {
	mockScanner := mock_controllers.NewMockScannerController(gomock.NewController(t))
	mockScanner.EXPECT().Scan(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, scanMap []scanadapter.ScanOperation) (*util.ScanResult, error) {
		time.Sleep(time.Second * 5)
		t.Log("scan complete ...")
		res := &util.ScanResult{
			Result: map[string]*kamala.ScanResult{"task1-path": {Name: "task1", Code: kamala.ScanCode_INVALID_PARAM},
				"task2-path": {Name: "task2", Code: kamala.ScanCode_INVALID_PARAM},
				"task3-path": {Name: "task3", Code: kamala.ScanCode_INVALID_PARAM}},
			RetryCount: 5,
		}
		return res, errors.New("timeout")
	}).AnyTimes()
	mockScanner.EXPECT().Exit().Return(nil).AnyTimes()
	input := make(map[string]*LocalTask)
	cases := []struct {
		name      string
		priority  entities.Priority
		parameter string
	}{
		{name: "task1-path", parameter: "parameter1", priority: entities.Priority_VERY_HIGH},
		{name: "task2-path", parameter: "parameter2", priority: entities.Priority_VERY_HIGH},
		{name: "task3-path", parameter: "parameter3", priority: entities.Priority_VERY_HIGH},
	}
	input["task1-path"] = &LocalTask{
		task: &entities.Task{
			Name:           "task1",
			Priority:       cases[0].priority,
			Parameter:      cases[0].parameter,
			PatternVersion: "20210205190005",
			EngineVersion:  "20210205190005",
		},
		lastTime: time.Second * 100,
	}

	input["task2-path"] = &LocalTask{
		task: &entities.Task{
			Name:           "task2",
			Priority:       cases[1].priority,
			Parameter:      cases[1].parameter,
			PatternVersion: "20210205190005",
			EngineVersion:  "20210205190005",
		},
		lastTime: time.Second * 100,
	}

	input["task3-path"] = &LocalTask{
		task: &entities.Task{
			Name:           "task3",
			Priority:       cases[2].priority,
			Parameter:      cases[2].parameter,
			PatternVersion: "20210205190005",
			EngineVersion:  "20210205190005",
		},
		lastTime: time.Second * 100,
	}
	mockScanner.EXPECT().Ping().Return(&scanadapter.PingResponse{
		StartupSeconds: 1,
		Version:        &scanadapter.Version{EngineVersion: "", PatternVersion: ""},
	}, nil).AnyTimes()
	mockAsset := mock_controllers.NewMockAssetController(gomock.NewController(t))
	mockAsset.EXPECT().GetAsset().Return(&entities.Asset{
		Name: "test1",
		UpdateFeature: &entities.UpdateFeature{
			UpdaterInterval:   300 * time.Second.Nanoseconds(),
			UpdaterVolumeSize: 100 * MB,
		},
		ScanFeature: &entities.ScanFeature{
			FailTolerance:  5,
			MaxScanTimeout: 100 * time.Second.Nanoseconds()},
	}).AnyTimes()
	mockScanner.EXPECT().SetState(gomock.Any()).AnyTimes()
	mockScanner.EXPECT().GetState().Return(int32(util.IDLE)).AnyTimes()
	s := &scan{
		preStartupTime:    time.Now().Add(-time.Second * 3).Unix(),
		resultChan:        make(chan *LocalTask, 3),
		scannerController: mockScanner,
		assetController:   mockAsset,
	}
	scanMap := defaultScanOperations(input)
	s.toScan(scanMap, input, "")
	assert.Equal(t, 0, len(input))
}

func TestToReload(t *testing.T) {
	h := helper.NewUpdateHelper("testfile")
	mockScanner := mock_controllers.NewMockScannerController(gomock.NewController(t))
	mockScanner.EXPECT().GetScanner().Return(&entities.Scanner{
		Name:           "test_scanner",
		StartupSeconds: 300,
	}).AnyTimes()
	mockScanner.EXPECT().Reload(gomock.Any()).Return(&scanadapter.Version{
		EngineVersion:  "20210203200916",
		PatternVersion: "20210203200916",
	}, nil)
	mockAsset := mock_controllers.NewMockAssetController(gomock.NewController(t))
	mockAsset.EXPECT().GetAsset().Return(&entities.Asset{
		Name: "test1",
		UpdateFeature: &entities.UpdateFeature{
			Kind:              entities.UpgradeKind_UPDATER,
			UpdaterInterval:   300 * time.Second.Nanoseconds(),
			UpdaterVolumeSize: 100 * MB,
		},
		ScanFeature: &entities.ScanFeature{
			FailTolerance:  5,
			MaxScanTimeout: 100 * time.Second.Nanoseconds()},
	}).AnyTimes()
	version := "1612354532"
	s := &scan{
		scannerStartTime:  600,
		helper:            h,
		assetController:   mockAsset,
		scannerController: mockScanner,
		preVersion:        version,
	}
	s.toReload(version, "20210203200916", "20210203200916")
}

type EntitiesTask2 struct {
	Name        string
	FetchUri    string
	Size        uint64
	Sha1        string
	StorageSize uint64
	FileSize    uint64
}

func mockFetchTask() []*EntitiesTask2 {
	tasks := []*EntitiesTask2{
		{
			Name:        fmt.Sprintf("PROCESS_ID_%v_task1", os.Getpid()),
			FetchUri:    "files/89a63806f735cb14b03b106a7d194be9a513178a/storage",
			Size:        1025000,
			Sha1:        "89a63806f735cb14b03b106a7d194be9a513178a",
			StorageSize: 1025000,
			FileSize:    1025000,
		},
		{
			Name:     fmt.Sprintf("PROCESS_ID_%v_task2", os.Getpid()),
			FetchUri: "files/89a63806f735cb14b03b106a7d194be9a513178a/storage",
			Size:     1025000,
			Sha1:     "89a63806f735cb14b03b106a7d194be9a513178a",
		},
		{
			Name:     fmt.Sprintf("PROCESS_ID_%v_task3", os.Getpid()),
			FetchUri: "files/89a63806f735cb14b03b106a7d194be9a513178a/storage",
			Size:     1025000,
			Sha1:     "89a63806f735cb14b03b106a7d194be9a513178a",
		},
		// 测试样本size已知并且为：20MB 的情况
		{
			Name:        fmt.Sprintf("PROCESS_ID_%v_task4", os.Getpid()),
			FetchUri:    "files/792435ce130dcbe2d4fc8e7b5b77594765def8c7/storage",
			Size:        21192408,
			Sha1:        "792435ce130dcbe2d4fc8e7b5b77594765def8c7",
			StorageSize: 21192408,
			FileSize:    21192408,
		},
		{
			Name:     fmt.Sprintf("PROCESS_ID_%v_task5", os.Getpid()),
			FetchUri: "files/792435ce130dcbe2d4fc8e7b5b77594765def8c7/storage",
			Size:     21192408,
			Sha1:     "792435ce130dcbe2d4fc8e7b5b77594765def8c7",
		},
		// 测试样本size都已知并且为：16MB压缩包 的情况
		{
			Name:        fmt.Sprintf("PROCESS_ID_%v_task6", os.Getpid()),
			FetchUri:    "files/81dd057fb9578a15c8ec32c5a412dad02f762aa9/storage",
			Size:        939233,
			Sha1:        "cdd3ea01fb9e2589ea093f8e226f38d9e6e61c38",
			StorageSize: 939233,
			FileSize:    939233,
		},
		{
			Name:     fmt.Sprintf("PROCESS_ID_%v_task7", os.Getpid()),
			FetchUri: "files/81dd057fb9578a15c8ec32c5a412dad02f762aa9/storage",
			Size:     939233,
			Sha1:     "cdd3ea01fb9e2589ea093f8e226f38d9e6e61c38",
		},
		// 测试样本size未知的情况
		{
			Name:        fmt.Sprintf("PROCESS_ID_%v_task8", os.Getpid()),
			FetchUri:    "files/792435ce130dcbe2d4fc8e7b5b77594765def8c7/storage",
			Sha1:        "792435ce130dcbe2d4fc8e7b5b77594765def8c7",
			StorageSize: 21192408,
			FileSize:    21192408,
		},
		{
			Name:        fmt.Sprintf("PROCESS_ID_%v_task9", os.Getpid()),
			FetchUri:    "files/89a63806f735cb14b03b106a7d194be9a513178a/storage",
			Sha1:        "89a63806f735cb14b03b106a7d194be9a513178a",
			StorageSize: 1025000,
			FileSize:    1025000,
		},
		// 测试下载回来的样本实际上是压缩包格式，下载后的内容比所在目录的上限大了，触发抢大目录锁并向大目录转移
		{
			Name:        fmt.Sprintf("PROCESS_ID_%v_task10", os.Getpid()),
			FetchUri:    "files/fefb39ef652b150f0bd43ea6b73db9c9e2a83ec7/storage",
			Sha1:        "213d1a831eddd85c4e6d85784ecfc415bc10204b",
			StorageSize: 37873928,
			FileSize:    65627224,
			//Size:        37873928,
		},
	}
	return tasks
}

func pushEntitiesTasks(taskChan chan *LocalTask, tasks []*entities.Task) {
	for _, task := range tasks {
		taskChan <- &LocalTask{task: task}
	}
}

func pushEntitiesTask2s(taskChan chan *LocalTask, tasks []*EntitiesTask2) {
	for _, task := range tasks {
		taskChan <- &LocalTask{
			task: &entities.Task{
				Name:     task.Name,
				FetchUri: task.FetchUri,
				Size:     task.Size,
				Sha1:     task.Sha1,
			},
		}
	}
	log.Infof("pushed %d task to taskChan", len(tasks))
}

func mockScan(queue pqueue.PQueue, resultChan chan *LocalTask) {
	ticker := time.Tick(200 * time.Millisecond)
	count := 0
	for {
		if count >= 10 {
			break
		}
		select {
		case <-ticker:
			time.Sleep(time.Second)
			task := queue.Pop()
			if task != nil {
				resultChan <- task.(*LocalTask)
				count++
				log.Debugf("任务：%v 进入resultChan, 此时已经入resultChan的数量：%v, PQueue len: %v",
					task.(*LocalTask).task.GetName(), count, queue.Len())
			} else {
				log.Debugf("从PQueue队列中获取的task为nil， len: %v", queue.Len())
			}
		}
	}
	log.Info("mock scan exit")
}

func mockFetchTask5(taskChan chan *LocalTask) {
	tasks := []*entities.Task{
		{
			Name:     fmt.Sprintf("PROCESS_ID_%v_task1", os.Getpid()),
			FetchUri: "files/fefb39ef652b150f0bd43ea6b73db9c9e2a83ec7/storage",
			Sha1:     "213d1a831eddd85c4e6d85784ecfc415bc10204b",
		},
		{
			Name:     fmt.Sprintf("PROCESS_ID_%v_task2", os.Getpid()),
			FetchUri: "files/fefb39ef652b150f0bd43ea6b73db9c9e2a83ec7/storage",
			Sha1:     "213d1a831eddd85c4e6d85784ecfc415bc10204b",
		},
		{
			Name:     fmt.Sprintf("PROCESS_ID_%v_task3", os.Getpid()),
			FetchUri: "files/fefb39ef652b150f0bd43ea6b73db9c9e2a83ec7/storage",
			Sha1:     "213d1a831eddd85c4e6d85784ecfc415bc10204b",
		},
		{
			Name:     fmt.Sprintf("PROCESS_ID_%v_task4", os.Getpid()),
			FetchUri: "files/fefb39ef652b150f0bd43ea6b73db9c9e2a83ec7/storage",
			Sha1:     "213d1a831eddd85c4e6d85784ecfc415bc10204b",
		},
		{
			Name:     fmt.Sprintf("PROCESS_ID_%v_task5", os.Getpid()),
			FetchUri: "files/fefb39ef652b150f0bd43ea6b73db9c9e2a83ec7/storage",
			Sha1:     "213d1a831eddd85c4e6d85784ecfc415bc10204b",
		},
	}
	for _, task := range tasks {
		taskChan <- &LocalTask{task: task}
	}
}

func mockScan2(queue pqueue.PQueue, resultChan chan *LocalTask) {
	ticker := time.Tick(500 * time.Millisecond)
	count := 0
	for {
		if count >= 5 {
			break
		}
		select {
		case <-ticker:
			time.Sleep(time.Second)
			task := queue.Pop()
			if task != nil {
				resultChan <- task.(*LocalTask)
				count++
				log.Debugf("任务：%v 进入resultChan, 此时已经入resultChan的数量：%v, PQueue len: %v",
					task.(*LocalTask).task.GetName(), count, queue.Len())
			} else {
				log.Debugf("从PQueue队列中获取的task为nil， len: %v", queue.Len())
			}
		}
	}
	log.Info("mock scan exit")
}

type ConcurrentController struct {
	sync.WaitGroup
}

func (c *ConcurrentController) Finalize() {
	c.Wait()
}

func (c *ConcurrentController) Run(cb func()) {
	c.Add(1)
	go func() {
		cb()
		c.Done()
	}()
}

func (c *ConcurrentController) Run1(cb func(pqueue.PQueue, chan *LocalTask), queue pqueue.PQueue, resultChan chan *LocalTask) {
	c.Add(1)
	go func() {
		cb(queue, resultChan)
		c.Done()
	}()
}

func (c *ConcurrentController) RunLock(cb func(uint64) (DirInfo, *FileLock), n int, size uint64) {
	c.Add(1)
	go func() {
		dir, lock := cb(size)
		log.Debugf("[unit test debug]:协程：%v\t锁住的目录是：%v, 是否成功：%v", n, dir.Name, lock.lockErr)
		err := lock.UnLock()
		log.Debugf("[unit test debug]:协程：%v\t解锁是否成功：%v", n, err)
		lock.Release()
		c.Done()
	}()
}

func (c *ConcurrentController) RunTryDownload(cb func(uint64) (DirInfo, *FileLock), n int, size uint64) {
	c.Add(1)
	go func() {
		dir, lock := cb(size)
		log.Debugf("[unit test debug]:协程：%v\t锁住的目录是：%v, 是否成功：%v", n, dir.Name, lock.lockErr)
		time.Sleep(time.Second)
		err := lock.UnLock()
		log.Debugf("[unit test debug]:协程：%v\t解锁是否成功：%v", n, err)
		lock.Release()
		c.Done()
	}()
}

func initScan(t *testing.T) *scan {
	t.Setenv("MORPHEUS_API_STORAGE", "10.252.16.107:30979")
	concurrency := 5
	pool, err := ants.NewPool(concurrency)
	if err != nil {
		log.Fatalf("failed to get new pool for storage, err: %v", err)
	}
	pool.Running()
	updatePool, err := ants.NewPool(concurrency)
	if err != nil {
		log.Fatalf("failed to get new pool for storage, err: %v", err)
	}
	updatePool.Running()
	mockTask := mock_controllers.NewMockTaskController(gomock.NewController(t))
	mockTask.EXPECT().DeliverTask(gomock.Any(), gomock.Any()).DoAndReturn(func(arg1 interface{}, arg2 interface{}) error {
		time.Sleep(3 * time.Second)
		return nil
	}).AnyTimes()
	mockAsset := mock_controllers.NewMockAssetController(gomock.NewController(t))
	mockAsset.EXPECT().GetAsset().Return(&entities.Asset{
		Name: "test_scanner",
		ScanFeature: &entities.ScanFeature{
			MaxFetchSize: 50 * MB,
		},
	}).AnyTimes()
	mockScanner := mock_controllers.NewMockScannerController(gomock.NewController(t))
	mockScanner.EXPECT().GetScanner().Return(&entities.Scanner{Name: "test_scanner"}).AnyTimes()
	smallFileEndPos = 1
	middleFileEndPos = 2
	bigFileEndPos = 3
	smallFile = 50 * MB
	middleFile = 100 * MB
	bigFile = 200 * MB
	s := &scan{
		resultChan:        make(chan *LocalTask, 10),
		taskChan:          make(chan *LocalTask, 10),
		logChan:           make(chan *LocalTask, 10),
		storageController: proxy.GetStorage(),
		filePQueue:        pqueue.New(pqueue.Size(10)),
		fileDir:           "./tmp/file",
		logDir:            "./tmp/file",
		plusFileDir:       path.Join(os.Getenv("HOME"), "tmp", "plusfile"),
		storagePool:       pool,
		updatePool:        updatePool,
		taskController:    mockTask,
		assetController:   mockAsset,
		scannerController: mockScanner,
		emptyDirSize:      20 * MB,
		plusDirSize:       350 * MB,
		enableUsePlusDisk: true,
	}
	s.initDirList()
	return s
}

// 测试样本size都已知并且为：5MB 的情况
func TestSampleSizeWithin5MB(t *testing.T) {
	defer framework.Init()()
	testFile := "./tmp/file"
	if !util.CheckFileIsExist(testFile) {
		_ = os.MkdirAll(testFile, os.ModePerm)
	}
	s := initScan(t)
	defer s.storagePool.Release()
	defer s.updatePool.Release()

	tasks := mockFetchTask()

	pg := sm.Patch(util.GetStorageSize, func(uri string) (int64, error) {
		for _, task := range tasks {
			if uri == task.FetchUri || uri == task.Sha1 {
				if task.StorageSize > 0 {
					return int64(task.StorageSize), nil
				}
				return int64(task.Size), nil
			}
		}
		return 0, fmt.Errorf("just return an error")
	})
	defer pg.Unpatch()
	mockStorage := mock_storage.NewMockStorage(gomock.NewController(t))
	mockStorage.EXPECT().Download(gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx context.Context, input util.DownloadInput) (*sdk.DownloadInfo, error) {
			time.Sleep(10 * time.Millisecond)

			// 如果可能，则取 task.FileSize, 否则 input.Size
			fileSize := input.Size
			for _, task := range tasks {
				if input.Uri == task.FetchUri || input.Sha1 == task.Sha1 {
					if task.FileSize > 0 {
						fileSize = task.FileSize
						break
					}
				}
			}
			log.Infof("mock Download: %+v, filesize %d", input, fileSize)
			content := bytes.Repeat([]byte{'A'}, int(fileSize))
			err := os.MkdirAll(filepath.Dir(input.FilePath), 0777)
			if err != nil {
				return nil, err
			}
			err = ioutil.WriteFile(input.FilePath, content, 0644)
			if err != nil {
				return nil, err
			}
			return &sdk.DownloadInfo{
				Length: fileSize,
				Code:   200,
			}, nil
		}).AnyTimes()
	s.storageController = mockStorage

	pushEntitiesTask2s(s.taskChan, tasks)
	c := &ConcurrentController{}
	s.LoopController.Run(s.loopDownload)
	c.Run1(mockScan, s.filePQueue, s.resultChan)
	s.LoopController.Run(s.loopDeliver)
	c.Finalize()
	log.Infof("[test debug]:MockFetchTask回收完成...........")
	for len(s.resultChan) > 0 {
		time.Sleep(500 * time.Microsecond)
		log.Infof("[test debug]:resultChan length: %v 等待deliver回收文件删除....", len(s.resultChan))
	}
	time.Sleep(time.Second * 10)
	s.LoopController.Finalize()
	err := os.RemoveAll(testFile)
	if err != nil {
		t.Fatalf("clean test file failed: %v", err)
	}
}

// 测试下载回来的样本恰好将目录空间使用完了，触发向高阶目录转移
/*func TestSampleCauseDirSpaceNotEnough(t *testing.T) {
	defer framework.Init()()
	testFile := "./tmp/file"
	if !util.CheckFileIsExist(testFile) {
		_ = os.MkdirAll(testFile, os.ModePerm)
	}
	s := initScan(t)
	defer s.storagePool.Release()
	defer s.updatePool.Release()
	mockFetchTask5(s.taskChan)
	c := &ConcurrentController{}
	s.LoopController.Run(s.loopDownload)
	c.Run1(mockScan2, s.filePQueue, s.resultChan)
	s.LoopController.Run(s.loopDeliver)
	c.Finalize()
	log.Infof("[test debug]:MockFetchTask回收完成...........")
	for len(s.resultChan) > 0 {
		time.Sleep(500 * time.Microsecond)
		log.Infof("[test debug]:resultChan length: %v 等待deliver回收文件删除....", len(s.resultChan))
	}
	s.LoopController.Finalize()
	err := os.RemoveAll(testFile)
	if err != nil {
		t.Fatalf("clean test file failed: %v", err)
	}
	_ = util.RemoveFile("./tmp")
}*/

func TestSplitPath(t *testing.T) {
	dest, _ := filepath.Rel("/relay", "/relay/file1/106901240225_task")
	t.Logf("res: %v", dest)
}
