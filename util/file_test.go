package util

import (
	"archive/zip"
	"bytes"
	"crypto/sha1"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"path"
	"strings"
	"testing"

	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk"
	"github.com/stretchr/testify/assert"
)

func TestCheckFileIsExist(t *testing.T) {
	isExist := CheckFileIsExist("./file.go")
	assert.Equal(t, true, isExist)
	isExist = CheckFileIsExist("./notFound.go")
	assert.Equal(t, false, isExist)
}

func TestCalcSha1(t *testing.T) {
	content, err := LoadFile("./file.go")
	assert.Equal(t, nil, err)
	res := CalcSha1(content)
	t.Logf("sha1: %v", res)
}

func TestCopyFile(t *testing.T) {
	err := CopyFile("./file.go", "./file_copy.go")
	assert.Equal(t, nil, err)
	err = RemoveFile("./file_copy.go")
	assert.Equal(t, nil, err)
}

func TestCopyDir(t *testing.T) {
	err := CopyDir("../testfile/latest", "../testfile/latest_copy")
	assert.Equal(t, nil, err)
	err = os.RemoveAll("../testfile/latest_copy")
	assert.Equal(t, nil, err)
}

func TestSaveFile(t *testing.T) {
	content, err := LoadFile("./file.go")
	assert.Equal(t, nil, err)

	err = SaveFile(content, "../testfile/file_copy.go")
	assert.Equal(t, nil, err)

	err = RemoveFile("../testfile/file_copy.go")
	assert.Equal(t, nil, err)
}

func Test_CheckSha1AndSave(t *testing.T) {
	// 创建压缩文件
	temp := "temp"
	os.MkdirAll(temp, os.ModePerm)
	defer os.RemoveAll(temp)
	zipFilePath := path.Join(temp, "test.zip")
	zipFile, _ := os.Create(zipFilePath)
	zipWirter := zip.NewWriter(zipFile)
	zipIoWriter, _ := zipWirter.Create("test")
	zipIoWriter.Write([]byte("test"))
	zipWirter.Close()
	zipFile.Close()

	//计算sha1
	file, _ := os.Open(zipFilePath)
	h := sha1.New()
	io.Copy(h, file)
	file.Close()
	sha1Sum := fmt.Sprintf("%x", h.Sum(nil))

	file, _ = os.Open(zipFilePath)

	err := CheckSha1AndSave(&sdk.DownloadInfo{
		Body: file,
	}, &DownloadInput{
		Sha1:     sha1Sum,
		FilePath: path.Join(temp, "save"),
	})

	assert.Nil(t, err)
}

func Test_CheckZipSha1Consistency(t *testing.T) {
	// 创建压缩文件
	temp := "temp"
	os.MkdirAll(temp, os.ModePerm)
	defer os.RemoveAll(temp)
	zipFilePath := path.Join(temp, "test.zip")
	zipFile, _ := os.Create(zipFilePath)
	zipWirter := zip.NewWriter(zipFile)
	zipIoWriter, _ := zipWirter.Create("test")
	zipIoWriter.Write([]byte("test"))
	zipWirter.Close()
	zipFile.Close()

	//计算sha1
	file, _ := os.Open(zipFilePath)
	h := sha1.New()
	io.Copy(h, bytes.NewReader([]byte("test")))
	file.Close()
	sha1Sum := fmt.Sprintf("%x", h.Sum(nil))

	file, _ = os.Open(zipFilePath)

	res, _ := CheckZipSha1Consistency(file, path.Join(temp, "save"), sha1Sum)

	assert.Equal(t, true, res.Sha1Same)
}

func Test_Sha1Sum(t *testing.T) {

	h := sha1.New()
	io.Copy(h, bytes.NewReader([]byte("test")))
	sha1Sum := fmt.Sprintf("%x", h.Sum(nil))

	res, _ := Sha1Sum(bytes.NewReader([]byte("test")))

	assert.Equal(t, sha1Sum, res)
}

func Test_Unzip(t *testing.T) {
	temp := "temp"
	os.MkdirAll(temp, os.ModePerm)
	defer os.RemoveAll(temp)
	zipFilePath := path.Join(temp, "test.zip")
	zipFile, _ := os.Create(zipFilePath)
	zipWirter := zip.NewWriter(zipFile)
	zipIoWriter, _ := zipWirter.Create("test")
	zipIoWriter.Write([]byte("test"))
	zipWirter.Close()
	zipFile.Close()

	file, _ := os.Open(zipFilePath)
	content, _ := ioutil.ReadAll(file)
	Unzip(content)
}

func Test_StreamSave(t *testing.T) {
	temp := "testdata"
	defer os.RemoveAll(temp)
	StreamSave(io.NopCloser(strings.NewReader("test")), path.Join(temp, "test"))
	_, err := os.Stat(path.Join(temp, "test"))

	fmt.Println(err)
	assert.Nil(t, err)
}

// 测试sha1全部不一致的情况
// func TestCheckSha1Consistency(t *testing.T) {
// 	tests := []struct{
// 		downloadInfo	*sdk.DownloadInfo
// 		in				*DownloadInput
// 		testFile		string
// 	}{
// 		{
// 			downloadInfo: &sdk.DownloadInfo{},
// 			in: &DownloadInput{
// 				Size: 151664497,
// 				FilePath: "./c5d45a227061cfb6208f5bb621c2f9ab64947f57",
// 				Sha1: "c5d45a227061cfb6208f5bb621c2f9ab64947f57",
// 			},
// 			testFile: "./test",
// 		},
// 		{
// 			downloadInfo: &sdk.DownloadInfo{},
// 			in: &DownloadInput{
// 				Size: 84008888,
// 				FilePath: "./c5d45a227061cfb6208f5bb621c2f9ab64947f57",
// 				Sha1: "c5d45a227061cfb6208f5bb621c2f9ab64947f57",
// 			},
// 			testFile: "./test.zip",
// 		},
// 	}

// 	for _, tt := range tests {
// 		f, err := os.Open(tt.testFile)
// 		if err != nil {
// 			t.Fatalf("open file: %v failed: %v", tt.in.FilePath, err)
// 		}
// 		tt.downloadInfo.Body = f
// 		err = CheckSha1AndSave(tt.downloadInfo, tt.in)
// 		if err != nil {
// 			t.Fatalf("check sha1 consistency failed: %v", err)
// 		}
// 		f.Close()
// 		assert2.Equal(t, tt.in.Sha1, tt.downloadInfo.ActualSha1)
// 		assert2.Equal(t, true, tt.downloadInfo.Sha1Same)
// 		t.Logf("Length: %v", tt.downloadInfo.Length)
// 		err = RemoveFile(tt.in.FilePath)
// 		if err != nil {
// 			t.Fatalf("remove file: %v failed", tt.in.FilePath)
// 		}
// 	}
// }

// func TestUnzipFile(t *testing.T) {
// 	err := UnzipFile("./test.zip", "test1")
// 	if err != nil {
// 		t.Fatalf("unzip failed: %v", err)
// 	}
// 	_ = RemoveFile("./test1")
// }

// 测试sha1全部是一致的情况
// func TestCheckSha1NotConsistency(t *testing.T) {
// 	tests := []struct {
// 		downloadInfo *sdk.DownloadInfo
// 		in           *DownloadInput
// 		testFile     string
// 	}{
// 		{
// 			downloadInfo: &sdk.DownloadInfo{},
// 			in: &DownloadInput{
// 				Size:     151664497,
// 				FilePath: "./c5d45a227061cfb6208f5bb621c2f9ab64947f88",
// 				Sha1:     "c5d45a227061cfb6208f5bb621c2f9ab64947f59",
// 			},
// 			testFile: "./test",
// 		},
// 		{
// 			downloadInfo: &sdk.DownloadInfo{},
// 			in: &DownloadInput{
// 				Size:     84008888,
// 				FilePath: "./c5d45a227061cfb6208f5bb621c2f9ab64947f99",
// 				Sha1:     "c5d45a227061cfb6208f5bb621c2f9ab64947f59",
// 			},
// 			testFile: "./test.zip",
// 		},
// 	}

// 	for _, tt := range tests {
// 		f, err := os.Open(tt.testFile)
// 		if err != nil {
// 			t.Fatalf("open file: %v failed: %v", tt.in.FilePath, err)
// 		}
// 		tt.downloadInfo.Body = f
// 		err = CheckSha1AndSave(tt.downloadInfo, tt.in)
// 		assert2.NotEqual(t, nil, err)
// 		f.Close()
// 		assert2.NotEqual(t, tt.in.Sha1, tt.downloadInfo.ActualSha1)
// 		assert2.Equal(t, false, tt.downloadInfo.Sha1Same)
// 		t.Logf("Length: %v", tt.downloadInfo.Length)
// 	}
// }

func TestGetDirSize(t *testing.T) {
	testPath := "../tmp/file"
	size, _ := GetDirSize(testPath)
	t.Logf("size: %v M", float64(size)/MB)
}

func Test_ExecCmdGetResponse(t *testing.T) {

	ExecCmdGetResponse("ls")
}

func Test_MoveFile(t *testing.T) {
	name := "test-"
	defer os.Remove(name)
	f, _ := os.Create(name)
	f.Close()

	MoveFile(name, "test+")
	defer os.Remove("test+")

}
