package util

import (
	"context"
	"crypto/sha1"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git-biz.qianxin-inc.cn/zion-infra/data-foundation/apis/morpheus-api.git/generated-go/morpheus/base"
	kamala "git-biz.qianxin-inc.cn/zion-infra/kamala/api.git"
	entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	api_scan "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git"
	"github.com/golang/protobuf/ptypes/timestamp"
	uuid "github.com/pborman/uuid"
)

const (
	MB                         = 1 << 20 // 1 MB in bytes
	GB                         = 1 << 30 // 1 GB in bytes
	assumedMinNetworkSpeedMBps = 100     // Min download speed for 1Gbps intranet
	assumedMinIOSpeedMBps      = 500     // Min SSD write speed
	effectiveSpeedMBps         = 100     // min(assumedMinNetworkSpeedMBps, assumedMinIOSpeedMBps)
)

type ScanResult struct {
	Result     map[string]*api_scan.ScanResult
	RetryCount int
}

func KamalaVersionToEntity(in *api_scan.Version) *entities.Version {
	if in == nil {
		return &entities.Version{}
	}
	response := entities.Version{
		Engine:     in.GetEngineVersion(),
		Pattern:    in.GetPatternVersion(),
		UpdateTime: time.Now().UnixNano(),
	}
	return &response
}

func TimestampToNano(in *timestamp.Timestamp) int64 {
	if in == nil {
		return 0
	}
	return in.GetSeconds()*int64(time.Second) + int64(in.GetNanos())
}

func KamalaKindToEntity(in kamala.VersionKind) entities.VersionKind {
	return entities.VersionKind(int32(in))
}

func GetTotalTime(timeout int64, fileSize uint64) time.Duration {
	ladderTime := GetDownloadLadderTime(fileSize) * 2
	ladderTime += time.Duration(timeout)
	return ladderTime
}

func GetDownloadLadderTime(fileSize uint64) time.Duration {
	var ladderTime float64 = 60 // Use float64 for precise calculations

	sizeInMB := float64(fileSize / MB)

	if fileSize > 30*MB {
		ladderTime = (sizeInMB/effectiveSpeedMBps)*1.2 + 60
	}
	if fileSize > 240*MB {
		ladderTime = (sizeInMB/effectiveSpeedMBps)*1.3 + 90
	}
	if fileSize > 1*GB {
		ladderTime = (sizeInMB/effectiveSpeedMBps)*1.4 + 120
	}
	if fileSize > 5*GB {
		ladderTime = (sizeInMB/effectiveSpeedMBps)*1.5 + 180
	}
	if fileSize > 10*GB {
		ladderTime = (sizeInMB/effectiveSpeedMBps)*1.6 + 300
	}
	if fileSize > 50*GB {
		ladderTime = 7200 // Upper limit: 2 hours for files >30GB
	}
	// 1 always means not known
	if fileSize == 1 {
		ladderTime = 1800 // Assume medium file for unknown size (30 minutes)
	}

	// Apply upper limit
	if ladderTime > 7200 {
		ladderTime = 7200
	}

	return time.Duration(ladderTime)
}

func GetUploadLadderTime(fileSize uint64) time.Duration {
	var ladderTime uint64 = 5
	if fileSize > 20*MB && fileSize <= 50*MB {
		ladderTime = fileSize / MB / 2
	} else if fileSize > 50*MB {
		ladderTime = fileSize / MB * 2
	}
	if fileSize > 240*MB {
		ladderTime = 240
	}
	// 1 always means not known
	if fileSize == 1 {
		ladderTime = 240
	}
	return time.Duration(ladderTime)
}

func SetStoreData(t *entities.Task, content []byte) {
	t.StoreHash = fmt.Sprintf("%x", sha1.Sum(content))
	t.StoreSize = uint64(len(content))
}

func GetSha1(logPath string) string {
	content, err := LoadFile(logPath)
	if err != nil {
		return ""
	}

	h := sha1.New()
	_, err = h.Write(content)
	if err != nil {
		return ""
	}

	result := h.Sum(nil)
	return fmt.Sprintf("%x", result)
}

func TaskSubst(t *entities.Task, uri string, logPath string) string {
	params := make(map[string]func() string)
	params["FILESHA1"] = func() string { return t.GetSha1() }
	params["LOGSHA1"] = func() string { return GetSha1(logPath) }
	params["ASSET"] = func() string { return t.GetAssetName() }
	params["SCANNER"] = func() string { return getScannerName(t) }
	params["CREATE_TS"] = func() string { return strconv.FormatInt(t.GetCreateTime(), 10) }
	params["START_TS"] = func() string { return strconv.FormatInt(t.GetStartTime(), 10) }
	params["FINISH_TS"] = func() string { return strconv.FormatInt(t.GetFinishTime(), 10) }
	params["UUID"] = func() string { return uuid.NewUUID().String() }

	return Subst(uri, params)
}

func getScannerName(task *entities.Task) string {
	scannerName := ""
	if len(task.GetScannerNames()) > 0 {
		scannerName = task.GetScannerNames()[0]
	}
	return scannerName
}

func Subst(uri string, subMap map[string]func() string) string {
	for k, f := range subMap {
		uri = strings.ReplaceAll(uri, fmt.Sprintf("${%s}", k), f())
	}
	return uri
}

func GetStorageSize(uri string) (int64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	storageSdk, err := GetDialer().FetchStorageSDK(ctx)
	if err != nil {
		return 1, err
	}
	if strings.Contains(uri, "relay") || strings.Contains(uri, "message") {
		return 60, nil
	}

	var s *base.Storage
	if strings.HasPrefix(uri, "files/") {
		s, err = storageSdk.GetStorage(ctx, uri)
	} else {
		s, err = storageSdk.GetScanLogBySha1(ctx, uri)
	}

	if err != nil {
		return 1, err
	}
	size := s.GetFile().GetSize()
	if size == 0 {
		size = 1
	}
	return size, nil
}

func IsSelfUpdate(asset *entities.Asset) bool {
	if asset.GetUpdateFeature().GetKind() == entities.UpgradeKind_SELF {
		return true
	}
	return false
}

// 判断是否为统一升级
func IsUniteUpdate(asset *entities.Asset) bool {
	if asset.GetUpdateFeature().GetKind() == entities.UpgradeKind_DARWIN ||
		asset.GetUpdateFeature().GetKind() == entities.UpgradeKind_UPDATER {
		return true
	}
	return false
}

func GetChildStoreUri(input string) (string, error) {
	var uri string

	// Split the input string into individual arguments
	input = strings.Trim(input, " ")
	if len(input) > 0 && input[0] == '{' {
		// If not a flag (doesn't start with '-'), and not already identified as JSON,
		// assume it's the start of JSON input
		var jsonMap map[string]interface{}
		if err := json.Unmarshal([]byte(input), &jsonMap); err == nil {
			// Extract "uri" field from JSON
			if uriValue, ok := jsonMap["uri"].(string); ok {
				uri = uriValue
			}
		} else {
			return "", fmt.Errorf("Invalid json: %v", err)
		}
		return uri, nil
	} else {
		args := strings.Fields(input)

		for i := 0; i < len(args); i++ {
			arg := args[i]
			option := "--uri"

			if strings.HasPrefix(arg, option) {
				if arg == option {
					// Check if --uri is followed by a value
					if i+1 < len(args) {
						uri = args[i+1]
						i++ // Move to the next argument after the URI
					} else {
						return "", fmt.Errorf("Error: --uri option requires a value.")
					}
				} else if arg[len(option)] == '=' {
					uri = arg[len(option)+1:]
				} else {
					uri = arg[len(option):]
				}
			}
		}
		return uri, nil
	}
	//return "", input
}
