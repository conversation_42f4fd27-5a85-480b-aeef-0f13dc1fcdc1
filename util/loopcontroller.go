package util

import (
	"sync"
)

const (
	// 表示鉴定器刚完成重启操作，此时进入启动状态，待collector可以Ping通鉴定器时，启动的准备工作完成，将进入IDLE状态
	Startup = 0
	// 表示鉴定器现在处于一个空闲的状态，可以做扫描，升级，加载任意操作
	IDLE = 1
	// 表示即将调用鉴定器升级接口，在该状态下不应该再继续的FetchTask，并且应该将之前已经领取的任务全部处理掉
	UPDATE_READY = 2
	// 表示鉴定器正处于升级状态，该状态下次不应该再进行FetchTask
	UPDATING = 3
	// 当调用了Exit接口后，鉴定器将重启，并将状态置为：Terminating
	// 重启完成后，将由Terminating状态转换为：IDLE状态
	TERMINATING = 4
	// 主要正对于自升级时，在NewScan是，自升级会先进行一次升级操作，此时状态置为FIRST_UPDATE
	// 这个状态下，那条异步升级的线此时其实是不必做升级操作的
	FIRST_UPDATE = 5
)

type LoopController struct {
	sync.WaitGroup
	Stop bool
}

func (c *LoopController) Finalize() {
	c.Stop = true
	c.Wait()
}

func (c *LoopController) Run(cb func()) {
	c.Add(1)
	go func() {
		cb()
		c.Done()
	}()
}
