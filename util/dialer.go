//go:generate mockgen -source=dialer.go -destination=../mock/dialer_mock.go -package=mock
package util

import (
	"context"
	"fmt"
	"net"
	"os"
	"sync"
	"sync/atomic"
	"time"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/config"
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/credentials"
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/dialer"
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	kamala_api_model "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/model.git/v2/api"
	scan_api "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git/api"
	kamala_api_vloader "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/vloader.git/api"
	vloaderSdk "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/vloader.git/sdk"
	worker_api "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/worker.git/api"
	storage_sdk "git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type Dialer interface {
	FetchStorageSDK(ctx context.Context) (*storage_sdk.StorageFile, error)
	// FetchScanLogClient(ctx context.Context) (morpheus_api.ScanLogClient, error)
	FetchTaskClient(ctx context.Context) (worker_api.TaskClient, error)
	FetchRelayClient(ctx context.Context) (worker_api.TaskClient, error)
	FetchAssetClient(ctx context.Context) (worker_api.AssetClient, error)
	FetchScannerClient(ctx context.Context) (scan_api.ScanApiClient, error)
	FetchVloaderClient(ctx context.Context, asset string) (kamala_api_vloader.VirusLibraryClient, error)
	FetchVersionClient(ctx context.Context) (kamala_api_model.VersionClient, error)
}

var (
	instance    Dialer
	initialized uint32
	m           sync.Mutex
)

func GetDialer() Dialer {
	if atomic.LoadUint32(&initialized) == 0 {
		_ = SetDefaultDialer(NewGrpcDialer())
	}
	return instance
}

func SetDefaultDialer(dialer Dialer) error {
	if atomic.LoadUint32(&initialized) == 1 {
		return status.Errorf(codes.AlreadyExists, "dialer already set, instance: %v", instance)
	}
	m.Lock()
	defer m.Unlock()
	if initialized == 0 {
		defer atomic.StoreUint32(&initialized, 1)
		instance = dialer
	}
	return nil
}

func NewGrpcDialer() Dialer {
	return &GRPCDialer{}
}

type GRPCDialer struct {
	StorageSDK *storage_sdk.StorageFile
	// ScanLogClient morpheus_api.ScanLogClient
	TaskClient    worker_api.TaskClient
	AssetClient   worker_api.AssetClient
	RelayClient   worker_api.TaskClient
	ScanClient    scan_api.ScanApiClient
	VloaderClient kamala_api_vloader.VirusLibraryClient
	VersonClient  kamala_api_model.VersionClient
}

func (g *GRPCDialer) FetchStorageSDK(ctx context.Context) (*storage_sdk.StorageFile, error) {
	name := os.Getenv("MORPHEUS_API_STORAGE")

	client, err := storage_sdk.NewStorageFile(ctx, name, time.Second*10)
	if err != nil {
		log.WithContext(ctx).Warnf("failed to fetch storage sdk, name: %v, err: %v", name, err)
	}
	return client, err
}

// func (g *GRPCDialer) FetchScanLogClient(ctx context.Context) (morpheus_api.ScanLogClient, error) {
// 	name := os.Getenv("MORPHEUS_API_SCANLOG")

// 	conn := dialer.CreateClientConnection(dialer.WithTarget(name))

// 	return morpheus_api.NewScanLogClient(conn), nil
// }

func (g *GRPCDialer) FetchTaskClient(ctx context.Context) (worker_api.TaskClient, error) {
	if os.Getenv("EDGE_TYPE") == "edge2" {
		name := os.Getenv("KAMALA_API_TASK")
		_ = dialer.RegisterDialOption("DialWorkerTask", dialer.DialWithRegistry(nil),
			dialer.DialWithCredentialsOption(credentials.WithInsecureDial(false),
				credentials.WithInsecureSkipVerify(true)))

		conn := dialer.CreateClientConnection(dialer.WithTarget(name), dialer.WithDialOptTag("DialWorkerTask"))

		return worker_api.NewTaskClient(conn), nil
	}
	name := os.Getenv("KAMALA_API_TASK")

	conn := dialer.CreateClientConnection(dialer.WithTarget(name))

	return worker_api.NewTaskClient(conn), nil
}

func (g *GRPCDialer) FetchAssetClient(ctx context.Context) (worker_api.AssetClient, error) {
	if os.Getenv("EDGE_TYPE") == "edge2" {
		name := os.Getenv("KAMALA_API_ASSET")
		_ = dialer.RegisterDialOption("DialWorkerAsset", dialer.DialWithRegistry(nil),
			dialer.DialWithCredentialsOption(credentials.WithInsecureDial(false),
				credentials.WithInsecureSkipVerify(true)))

		conn := dialer.CreateClientConnection(dialer.WithTarget(name), dialer.WithDialOptTag("DialWorkerAsset"))

		return worker_api.NewAssetClient(conn), nil
	}

	name := os.Getenv("KAMALA_API_ASSET")

	conn := dialer.CreateClientConnection(dialer.WithTarget(name))

	return worker_api.NewAssetClient(conn), nil
}

func (g *GRPCDialer) FetchScannerClient(ctx context.Context) (scan_api.ScanApiClient, error) {
	name := config.String("SCAN_API")

	_ = dialer.RegisterDialOption("DirectFetchScanner", dialer.DialWithRegistry(nil))
	conn := dialer.CreateClientConnection(dialer.WithTarget(name), dialer.WithDialOptTag("DirectFetchScanner"))

	return scan_api.NewScanApiClient(conn), nil
}

func (g *GRPCDialer) FetchRelayClient(ctx context.Context) (worker_api.TaskClient, error) {
	hostIp := os.Getenv("HOST_ADDR")

	hostIpn := net.ParseIP(hostIp)
	if hostIpn == nil {
		log.Errorf("parse ip err, ip:%v", hostIp)
		return nil, fmt.Errorf("parse ip err, ip:%v", hostIp)
	}

	addr := fmt.Sprintf("%v%v", hostIp, os.Getenv("KAMALA_RELAY_API"))

	if hostIpn.To16() != nil {
		addr = fmt.Sprintf("[%v]%v", hostIp, os.Getenv("KAMALA_RELAY_API"))
	}

	_ = dialer.RegisterDialOption("DirectFetchRelay", dialer.DialWithRegistry(nil))
	conn := dialer.CreateClientConnection(dialer.WithTarget(addr), dialer.WithDialOptTag("DirectFetchRelay"))

	return worker_api.NewTaskClient(conn), nil
}

// 获取vloader服务的client
func (g *GRPCDialer) FetchVloaderClient(ctx context.Context, asset string) (kamala_api_vloader.VirusLibraryClient, error) {
	svc := os.Getenv("KAMALA_VLOADER_SVC_NAME")
	return vloaderSdk.NewVloaderSdk(svc, asset)
}

// 获取version服务的client
func (g *GRPCDialer) FetchVersionClient(ctx context.Context) (kamala_api_model.VersionClient, error) {
	name := os.Getenv("KAMALA_API_ASSET")

	_ = dialer.RegisterDialOption("DirectFetchVerson", dialer.DialWithRegistry(nil))
	conn := dialer.CreateClientConnection(dialer.WithTarget(name))

	return kamala_api_model.NewVersionClient(conn), nil
}
