package util

import (
	"errors"
	"fmt" // Add for rounding floats
	"math"
	"os"
	"testing"
	"time"

	kamala "git-biz.qianxin-inc.cn/zion-infra/kamala/api.git"
	entities "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/entities.git"
	api_scan "git-biz.qianxin-inc.cn/zion-infra/kamala/apis/scan.git"

	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func TestGetDownloadLadderTime(t *testing.T) {
	tests := []struct {
		name     string
		fileSize uint64
		expected time.Duration
	}{
		{"Small file <=30MB", 20 * MB, 60 * time.Second},
		{"Medium file 30MB-240MB", 100 * MB, time.Duration(int64(math.Trunc((100.0/100)*1.2+60))) * time.Second},
		{"Medium file 240MB-1GB", 500 * MB, time.Duration(int64(math.Trunc((500.0/100)*1.3+90))) * time.Second},
		{"Large file 1GB-5GB", 2 * GB, time.Duration(int64(math.Trunc((2048.0/100)*1.4+120))) * time.Second},
		{"Large file 5GB-10GB", 6 * GB, time.Duration(int64(math.Trunc((6144.0/100)*1.5+180))) * time.Second},
		{"XLarge file 10GB-50GB", 20 * GB, time.Duration(int64(math.Trunc((20480.0/100)*1.6+300))) * time.Second},
		{"XXLarge file >50GB", 60 * GB, 7200 * time.Second}, // Upper limit
		{"Unknown size (1)", 1, 1800 * time.Second},
		// Boundary cases
		{"Exactly 30MB", 30 * MB, 60 * time.Second}, // Still default
		{"Exactly 1GB", 1 * GB, time.Duration(int64(math.Trunc((1024.0/100)*1.3+90))) * time.Second},
		{"Exactly 50GB", 50 * GB, time.Duration(int64(math.Trunc((51200.0/100)*1.6+300))) * time.Second},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetDownloadLadderTime(tt.fileSize) * time.Second
			if got != tt.expected {
				t.Errorf("%s: GetDownloadLadderTime(%d) = %v, want %v", tt.name, tt.fileSize, got, tt.expected)
			}
		})
	}
}

func TestGetUploadLadderTime(t *testing.T) {
	type args struct {
		fileSize uint64
	}
	tests := []struct {
		name string
		args args
		want time.Duration
	}{
		{
			name: "test1",
			args: args{
				fileSize: 10 * MB,
			},
			want: time.Duration(5),
		},
		{
			name: "test2",
			args: args{
				fileSize: 20 * MB,
			},
			want: time.Duration(5),
		},
		{
			name: "test3",
			args: args{
				fileSize: 25 * MB,
			},
			want: time.Duration(25 / 2),
		},
		{
			name: "test4",
			args: args{
				fileSize: 30 * MB,
			},
			want: time.Duration(15),
		},
		{
			name: "test5",
			args: args{
				fileSize: 40 * MB,
			},
			want: time.Duration(20),
		},
		{
			name: "test6",
			args: args{
				fileSize: 50 * MB,
			},
			want: time.Duration(25),
		},
		{
			name: "test7",
			args: args{
				fileSize: 60 * MB,
			},
			want: time.Duration(120),
		},
		{
			name: "test8",
			args: args{
				fileSize: 80 * MB,
			},
			want: time.Duration(160),
		},
		{
			name: "test9",
			args: args{
				fileSize: 240 * MB,
			},
			want: time.Duration(480),
		},
		{
			name: "test10",
			args: args{
				fileSize: 250 * MB,
			},
			want: time.Duration(240),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetUploadLadderTime(tt.args.fileSize); got != tt.want {
				t.Errorf("GetUploadLadderTime() = %v, want %v", got, tt.want)
			}

		})
	}
}

func TestKamalaVersionToEntity(t *testing.T) {

	var engineVersion string = "20210203200916"
	var patternVersion string = "20210203200916"
	tests := []struct {
		ver  *api_scan.Version
		want *entities.Version
	}{
		{
			ver: &api_scan.Version{
				EngineVersion:  engineVersion,
				PatternVersion: patternVersion,
			},
			want: &entities.Version{
				Engine:     engineVersion,
				Pattern:    patternVersion,
				UpdateTime: time.Now().UnixNano(),
			},
		},
		{
			ver:  nil,
			want: &entities.Version{},
		},
	}

	for _, test := range tests {
		res := KamalaVersionToEntity(test.ver)
		if test.ver == nil {
			assert.Equal(t, "", res.Engine, "从api_scan.Version: %v创建entities.Version: %v出错", "", "")
			assert.Equal(t, "", res.Pattern, "从api_scan.Version: %v创建entities.Version: %v出错", "", "")
		} else {
			assert.Equal(t, test.want.Engine, res.Engine, "从api_scan.Version: %v创建entities.Version: %v出错", *&test.ver.EngineVersion, *&test.want.Engine)
			assert.Equal(t, test.want.Pattern, res.Pattern, "从api_scan.Version: %v创建entities.Version: %v出错", *&test.ver.PatternVersion, *&test.want.Pattern)

		}
	}

}

func TestTimestampToNano(t *testing.T) {
	var x *timestamp.Timestamp = timestamppb.Now()

	tests := []struct {
		val  *timestamp.Timestamp
		want int64
	}{
		{
			val:  x,
			want: x.GetSeconds()*int64(time.Second) + int64(x.GetNanos()),
		},
		{
			val:  nil,
			want: 0,
		},
	}

	for _, test := range tests {
		res := TimestampToNano(test.val)
		if test.val == nil {
			if res != test.want {
				t.Error("protobuf时间戳到nano传入nil时，没有返回0")
			}
		}

		assert.Equal(t, test.want, res, "protobuf时间戳到nano计算出错")
	}
}
func TestKamalaKindToEntity(t *testing.T) {
	tests := []struct {
		val  kamala.VersionKind
		want entities.VersionKind
	}{
		{
			val:  kamala.VersionKind(1),
			want: entities.VersionKind(1),
		},
		{
			val:  kamala.VersionKind(0),
			want: entities.VersionKind(0),
		},
	}

	for _, test := range tests {
		res := KamalaKindToEntity(test.val)
		assert.Equal(t, test.want, res, "kamala.VersionKind到entities.VersionKind转换出错")
	}

}

func Test_GetStorageSize(t *testing.T) {
	tests := []struct {
		uri  string
		err  error
		want int64
	}{
		{
			uri:  "",
			err:  errors.New("test"),
			want: 1,
		},
		{
			uri:  "relay",
			want: 60,
		},
	}

	for _, test := range tests {

		res, _ := GetStorageSize(test.uri)

		assert.Equal(t, test.want, res)

	}
}

func Test_IsSelfUpdate(t *testing.T) {
	asset := entities.Asset{
		UpdateFeature: &entities.UpdateFeature{
			Kind: entities.UpgradeKind_SELF,
		},
	}

	res := IsSelfUpdate(&asset)
	assert.Equal(t, true, res)
	res = IsUniteUpdate(&asset)
	assert.Equal(t, false, res)

	asset = entities.Asset{
		UpdateFeature: &entities.UpdateFeature{
			Kind: entities.UpgradeKind_DARWIN,
		},
	}
	res = IsSelfUpdate(&asset)
	assert.Equal(t, false, res)
	res = IsUniteUpdate(&asset)
	assert.Equal(t, true, res)

}

func Test_TaskSubst(t *testing.T) {
	logFilePath := "logFilePath"
	defer os.RemoveAll(logFilePath)
	file, _ := os.Create(logFilePath)
	file.Close()

	task := entities.Task{
		Sha1:      "testSHA1",
		AssetName: "bd",
	}
	uri := TaskSubst(&task, "files/${FILESHA1}/logs/${ASSET}/${LOGSHA1}/storage", logFilePath)

	fmt.Println(uri)
}

// func TestGetStorageSize(t *testing.T) {
// 	defer framework.Init()()
// 	err := os.Setenv("MORPHEUS_API_STORAGE", "10.252.16.107:30979")
// 	if err != nil {
// 		log.Fatalf("set morpheus env error: %v", err)
// 	}
// 	uri := "relay:9c10b53b23a9968d6a125b86298f6829fdd56f7b"
// 	size, _ := GetStorageSize(uri)
// 	assert.Equal(t, size, int64(1))

// 	uri = "files/94176b8c91331a06cb73c4589dc66ae504bcb975/storage"
// 	size, _ = GetStorageSize(uri)
// 	assert.Equal(t, size, int64(842967))

// 	uri = "files/3d10e56329639e0ebcfa3ac60f64e5804716aec4/storage"
// 	size, err = GetStorageSize(uri)
// 	assert.Equal(t, nil, err)
// 	assert.Equal(t, size, int64(1))

// 	uri = "message||files/94176b8c91331a06cb73c4589dc66ae504bcb975/storage"
// 	size, _ = GetStorageSize(uri)
// 	assert.Equal(t, size, int64(60))

// 	uri = "message"
// 	size, _ = GetStorageSize(uri)
// 	assert.Equal(t, size, int64(60))

// }
