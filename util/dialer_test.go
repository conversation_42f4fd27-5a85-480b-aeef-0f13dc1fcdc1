package util

import (
	"context"
	"os"
	"testing"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/config"
	"github.com/agiledragon/gomonkey/v2"
)

func Test_Fetch(t *testing.T) {
	guard := gomonkey.ApplyFunc(os.Getenv, func(s string) string {
		return ""
	})
	defer guard.Reset()

	guard = gomonkey.ApplyFunc(config.String, func(s string) string {
		return ""
	})
	defer guard.Reset()

	g := NewGrpcDialer()
	g.FetchStorageSDK(context.Background())
	// g.FetchScanLogClient(context.Background())
	g.FetchTaskClient(context.Background())
	g.FetchAssetClient(context.Background())
	g.FetchScannerClient(context.Background())
	g.FetchRelayClient(context.Background())
	g.FetchVloaderClient(context.Background(), "bd")
	g.Fetch<PERSON>ion<PERSON>lient(context.Background())
}

func Test_Fetch_edge2(t *testing.T) {
	guard := gomonkey.ApplyFunc(os.Getenv, func(s string) string {
		return ""
	})
	defer guard.Reset()

	guard = gomonkey.ApplyFunc(config.String, func(s string) string {
		return ""
	})
	defer guard.Reset()

	gomonkey.ApplyFunc(os.Getenv, func(s string) string {
		if s == "EDGE_TYPE" {
			return "edge2"
		}
		if s == "HOST_ADDR" {
			return "127.0.0.1"
		}
		return ""
	})
	g := NewGrpcDialer()
	g.FetchStorageSDK(context.Background())
	// g.FetchScanLogClient(context.Background())
	g.FetchTaskClient(context.Background())
	g.FetchAssetClient(context.Background())
	g.FetchScannerClient(context.Background())
	g.FetchRelayClient(context.Background())
	g.FetchVloaderClient(context.Background(), "")
	g.FetchVersionClient(context.Background())
}
