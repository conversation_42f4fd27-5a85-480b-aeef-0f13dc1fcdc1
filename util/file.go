package util

import (
	"archive/zip"
	"bytes"
	"crypto/sha1"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	log2 "log"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"strings"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/storage/file/morpheus/sdk"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

const (
	ZipHeader = "\x50\x4b\x03\x04"
)

type CheckSha1Info struct {
	Sha1Same   bool
	Size       int64
	ActualSha1 string
}

type DownloadInput struct {
	Uri      string
	Url      string
	Size     uint64
	Sha1     string
	FilePath string
}

// 保存exec执行后的回显
type ExecResponse struct {
	cmd    string
	stdOut string
	stdErr string
}

func CheckFileIsExist(filename string) bool {
	exist := true
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		exist = false
	}
	return exist
}

func SaveFile(content []byte, path string) error {
	var f *os.File
	var err error
	dir := filepath.Dir(path)
	if !CheckFileIsExist(dir) {
		_ = os.MkdirAll(dir, os.ModePerm)
	}

	if CheckFileIsExist(path) { // 如果文件存在
		f, err = os.OpenFile(path, os.O_WRONLY, 0o666) // 打开文件
	} else {
		f, err = os.Create(path) // 创建文件
	}
	if err != nil {
		return status.Errorf(codes.Internal, "failed to open file,err: %v", err)
	}

	_, err = io.WriteString(f, string(content)) // 写入文件(字符串)
	if err != nil {
		_ = RemoveFile(path) // 下载失败后，确保文件不存在，防止文件泄露
		return status.Errorf(codes.Internal, "failed to save file data, err: %v", err)
	}
	defer f.Close()
	return err
}

func StreamSave(in io.ReadCloser, path string) (int64, error) {
	var f *os.File
	var err error
	var n int64
	dir := filepath.Dir(path)
	if !CheckFileIsExist(dir) {
		os.MkdirAll(dir, os.ModePerm)
	}

	if CheckFileIsExist(path) { // 如果文件存在
		f, err = os.OpenFile(path, os.O_WRONLY, 0o666) // 打开文件
	} else {
		f, err = os.Create(path) // 创建文件
	}
	defer f.Close()
	n, err = io.Copy(f, in)
	if err != nil || n < 0 {
		return n, status.Errorf(codes.Internal, "stream save failed, save size: %v, error: %v", n, err)
	}
	return n, nil
}

func LoadFile(path string) ([]byte, error) {
	if !CheckFileIsExist(path) {
		return nil, status.Errorf(codes.NotFound, "not found path: %v", path)
	}
	content, err := ioutil.ReadFile(path)
	if err != nil {
		return nil, status.Errorf(codes.Unknown, "failed to read path: %v", path)
	}
	return content, nil
}

func StreamLoadFile(path string) (io.ReadCloser, error) {
	if !CheckFileIsExist(path) {
		return nil, status.Errorf(codes.NotFound, "not found path: %v", path)
	}
	reader, err := os.Open(path)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "open file: %v failed: %v", path, err)
	}
	return reader, nil
}

func RemoveFile(path string) error {
	if path != "" && CheckFileIsExist(path) {
		return os.Remove(path)
	}
	return nil
}

func RemoveDir(dir string) error {
	if dir != "" && CheckFileIsExist(dir) {
		return os.RemoveAll(dir)
	}
	return nil
}

func Unzip(content []byte) ([]byte, error) {
	oneZip, err := zip.NewReader(bytes.NewReader(content), int64(len(content)))
	if err != nil {
		return nil, status.Errorf(codes.Internal, "download file is zip, []byte to zipFile error:%v", err)
	}
	if len(oneZip.File) < 1 {
		return nil, status.Errorf(codes.InvalidArgument, "download file is zip, but it's empty")
	}
	unzipFileBytes, err := ReadZip(oneZip.File[0])
	if err != nil {
		return nil, err
	}
	return unzipFileBytes, nil
}

// 只会将压缩包中的那个二进制文件解压出来
func UnzipFile(src, dest string) error {
	reader, err := zip.OpenReader(src)
	if err != nil {
		return err
	}
	defer reader.Close()
	if len(reader.File) < 1 {
		return errors.New(fmt.Sprintf("download file is zip, []byte to zipFile error:%v", err))
	}
	fileReader, err := reader.File[0].Open()
	if err != nil {
		return err
	}
	defer fileReader.Close()

	// reader.File[0].Mode 权限有问题，这里权限直接写死
	targetFile, err := os.OpenFile(dest, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0o755)
	if err != nil {
		return err
	}
	defer targetFile.Close()

	if _, err := io.Copy(targetFile, fileReader); err != nil {
		return err
	}

	return nil
}

func GetDirSize(path string) (uint64, error) {
	var size uint64
	if !CheckFileIsExist(path) {
		return 0, errors.New(fmt.Sprintf("the path: %v not exist", path))
	}
	errInfo := filepath.Walk(path, func(_ string, info os.FileInfo, err error) error {
		if info != nil && !info.IsDir() {
			size += uint64(info.Size())
		}
		if info == nil && strings.Contains(err.Error(), "no such file") {
			log2.Printf("debug: 计算目录：%v 时，info为空, err: %v", path, err)
			return nil
		}
		return err
	})
	if errInfo != nil {
		log2.Printf("debug: 目录：%v，errInfo: %v", path, errInfo)
	}
	return size, errInfo
}

func ReadZip(zf *zip.File) ([]byte, error) {
	file, err := zf.Open()
	defer file.Close()
	if err != nil {
		return nil, status.Errorf(codes.Internal, "open zipFile error:%v", err)
	}
	return ioutil.ReadAll(file)
}

func CalcSha1(content []byte) string {
	sha1Helper := sha1.New()
	sha1Helper.Write(content)
	result := fmt.Sprintf("%x", sha1Helper.Sum(nil))
	return result
}

// 根据文件流，流式的计算sha1减少内存的消耗
func Sha1Sum(f io.Reader) (string, error) {
	h := sha1.New()
	if _, err := io.Copy(h, f); err != nil {
		return "", err
	}
	return hex.EncodeToString(h.Sum(nil)), nil
}

func IsZip(r io.Reader) (bool, []byte) {
	buf := make([]byte, 4)
	n, err := r.Read(buf)
	if err != nil || n != 4 {
		return false, buf[:n]
	}
	return bytes.Equal(buf[0:4], []byte(ZipHeader)), buf[0:4]
}

// 复合计算sha1是因为，在拿到文件流时需要预先读取前4个字节来判断该文件流是不是一个压缩包
// 但此时文件流的前4个字节已经被读取走了，计算sha1之前需要将这最开始读取走的4个字节先灌进去，
// 然后再根据文件流去灌计算出来的sha1，才是正确的；
// 例如：有个文件流Body，size为：204，读取这个文件流的前4个字节判断文件是不是压缩包，此时
// 文件流的size只有200了，但是完整的sha1需要将204字节全部灌进去才是准确的，所以IsZip判断
// 是不是sha1时将前四个字节返回，交由sha1计算函数，先把前4个字节灌进去来保证sha1算的正确性
func ComplexSha1Sum(f io.Reader, buf []byte) (int64, string, error) {
	h := sha1.New()
	var wn int
	var cn int64
	var err error
	if wn, err = h.Write(buf); err != nil {
		return int64(wn), "", err
	}
	if cn, err = io.Copy(h, f); err != nil {
		return cn + int64(wn), "", err
	}

	return cn + int64(wn), fmt.Sprintf("%x", h.Sum(nil)), nil
}

// 拷贝文件
func CopyFile(src, dst string) error {
	var err error
	var srcFileData *os.File
	var dstFileData *os.File
	var srcInfo os.FileInfo
	if srcFileData, err = os.Open(src); err != nil {
		return err
	}
	defer srcFileData.Close()
	if dstFileData, err = os.Create(dst); err != nil {
		return err
	}
	defer dstFileData.Close()
	if _, err = io.Copy(dstFileData, srcFileData); err != nil {
		return err
	}
	if srcInfo, err = os.Stat(src); err != nil {
		return err
	}
	return os.Chmod(dst, srcInfo.Mode())
}

// 拷贝目录
func CopyDir(src, dst string) error {
	var err error
	var fdInfos []os.FileInfo
	var srcInfo os.FileInfo
	if srcInfo, err = os.Stat(src); err != nil {
		return err
	}
	if err = os.MkdirAll(dst, srcInfo.Mode()); err != nil {
		return err
	}
	if fdInfos, err = ioutil.ReadDir(src); err != nil {
		return err
	}
	for _, fd := range fdInfos {
		srcFilePath := path.Join(src, fd.Name())
		dstFilePath := path.Join(dst, fd.Name())
		if fd.IsDir() {
			if err = CopyDir(srcFilePath, dstFilePath); err != nil {
				log.Warn(err)
			}
		} else {
			if err = CopyFile(srcFilePath, dstFilePath); err != nil {
				log.Warn(err)
			}
		}
	}
	return nil
}

// 检测sha1一致性
func CheckSha1Consistency(in io.ReadCloser, savePath, expectSha1 string, buf []byte) (*CheckSha1Info, error) {
	sha1Info := &CheckSha1Info{}
	f, err := os.OpenFile(savePath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0o666)
	if err != nil {
		sha1Info.Sha1Same = false
		return sha1Info, errors.New(fmt.Sprintf("open file: %v failed: %v", savePath, err))
	}
	_, err = f.Write(buf)
	if err != nil {
		sha1Info.Sha1Same = false
		return sha1Info, errors.New(fmt.Sprintf("write file: %v failed: %v", savePath, err))
	}

	tee := io.TeeReader(in, f)
	size, actualSha1, err := ComplexSha1Sum(tee, buf)
	sha1Info.ActualSha1 = actualSha1
	sha1Info.Size = size
	f.Close()
	if err != nil {
		sha1Info.Sha1Same = false
		return sha1Info, errors.New(fmt.Sprintf("calc sha1 failed: %v", err))
	}
	if actualSha1 != expectSha1 {
		log.Debugf("actual sha1: %v not equal expect sha1: %v", sha1Info.ActualSha1, expectSha1)
		return sha1Info, status.Errorf(codes.InvalidArgument, "actual sha1: %v not equal expect sha1: %v", sha1Info.ActualSha1, expectSha1)
	}
	sha1Info.Sha1Same = true
	return sha1Info, nil
}

func CheckSha1AndSave(downloadInfo *sdk.DownloadInfo, in *DownloadInput) error {
	// 一定是先校验sha1的一致性，不管是不是压缩包，当检验结果为sha1不一致的时候才触发解压校验，
	// 解压校验结果为sha1不一致则是真正的sha1不一致
	var sha1Info *CheckSha1Info
	var err error
	var f io.ReadCloser
	isZip, buf := IsZip(downloadInfo.Body)
	sha1Info, err = CheckSha1Consistency(downloadInfo.Body, in.FilePath, in.Sha1, buf)
	// 当返回错误码为：InvalidArgument时，说明sha1直接检查下载的样本时sha1不一致，此时sha1不一致并且是个压缩包，那么解压检验sha1
	if status.Code(err) == codes.InvalidArgument && !sha1Info.Sha1Same && isZip {
		f, err = os.Open(in.FilePath)
		if err == nil {
			sha1Info, err = CheckZipSha1Consistency(f, in.FilePath, in.Sha1)
		}
		if f != nil {
			_ = f.Close()
		}
	}

	if sha1Info != nil {
		downloadInfo.Length = uint64(sha1Info.Size)
		downloadInfo.Sha1Same = sha1Info.Sha1Same
		downloadInfo.ActualSha1 = sha1Info.ActualSha1
	}
	if err != nil {
		_ = RemoveFile(in.FilePath)
		_ = RemoveFile(fmt.Sprintf("%v.zip", in.FilePath))
		return err
	}
	return nil
}

// 针对样本是压缩包的情况下，首先是先将原始的压缩包落盘，然后再解压到上游给定的存储路径，
// 再通过读文件的方式流式的计算sha1，若是sha1不一致则将原始压缩包和解压后的文件全部删除
func CheckZipSha1Consistency(in io.ReadCloser, savePath, expectSha1 string) (*CheckSha1Info, error) {
	sha1Info := &CheckSha1Info{}
	// 压缩包为路径名
	zipPath := fmt.Sprintf("%s.zip", savePath)
	h := sha1.New()
	zipFile, err := os.OpenFile(zipPath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0o666)
	if err != nil {
		return sha1Info, errors.New(fmt.Sprintf("open file: %v failed: %v", zipPath, err))
	}
	_, err = io.Copy(zipFile, in)
	if err != nil {
		return sha1Info, errors.New(fmt.Sprintf("io copy file: %v failed: %v", zipPath, err))
	}
	zipFile.Close()

	err = UnzipFile(zipPath, savePath)
	if err != nil {
		return sha1Info, errors.New(fmt.Sprintf("unzip file: %v failed: %v", savePath, err))
	}
	err = RemoveFile(zipPath)
	if err != nil {
		return sha1Info, errors.New(fmt.Sprintf("remove file: %v failed: %v", zipPath, err))
	}
	unzipFile, err := os.Open(savePath)
	if err != nil {
		return sha1Info, errors.New(fmt.Sprintf("open file:%v failed: %v", savePath, err))
	}
	unzipN, err := io.Copy(h, unzipFile)
	unzipFile.Close()
	if err != nil {
		return sha1Info, errors.New(fmt.Sprintf("io copy file: %v failed: %v", savePath, err))
	}
	sha1Info.ActualSha1 = fmt.Sprintf("%x", h.Sum(nil))
	sha1Info.Size = unzipN
	if sha1Info.ActualSha1 != expectSha1 {
		err = RemoveFile(savePath)
		if err != nil {
			log.Fatalf("remove file: %v failed: %v", savePath, err)
		}
		return sha1Info, status.Errorf(codes.InvalidArgument, "actual sha1: %v not equal expect sha1: %v", sha1Info.ActualSha1, expectSha1)
	}

	sha1Info.Sha1Same = true
	return sha1Info, nil
}

// 执行shell命令并返回命令stdOut, stdErr
func ExecCmdGetResponse(cmdStr string) (ExecResponse, error) {
	cmd := exec.Command("bash", "-c", cmdStr)
	var outBuf, errBuf bytes.Buffer
	cmd.Stdout = &outBuf
	cmd.Stderr = &errBuf
	if err := cmd.Run(); err != nil {
		return ExecResponse{cmdStr, string(outBuf.Bytes()), string(errBuf.Bytes())},
			status.Errorf(codes.Unavailable, "The command:%s is err, %s", cmdStr, err)
	}
	return ExecResponse{cmdStr, string(outBuf.Bytes()), string(errBuf.Bytes())}, nil
}

func MoveFile(src, dest string) {
	if !CheckFileIsExist(src) {
		return
	}
	cmd := fmt.Sprintf("mv %v %v", src, dest)
	cmdRes, cmdErr := ExecCmdGetResponse(cmd)
	if cmdErr != nil {
		log.Errorf("[test debug]toDownload:执行mv指令失败：%v", cmdErr)
	}
	log.Debugf("[test debug]toDownload:mv指令执行结果：[cmd: %v], [stdOut: %v], [stdErr: %v]", cmdRes.cmd,
		cmdRes.stdOut, cmdRes.stdErr)
}
