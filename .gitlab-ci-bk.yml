variables:
  #GIT_SUBMODULE_STRATEGY: recursive
  CI_REGISTRY_IMAGE: harbor.ops.qianxin-inc.cn/zion/kamala-collector
  KAMALA_PROXY: http://static-proxy.g0.qianxin-inc.cn:3128
  # go build 生成的二进制文件名
  BINARY_NAME: kamala-collector
  # 镜像推送的darwin通道
  DARWIN_CHANNEL: kimage_collector
   # gitlab runnner tag
  RUNNER_TAG: k8s

stages:
  - build
  - test
  - image
  - push
  - sonar

.prepare-compile:
  image: harbor.ops.qianxin-inc.cn/library/golang:1.17.6
  before_script:
    - go env -w GO111MODULE=on
    - go env -w GOPROXY="https://af-biz.qianxin-inc.cn/artifactory/api/go/test-go-virtual,direct"
    - go env -w GOPRIVATE="*.qianxin-inc.cn"
    - go env -w GOSUMDB=off
    - go env -w GOMODCACHE=${PWD}/.modcache
    - git config --global url."***************************:".insteadOf "https://git-open.qianxin-inc.cn"
    - git config --global url."**************************:".insteadOf "https://git-biz.qianxin-inc.cn/"
    - mkdir -p ~/.ssh
    - mkdir -p ./.modcache
    - echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config
    # - echo "$GIT_SSH_KEY"
    - echo "$GIT_SSH_KEY" > ~/.ssh/id_rsa
    - chmod 0700 ~/.ssh/id_rsa
    - 'curl --location --header "JOB-TOKEN: $CI_JOB_TOKEN" "https://git-biz.qianxin-inc.cn/api/v4/projects/5791/jobs/artifacts/feature/darwin/raw/kamala-tool?job=gobuild" --output kamala-tool'
    - chmod +x kamala-tool
    - ./kamala-tool darwin generate -c $DARWIN_CHANNEL -o file://darwin.version

go build:
  stage: build
  extends:
    - .prepare-compile
  only:
    - tags
  script:
    - du -ha --max-depth=1
    - go mod tidy
    - go build -o $BINARY_NAME
    - du -ha --max-depth=1
    - ls -la
  cache:
    paths:
      - .modcache
  artifacts:
    paths:
      - ./$BINARY_NAME
      - ./darwin.version
    expire_in: 1h
  tags:
    - $RUNNER_TAG

go test:
  stage: test
  extends:
    - .prepare-compile
  only:
    - tags
  script:
    - du -ha --max-depth=1
    - ls -la
    - go mod tidy
    - echo "mock test"
  cache:
    paths:
      - .modcache
  tags:
    - $RUNNER_TAG

make image:
  stage: image
  only:
    - tags
  image:
    name: qianxin-image.af-biz.qianxin-inc.cn/kaniko-project/executor:v1.9.1-debug
    entrypoint: [""]
  script:
    # 加载推送工具 
    - DARWIN_VERSION=$(cat darwin.version)
    - echo $DARWIN_VERSION
    - mkdir -p /kaniko/.docker
    - echo "{\"auths\":{\"${CI_REGISTRY}\":{\"auth\":\"$(printf "%s:%s" "${CI_REGISTRY_USER}" "${CI_REGISTRY_PASSWORD}" | base64 | tr -d '\n')\"}}}" > /kaniko/.docker/config.json
    - >-
      /kaniko/executor
      --build-arg "all_proxy=${KAMALA_PROXY}"
      --build-arg "http_proxy=${KAMALA_PROXY}"
      --build-arg "https_proxy=${KAMALA_PROXY}"
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/Dockerfile"
      --tarPath "./image.tar"
      --destination "${CI_REGISTRY_IMAGE}:${DARWIN_VERSION}-${CI_BUILD_TAG}"
      --no-push
    - echo ${CI_REGISTRY_IMAGE}:${DARWIN_VERSION}-${CI_BUILD_TAG}
  artifacts:
    paths:
      - ./image.tar
      - ./kamala-tool
      - ./darwin.version
    expire_in: 1h
  tags:
    - $RUNNER_TAG
  
push darwin:
  stage: push
  extends:
    - .prepare-compile
  only:
    - tags
  script:
    - ENV=dev
    - echo $ENV
     # 重命名上一步生成的镜像文件
    - TIME=$(date +%s)
    - echo $TIME
    - mv ./image.tar ./image-$TIME.tar
    - DARWIN_VERSION=$(cat darwin.version)
    - export GOLANG_PROTOBUF_REGISTRATION_CONFLICT=warn 
    - echo "./kamala-tool darwin push -v $DARWIN_VERSION -e $ENV -c $DARWIN_CHANNEL -l tag=$CI_BUILD_TAG,name=$CI_REGISTRY_IMAGE collector:./image-$TIME.tar"
    # 推版本
    - ./kamala-tool darwin push -e $ENV -c $DARWIN_CHANNEL -l tag=$CI_BUILD_TAG,name=$CI_REGISTRY_IMAGE collector:./image-$TIME.tar
  tags:
    - $RUNNER_TAG

sonar scanner:
  stage: sonar
  only:
    - tags
  image: harbor.ops.qianxin-inc.cn/cicd/sonar-scanner-linux:jdk-8-maven-3.5.3-cli-3.2.0
  variables:
    SONAR_HOST_URL: "https://sonar-biz.qianxin-inc.cn/"
    SONAR_TOKEN: "****************************************"
    PROJECT_SERVICE_NAME: "kamala-collector"
  script:
    - sonar-scanner -X -Dsonar.go.coverage.reportPaths=./coverage.out -Dsonar.host.url=${SONAR_HOST_URL} -Dsonar.projectKey=${PROJECT_SERVICE_NAME} -Dsonar.projectName=${PROJECT_SERVICE_NAME}  -Dsonar.sources=./ -Dsonar.java.binaries=./ -Dsonar.login=${SONAR_TOKEN}
  tags:
    - $RUNNER_TAG
