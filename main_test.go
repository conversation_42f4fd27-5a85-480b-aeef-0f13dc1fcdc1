package main

import (
	"fmt"
	"testing"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	"git-biz.qianxin-inc.cn/zion-infra/kamala/warzone/collector.git/util"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/judwhite/go-svc/svc"
)

type MyService struct {
}

// Init is called before the program/service is started and after it's
// determined if the program is running as a Windows Service. This method must
// be non-blocking.
func (s *MyService) Init(svc.Environment) error {
	return nil
}

// Start is called after Init. This method must be non-blocking.
func (s *MyService) Start() error {
	return fmt.Errorf("The mock service cannot be started")
}

// Stop is called in response to syscall.SIGINT, syscall.SIGTERM, or when a
// Windows Service is stopped.
func (s *MyService) Stop() error {
	return nil
}

func TestMain(t *testing.T) {
	patches := gomonkey.ApplyFunc(NewScan, func(dialer util.Dialer, fileDir, plusFileDir, logDir string) svc.Service {
		return &MyService{}
	})
	defer patches.Reset()

	patches.ApplyFunc(log.Fatal, func(args ...interface{}) {
		log.Info(args...)
	})

	/*go func() {
		time.Sleep(10 * time.Millisecond)
		syscall.Kill(syscall.Getpid(), syscall.SIGINT)
	}()*/

	main()
}
